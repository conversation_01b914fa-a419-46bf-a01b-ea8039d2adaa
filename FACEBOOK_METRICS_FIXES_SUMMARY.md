# Facebook Metrics Collection System Fixes

## Overview
This document summarizes the comprehensive fixes implemented to resolve critical issues in the Facebook metrics collection system, including SQLAlchemy greenlet errors, Facebook Graph API error handling, database integrity constraint violations, and error counting problems.

## Issues Fixed

### 1. SQLAlchemy "MissingGreenlet" Errors ✅

**Problem**: The system was encountering "greenlet_spawn has not been called; can't call await_only() here" errors when accessing database model attributes like `account.page_id`, `account.username`, etc.

**Root Cause**: SQLAlchemy was trying to perform lazy loading of model attributes in async contexts where the greenlet wasn't properly initialized.

**Solution Implemented**:
- Added `await db.refresh(account)` calls to ensure accounts are properly loaded in the current session
- Pre-extracted account attributes at the beginning of functions to avoid repeated lazy loading
- Updated all growth trend functions to use the refresh pattern

**Files Modified**:
- `microservices/socials_service/app/tasks/facebook_metrics.py`
- `microservices/socials_service/app/services/facebook.py`

### 2. Facebook Graph API Error Handling ✅

**Problem**: The system was failing when encountering Facebook API errors like "Object with ID does not exist" (Error Code 100, Subcode 33) and not handling missing posts gracefully.

**Solution Implemented**:
- Enhanced `save_comments_to_db()` function with comprehensive Facebook API error handling
- Added specific handling for common Facebook error codes:
  - Code 100, Subcode 33: Object doesn't exist/missing permissions → Skip gracefully
  - Code 100: Invalid parameter → Skip gracefully  
  - Code 190, 102: Access token issues → Log as error
- Function now returns structured response indicating success, skip, or error status
- Improved error messages and logging for better debugging

**Files Modified**:
- `microservices/socials_service/app/services/facebook.py`

### 3. Database Integrity Constraint Violations ✅

**Problem**: The system was failing with duplicate key violations in the `facebook_growth_trends` table due to the unique constraint on `(organisation_id, trend_type, month)`.

**Solution Implemented**:
- Replaced simple `db.add()` calls with proper upsert logic
- Added checks for existing records before insertion
- If record exists: Update existing record with new values and timestamp
- If record doesn't exist: Create new record
- Applied this pattern to all growth trend functions:
  - `fetch_and_store_audience_growth_trend()`
  - `fetch_and_store_engagement_growth_trend()`
  - `fetch_and_store_reach_growth_trend()`
  - `fetch_and_store_click_rate_growth_trend()`

**Files Modified**:
- `microservices/socials_service/app/tasks/facebook_metrics.py`

### 4. Error Counting and Statistics Tracking ✅

**Problem**: The final statistics were incorrectly showing "Failed: 0" despite errors occurring, and the system wasn't properly categorizing different types of failures.

**Solution Implemented**:
- Enhanced error tracking with detailed categorization:
  - `account_errors`: Critical errors that cause account processing to fail
  - `account_warnings`: Non-critical issues like skipped posts
  - Separate counters for comments processed, skipped, and failed
- Improved statistics tracking logic:
  - Accounts are marked as failed only if they have critical errors
  - Warnings (like skipped posts) don't count as failures
  - Proper increment of `failed_accounts` counter
- Enhanced logging with detailed error reporting and summaries

**Files Modified**:
- `microservices/socials_service/app/tasks/facebook_metrics.py`

### 5. Improved Logging and Error Categorization ✅

**Solution Implemented**:
- Added structured logging that distinguishes between:
  - **Recoverable errors**: Missing posts, insufficient permissions (logged as warnings)
  - **Critical errors**: Database issues, unexpected exceptions (logged as errors)
- Detailed per-account summaries showing:
  - Number of comments processed, skipped, and failed
  - List of errors and warnings for each account
  - Clear indication of why an account failed or succeeded
- Better error messages with context (account name, post ID, error reason)

## Testing

A comprehensive test script was created (`test_facebook_metrics_fix.py`) that verifies:

1. **Account Attribute Access**: Ensures no greenlet errors when accessing model attributes
2. **Session Refresh Pattern**: Validates the refresh pattern works correctly
3. **Growth Trends Upsert Logic**: Tests that duplicate records are handled properly

## Key Benefits

1. **Reliability**: System now handles Facebook API errors gracefully without crashing
2. **Data Integrity**: No more constraint violations in growth trends table
3. **Accurate Reporting**: Error statistics now correctly reflect actual failures
4. **Better Debugging**: Enhanced logging makes it easier to identify and resolve issues
5. **Resilience**: Individual post failures don't stop the entire batch processing

## Usage Notes

- The system now continues processing even when individual posts fail
- Skipped posts (due to missing permissions or deleted content) are logged as warnings, not errors
- Growth trends are properly updated without creating duplicates
- Final statistics accurately reflect the processing results

## Files Modified Summary

1. `microservices/socials_service/app/tasks/facebook_metrics.py` - Main metrics collection logic
2. `microservices/socials_service/app/services/facebook.py` - Facebook API interaction and error handling
3. `test_facebook_metrics_fix.py` - Comprehensive test suite for validation

All fixes maintain backward compatibility and improve the overall robustness of the Facebook metrics collection system.
