import time
from datetime import datetime
from typing import Annotated, List, Optional

import dns.resolver
from app.utils.logger import get_logger
from email_validator import EmailNotValidError, validate_email
from pydantic import (
    BaseModel,
    ConfigDict,
    EmailStr,
    Field,
    StringConstraints,
    model_validator,
)

# Initialize the logger for this module
logger = get_logger(__name__)


def validate_mx_record(domain: str) -> bool:
    if not domain or not isinstance(domain, str):
        logger.error("Invalid domain input")
        raise ValueError("Invalid domain input")

    for attempt in range(3):
        try:
            mx_records = dns.resolver.resolve(domain, 'MX', lifetime=10.0)
            return bool(mx_records)
        except dns.resolver.Timeout:
            logger.warning(f"Timeout while resolving MX records for domain {domain}, retrying... (Attempt {attempt + 1}/3)")
            time.sleep(2 ** attempt)
        except dns.resolver.NoAnswer:
            logger.error(f"No MX record found for domain {domain}.")
            return False
        except dns.resolver.NXDOMAIN:
            logger.error(f"Domain {domain} does not exist (NXDOMAIN).")
            return False
        except Exception as e:
            logger.error(f"An error occurred while validating MX records for {domain}: {e}")
            return False

    logger.error(f"Failed to validate MX records for {domain} after multiple attempts.")
    return False


class UserBase(BaseModel):
    """Base user schema"""

    first_name: Optional[str] = ''
    last_name: Optional[str] = ''
    email: EmailStr


class UserCreate(UserBase):
    """Schema to create a user"""

    password: Annotated[
        str, StringConstraints(
            min_length=8,
            max_length=64,
            strip_whitespace=True
        ),
        Field(example="P@ssw0rd")
    ]
    first_name: Annotated[
        str, StringConstraints(
            min_length=3,
            max_length=30,
            strip_whitespace=True
        )
    ]
    last_name: Annotated[
        str, StringConstraints(
            min_length=3,
            max_length=30,
            strip_whitespace=True
        )
    ]
    # status: Optional[str] = 'invited'

    @model_validator(mode='before')
    @classmethod
    def validate_password(cls, values: dict):
        """
        Validates passwords
        """
        password = values.get('password')
        email = values.get("email")

        # constraints for password
        if not any(c.islower() for c in password):
            raise ValueError("password must include at least one lowercase character")
        if not any(c.isupper() for c in password):
            raise ValueError("password must include at least one uppercase character")
        if not any(c.isdigit() for c in password):
            raise ValueError("password must include at least one digit")

        try:
            email = validate_email(email, check_deliverability=True)
            if email.domain.count(".com") > 1:
                raise EmailNotValidError("Email address contains multiple '.com' endings.")
            if not validate_mx_record(email.domain):
                raise ValueError('Email is invalid')
        except EmailNotValidError as exc:
            raise ValueError(exc) from exc
        except Exception as exc:
            raise ValueError(exc) from exc

        return values


class UserUpdate(BaseModel):

    first_name: Optional[str] = None
    last_name: Optional[str] = None


class UserResponse(UserBase):
    id: str
    is_active: bool
    is_verified: bool
    is_onboarded: bool
    status: str

    model_config = ConfigDict(from_attributes=True)


class LoginRequest(BaseModel):
    email: EmailStr
    password: str

    @model_validator(mode='before')
    @classmethod
    def validate_password(cls, values: dict):
        """
        Validates passwords
        """
        password = values.get('password')
        email = values.get("email")

        # constraints for password
        if not any(c.islower() for c in password):
            raise ValueError("password must include at least one lowercase character")
        if not any(c.isupper() for c in password):
            raise ValueError("password must include at least one uppercase character")
        if not any(c.isdigit() for c in password):
            raise ValueError("password must include at least one digit")

        try:
            email = validate_email(email, check_deliverability=True)
            if email.domain.count(".com") > 1:
                raise EmailNotValidError("Email address contains multiple '.com' endings.")
            if not validate_mx_record(email.domain):
                raise ValueError('Email is invalid')
        except EmailNotValidError as exc:
            raise ValueError(exc) from exc
        except Exception as exc:
            raise ValueError(exc) from exc

        return values


class InvitedPasswordSchema(BaseModel):
    """Schema for setting password of an invited user"""

    new_password: Annotated[
        Optional[str],
        StringConstraints(min_length=8,
                          max_length=64,
                          strip_whitespace=True)
    ]

    confirm_new_password: Annotated[
        Optional[str],
        StringConstraints(min_length=8,
                          max_length=64,
                          strip_whitespace=True)
    ]

    @model_validator(mode='before')
    @classmethod
    def validate_password(cls, values: dict):
        """
        Validates passwords
        """
        new_password = values.get('new_password')
        confirm_new_password = values.get("confirm_new_password")

        if new_password and confirm_new_password:
            # constraints for new_password
            if not any(c.islower() for c in new_password):
                raise ValueError("New password must include at least one lowercase character")
            if not any(c.isupper() for c in new_password):
                raise ValueError("New password must include at least one uppercase character")
            if not any(c.isdigit() for c in new_password):
                raise ValueError("New password must include at least one digit")

            if confirm_new_password != new_password:
                raise ValueError("New Password and Confirm New Password must match")

        return values


class ChangePasswordSchema(BaseModel):
    """Schema for changing password of a user"""

    old_password: Annotated[
        Optional[str],
        StringConstraints(min_length=8,
                          max_length=64,
                          strip_whitespace=True)
    ] = None

    new_password: Annotated[
        str,
        StringConstraints(min_length=8,
                          max_length=64,
                          strip_whitespace=True)
    ]

    confirm_new_password: Annotated[
        str,
        StringConstraints(min_length=8,
                          max_length=64,
                          strip_whitespace=True)
    ]

    @model_validator(mode='before')
    @classmethod
    def validate_password(cls, values: dict):
        """
        Validates passwords
        """
        old_password = values.get('old_password')
        new_password = values.get('new_password')
        confirm_new_password = values.get("confirm_new_password")

        if (old_password and old_password.strip() == '') or old_password == '':
            values['old_password'] = None
        # constraints for old_password
        if old_password and old_password.strip():
            if not any(c.islower() for c in old_password):
                raise ValueError("Old password must include at least one lowercase character")
            if not any(c.isupper() for c in old_password):
                raise ValueError("Old password must include at least one uppercase character")
            if not any(c.isdigit() for c in old_password):
                raise ValueError("Old password must include at least one digit")

        # constraints for new_password
        if not any(c.islower() for c in new_password):
            raise ValueError("New password must include at least one lowercase character")
        if not any(c.isupper() for c in new_password):
            raise ValueError("New password must include at least one uppercase character")
        if not any(c.isdigit() for c in new_password):
            raise ValueError("New password must include at least one digit")

        if confirm_new_password != new_password:
            raise ValueError("New Password and Confirm New Password must match")

        return values


class ResetPasswordSchema(BaseModel):
    """Schema for resetting password of a user"""
    email: EmailStr

    new_password: Annotated[
        str,
        StringConstraints(min_length=8,
                          max_length=64,
                          strip_whitespace=True)
    ]

    confirm_new_password: Annotated[
        str,
        StringConstraints(min_length=8,
                          max_length=64,
                          strip_whitespace=True)
    ]

    @model_validator(mode='before')
    @classmethod
    def validate_password(cls, values: dict):
        """
        Validates passwords
        """
        new_password = values.get('new_password')
        confirm_new_password = values.get("confirm_new_password")

        # constraints for new_password
        if not any(c.islower() for c in new_password):
            raise ValueError("New password must include at least one lowercase character")
        if not any(c.isupper() for c in new_password):
            raise ValueError("New password must include at least one uppercase character")
        if not any(c.isdigit() for c in new_password):
            raise ValueError("New password must include at least one digit")

        if confirm_new_password != new_password:
            raise ValueError("New Password and Confirm New Password must match")

        return values


class SetOauthUserPassword(ResetPasswordSchema):
    '''Schema to set the password for a user'''
    pass


class EmailRequest(BaseModel):
    '''use this to validate the email sent for reset password'''
    email: EmailStr

    @model_validator(mode='before')
    @classmethod
    def validate_email(cls, values: dict):
        """
        Validates email
        """
        email = values.get("email")
        try:
            email = validate_email(email, check_deliverability=True)
            if email.domain.count(".com") > 1:
                raise EmailNotValidError("Email address contains multiple '.com' endings.")
            if not validate_mx_record(email.domain):
                raise ValueError('Email is invalid')
        except EmailNotValidError as exc:
            raise ValueError(exc) from exc
        except Exception as exc:
            raise ValueError(exc) from exc
        return values


class EmailSchema(BaseModel):
    email: List[EmailStr]
    subject: str
    body: str


class VerifyToken(BaseModel):
    token: str


class UserData(BaseModel):
    """
    Schema for users to be returned to superadmin
    """
    id: str
    email: EmailStr
    first_name: Optional[str] = ''
    last_name: Optional[str] = ''
    is_active: bool
    is_deleted: bool
    is_verified: bool
    is_onboarded: bool
    is_superadmin: bool
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class OrgUserResponse(BaseModel):
    """Returns details of members in a team"""
    id: str
    first_name: str
    last_name: str
    email: EmailStr
    role_name: str
    permissions: Optional[List[str | None]]


class DeleteSchema(BaseModel):
    '''Model to validate the deletion of a user from an organisation'''
    org_id: str
    user_id: str


class FetchSchema(BaseModel):
    '''Model to validate of a user from an organisation'''
    org_id: str
    user_id: str


# User Profile Schemas
class UserProfileBase(BaseModel):
    organisation_id: str
    full_name: str
    email_address: str
    phone_number: Optional[str] = None
    user_role: Optional[str] = None
    img_url: Optional[str] = None
    bio: Optional[str] = None


class UserProfileCreate(BaseModel):
    full_name: str
    phone_number: Optional[str] = None
    user_role: Optional[str] = None
    img_url: Optional[str] = None


class UserProfileUpdate(BaseModel):
    full_name: Optional[str] = None
    phone_number: Optional[str] = None
    bio: Optional[str] = None
    img_url: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)


class UserProfilePublic(UserProfileBase):
    id: str
    user_id: str

    model_config = ConfigDict(from_attributes=True)
