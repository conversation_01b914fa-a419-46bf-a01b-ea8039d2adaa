from app.models.model import Permission
from pydantic import BaseModel, EmailStr, HttpUrl, ConfigDict
from uuid_extensions import uuid7
from typing import Dict, Optional, List
from datetime import datetime


# Role schema
class RoleCreate(BaseModel):
    org_id: str
    name: str
    description: Optional[str] = None


class RoleResponse(BaseModel):
    id: str
    name: str
    model_config = ConfigDict(from_attributes=True)


class RoleAssignRequest(BaseModel):
    role_id: str
    model_config = ConfigDict(from_attributes=True)


class RoleUpdate(BaseModel):
    name: str
    description: Optional[str] = None
    role_id: str
    model_config = ConfigDict(from_attributes=True)


class RoleDeleteResponse(BaseModel):
    id: str
    message: str

    model_config = ConfigDict(from_attributes=True)


# Permissions
class PermissionCreate(BaseModel):
    title: str
    model_config = ConfigDict(from_attributes=True)


class PermissionResponse(BaseModel):
    id: str
    title: str

    model_config = ConfigDict(from_attributes=True)


class PermissionListResponse(BaseModel):
    permission: str
    model_config = ConfigDict(from_attributes=True)


class PermissionAssignRequest(BaseModel):
    permission_id: str
    model_config = ConfigDict(from_attributes=True)


class PermissionUpdate(BaseModel):
    permission_id: str
    title: str

    model_config = ConfigDict(from_attributes=True)


# permissions to role

class PermissionAssignRole(BaseModel):
    role: str
    permission_ids: List[str]

    model_config = ConfigDict(from_attributes=True)


class PermissionAssignUser(BaseModel):
    user: str
    organisation_id: str
    permission_ids: List[str]

    model_config = ConfigDict(from_attributes=True)

# add user to organization with invitation

class UserAddToOrganisation(BaseModel):
    invitation_link: str


class InvitationCreate(BaseModel):
    user_email: EmailStr
    organisation_id: str


class InviteeSchema(BaseModel):
    first_name: str
    last_name: str
    email: EmailStr
    password: Optional[str] = None
    # permissions: List[PermissionAssignRequest]
    permissions: List[str]
    role_name: str
    organisation_id: str

    model_config = ConfigDict(from_attributes=True)
    # TODO: implement MX checking of email addresses


class UpdateUserDetailsSchema(BaseModel):
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    permissions: Optional[List[str]] = None
    role_name: Optional[str] = None
    # email: Optional[EmailStr] = None

    model_config = ConfigDict(from_attributes=True)
    # TODO: implement MX checking of email addresses

# Competitor
class CompetitorData(BaseModel):
    """Base organisation schema"""
    id: str
    name: str
    link: str
    data: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)


class CompetitorCreate(BaseModel):
    competitor_name: str
    competitor_link: str
    model_config = ConfigDict(from_attributes=True)


# Socialaccounts
class SocialAccountData(BaseModel):
    '''Base social accounts model'''
    id: str
    platform_name: str
    username: str
    platform_link: str

    model_config = ConfigDict(from_attributes=True)


class SocialAccountCreate(BaseModel):
    '''create a social account'''
    platform_name: str
    platform_link: str
    username: str

    model_config = ConfigDict(from_attributes=True)


# organizations
class CreateUpdateOrganisation(BaseModel):
    """Organisation schema to create or date organisation"""
    name: str
    brand_logo: str
    industry: Optional[str] = None
    primary_color: Optional[str] = None
    business_tone: Optional[str] = None
    brand_voice: Optional[str] = None
    target_audience: Optional[str] = None
    company_size: Optional[str] = None
    description: Optional[str] = None
    social_accounts: Optional[List[SocialAccountCreate]] = []
    competitors: Optional[List[CompetitorCreate]] = []

    model_config = ConfigDict(from_attributes=True)

class UpdateOrganisation(BaseModel):
    """Organisation schema to create or date organisation"""
    name: Optional[str] = ''
    brand_logo: Optional[str] = ''
    industry: Optional[str] = None
    primary_color: Optional[str] = None
    business_tone: Optional[str] = None
    brand_voice: Optional[str] = None
    target_audience: Optional[str] = None
    company_size: Optional[str] = None
    description: Optional[str] = None
    social_accounts: Optional[List[SocialAccountCreate]] = []
    competitors: Optional[List[CompetitorCreate]] = []

    model_config = ConfigDict(from_attributes=True)

class AddUpdateOrganisationRole(BaseModel):
    """Schema to update a user role in an organisation"""
    role_id: str
    user_id: str
    org_id: str
    # @field_validator("role")
    # def role_validator(cls, value):
    #     if value not in ["admin", "user", uest", "owner"]:
    #         raise ValueError("Role has to  one of admin, guest, user, or owner")
    #     return value


class RemoveUserFromOrganisation(BaseModel):
    """Schema to delete a user role in an organisation"""
    user_id: str
    org_id: str


class PaginatedOrgUsers(BaseModel):
    """Describe response object for paginated users in organisation"""
    page: int
    per_page: int
    per_page: int
    total: int
    status_code: int
    success: bool
    message: str
    data: List[Dict]


class OrganisationData(BaseModel):
    """Base organisation schema"""
    id: str
    created_at: datetime
    updated_at: datetime
    name: str
    email: Optional[EmailStr] = None
    industry: Optional[str] = None
    company_size: Optional[str] = None
    brand_logo: Optional[str] = None

    business_tone: Optional[str] = None
    primary_color: Optional[str] = None
    brand_voice: Optional[str] = None
    target_audience: Optional[str] = None
    description: Optional[str] = None
    organisation_id: str

    model_config = ConfigDict(from_attributes=True)


class AdminGetOrganisation(BaseModel):
    """Organisation schema to create or date organisation"""
    id: str
    name: str
    brand_logo: str
    industry: Optional[str] = None
    primary_color: Optional[str] = None
    business_tone: Optional[str] = None
    brand_voice: Optional[str] = None
    target_audience: Optional[str] = None
    company_size: Optional[str] = None
    description: Optional[str] = None
    social_account: Optional[List[SocialAccountCreate]] = []
    competitors: Optional[List[CompetitorCreate]] = []

    model_config = ConfigDict(from_attributes=True)


class AdminGetRole(BaseModel):
    """Admin pydantic to return all roles"""
    id: str
    name: str
    organisation: AdminGetOrganisation
    model_config = ConfigDict(from_attributes=True)

# class ResetPasswordToken
# class TeamMember(BaseModel):
#     """Base team member schema"""
#     name: str
#     role: str
#     description: str
