from app.models.model import WaitList
from app.utils.logger import get_logger
from sqlalchemy.orm import Session
from fastapi import HTT<PERSON>Ex<PERSON>, status

logger = get_logger(__name__)


async def add_user_to_waitlist(email: str, db_session: Session):
    try:
        '''Add a user to the waitlist'''
        user = db_session.query(WaitList).filter(WaitList.email == email).first()
        if user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail='Email has already been added'
            )
        db_waitlist = WaitList(email=email)
        db_session.add(db_waitlist)
        db_session.commit()
    except Exception as e:
        logger.info(f"Error adding user to waitlist: {e}")
        db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail='An unexpected error occurred'
        )


async def get_users_in_waitlist(db_session: Session):
    try:
        '''Get all users in the waitlist'''
        return db_session.query(WaitList).all()
    except Exception as e:
        logger.info(f"Error getting users in waitlist: {e}")
        db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail='An unexpected error occurred'
        )