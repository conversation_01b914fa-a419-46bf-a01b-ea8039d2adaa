from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Optional
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, status
from psycopg2 import IntegrityError
from sqlalchemy.orm import Session
from app.models.model import (
    Organisation, Permission, User, Role, user_organisation_roles,
    user_permissions)
from app.schemas.pydantic_schema import (
    AddUpdateOrganisationRole, PermissionAssignRequest,
    PermissionAssignRole, PermissionAssignUser, PermissionCreate,
    PermissionListResponse, PermissionResponse,
    PermissionUpdate, RemoveUserFromOrganisation,
    RoleAssignRequest, RoleCreate, RoleResponse,
    RoleDeleteResponse, RoleUpdate
    )

from app.utils.logger import get_logger

logger = get_logger(__name__)

# CRUD OPERATION ON ROLES
# check list
# 1. create role
# 2. update_role
# 3. delete a role
# 4. get a role
# 5. create permissions
# 6. get all permissions
# 7. get a specific Permission
# 8. update a permission
# 9. Delete a permission
# 10. add permission to role
# 11. remove permission from role
# 12. update permission in role
# 13. add a role to a User in an org
# 14. remove a role from a user
# 15. update a user role
# 16. create invitation
# 17. validate invitation from users


def create_role(role: RoleCreate, db_session: Session):
    """
    Create a new role in the database.

    Parameters:
    - db: SQLAlchemy Session
    - role: RoleCreate schema instance

    Returns:
    - The created Role object
    """
    try:
        logger.info("Creating a new role in organisation")
        # confirm the organisation exists
        logger.debug("Checking if organisation exists")
        db_org = db_session.query(Organisation).filter_by(id=role.org_id).first()

        if not db_org:
            logger.error("No organisation found")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail='Organisation not found'
            )
        # check if role already exists in the organisation
        logger.info(f"searching for existing role: {role.name} in organisation: {db_org.name}")
        db_role = db_session.query(Role).filter_by(
            name=role.name.lower(),
            organisation_id=role.org_id
            ).first()
        if db_role:
            logger.debug("Role: {db_role.name} found")
            return RoleResponse.model_validate(db_role)
        else:
            # create  a new role
            logger.info(f"Creating a new role: {role.name} in organisation: {db_org.name}")
            new_role = Role(
                name=role.name.lower(),
                description=role.description,
                organisation_id=role.org_id
            )

            logger.info("new_role created, adding to database...")
            db_session.add(new_role)
            db_session.commit()
            logger.info(f"Role: {new_role.name} created succesfully")
            db_session.refresh(new_role)
            return RoleResponse.model_validate(new_role)
    except IntegrityError as e:
        db_session.rollback()
        logger.error(f"Integrity Error: {str(e)}")
        raise HTTPException(
            status_code=400,
            detail=f"A role with this name exists already: {str(e)}"
        )
    except HTTPException as e:
        db_session.rollback()
        logger.error(f'HTTPException Error: {str(e)}')
        raise e
    except Exception as e:
        db_session.rollback()
        logger.error(f'Error: {str(e)}')
        raise HTTPException(
            status_code=500,
            detail=f"An unexpected error occured: {str(e)}"
        )


def get_role(role_id: str, db_session: Session):
    """
    Retrieve a role by its ID.

    Parameters:
    - db: SQLAlchemy Session
    - role_id: The ID of the role to retrieve

    Returns:
    - The Role object if found, else None
    """
    try:
        db_role = db_session.query(Role).filter(Role.id == role_id).first()
        if not db_role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Role not found"
            )
        return RoleResponse.model_validate(db_role)
    except HTTPException as e:
        db_session.rollback()
        raise e
    except Exception as e:
        db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}"

        )


def get_roles(db_session: Session, org_id: str, skip: int = 0, limit: int = 10):
    """
    Retrieve a list of roles with pagination.

    Parameters:
    - db: SQLAlchemy Session
    - skip: Number of roles to skip
    - limit: Maximum number of roles to retrieve

    Returns:
    - A list of Role objects
    """
    try:
        # confirm the organisation exists
        db_org = db_session.query(Organisation).filter_by(id=org_id).first()
        if not db_org:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail='Organisation not found'
            )
        res = db_session.query(Role).filter(Role.organisation_id == org_id).offset(skip).limit(limit).all()
        return res
    except HTTPException as e:
        db_session.rollback()
        raise e
    except Exception as e:
        db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'An unexpected error occurred: {str(e)}'
        )


def update_role(db_session: Session, role_update: RoleUpdate) -> RoleResponse:
    """
    Update an existing role.

    Parameters:
    - db: SQLAlchemy Session
    - role_id: The ID of the role to update
    - role_update: RoleUpdate schema instance with updated values

    Returns:
    - The updated Role object if found and updated, else None
    """
    try:
        # get the role from the db
        db_role = db_session.query(Role).filter(Role.id == role_update.role_id).first()
        if not db_role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Requested role not found"
            )
        # update the role
        db_role.name = role_update.name.lower()
        db_role.description = role_update.description
        db_session.commit()

        return RoleResponse.model_validate(db_role)
    except HTTPException as e:
        db_session.rollback()
        raise e
    except IntegrityError:
        db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail='A role with this name already exists'
        )
    except Exception as e:
        db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'An unexpected error occurred: {str(e)}'
        )


def delete_roles(db_session: Session, role_id: RoleAssignRequest) -> str:
    """
    Delete a role by its ID.

    Parameters:
    - db: SQLAlchemy Session
    - role_id: The ID of the role to delete

    Returns:
    - The deleted RoleResponse object if found and deleted, else None
    """
    try:
        db_role = db_session.query(Role).filter(Role.id == role_id.role_id).first()
        if not db_role:

            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Role not found"
            )

        db_session.delete(db_role)
        db_session.commit()
        return {
            "message": "Role deleted successfully"
        }
    except HTTPException as e:
        db_session.rollback()
        raise e
    except Exception as e:
        db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'An unexpected error occurred: {str(e)}'
        )


# permissions
def create_permission(permission: PermissionCreate, db_session: Session) -> PermissionResponse:
    """
    Create a new permission in the database.

    Parameters:
    - db: SQLAlchemy Session
    - permission: PermissionCreate schema instance

    Returns:
    - The created Permission object
    """
    try:
        # check if permission already exists
        db_permission = db_session.query(Permission).filter_by(title=permission.title.lower()).first()
        if db_permission:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail='Permission already exists'
            )
        else:
            # create a new permission
            #UPDATE: Create different permissions from inbuilt to organisation specific
            new_permission = Permission(
                title=permission.title.lower()
            )
            db_session.add(new_permission)
            db_session.commit()
            db_session.refresh(new_permission)
            return PermissionResponse.model_validate(new_permission)
    except IntegrityError as e:
        db_session.rollback()
        raise HTTPException(
            status_code=400,
            detail=f"A permission with this name exists already: {str(e)}"
        )
    except HTTPException as e:
        db_session.rollback()
        raise e
    except Exception as e:
        db_session.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"An unexpected error occured: {str(e)}"
        )


def get_permission(permission: str, db_session: Session) -> PermissionResponse:
    """
    Retrieve a permission by its ID.

    Parameters:
    - db: SQLAlchemy Session
    - permission_id: The ID of the role to retrieve

    Returns:
    - The Permission object if found, else None
    """
    try:
        db_permission = db_session.query(Permission).filter(Permission.id == permission).first()
        if not db_permission:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Permission not found"
            )
        return PermissionResponse.model_validate(db_permission)
    except HTTPException as e:
        db_session.rollback()
        raise e
    except Exception as e:
        db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}"

        )


def get_permissions(db_session: Session, skip: int = 0, limit: int = 10):
    """
    Retrieve a list of permissions with pagination.

    Parameters:
    - db: SQLAlchemy Session
    - skip: Number of permissions to skip
    - limit: Maximum number of permission to retrieve

    Returns:
    - A list of Permission objects
    """
    try:
        permissions = db_session.query(Permission).offset(skip).limit(limit).all()
        return [PermissionResponse.model_validate(permission) for permission in permissions]
    except Exception as e:
        db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'An unexpected error occurred: {str(e)}'
        )


def update_permission(db_session: Session, permission_update: PermissionUpdate):
    """
    Update an existing permission.

    Parameters:
    - db: SQLAlchemy Session
    - permission_id: The ID of the permission to update
    - permission_update: PermissionUpdate schema instance with updated values

    Returns:
    - The updated permission object if found and updated, else None
    """
    try:
        db_permission = db_session.query(Permission).filter(Permission.id == permission_update.permission_id).first()
        if not db_permission:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Requested permission not found"
            )
        # update the permission
        db_permission.title = permission_update.title.lower()
        db_session.commit()

        return PermissionResponse.model_validate(db_permission)
    except HTTPException as e:
        db_session.rollback()
        raise e
    except IntegrityError:
        db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail='A permission with this name already exists'
        )
    except Exception as e:
        db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'An unexpected error occurred: {str(e)}'
        )


def delete_permission(db_session: Session, permission_id: str):
    """
    Delete a permission by its ID.

    Parameters:
    - db: SQLAlchemy Session
    - permission_id: The ID of the permission to delete

    Returns:
    - The deleted PermissionResponse object if found and deleted, else None
    """
    try:
        db_permission = (
            db_session.query(Permission)
            .filter(Permission.id == permission_id)
            .first()
        )
        if not db_permission:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Permission not found"
            )

        db_session.delete(db_permission)
        db_session.commit()
        return {
            "message": "Permmission deleted successfully"
        }
    except HTTPException as e:
        db_session.rollback()
        raise e
    except Exception as e:
        db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'An unexpected error occurred: {str(e)}')


def add_permissions_to_role(permission: PermissionAssignRole, db_session: Session):
    '''This adds a list of permissions to a role'''
    try:
        # 1. Get the role
        db_role = db_session.query(Role).filter_by(id=permission.role).first()
        if not db_role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail='Requested role not found'
            )

        # 2. Get the permissions
        db_permissions = db_session.query(Permission).filter(Permission.id.in_(permission.permission_ids)).all()
        if not db_permissions or len(db_permissions) != len(permission.permission_ids):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail='One or more permissions not found'
            )

        # 3. Add each permission to the role
        db_role.permissions = db_permissions

        # Commit the changes
        db_session.commit()

        return {
            "message": f"Permissions successfully added to Role: {db_role.name}"
        }

    except HTTPException as e:
        db_session.rollback()
        raise e
    except Exception as e:
        db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'An unexpected error occurred: {str(e)}'
        )


def remove_permission_from_role(role_id: str, permission_ids: str, db_session: Session):
    '''Removes a permission from a role'''
    try:
        # 1. get the roles and permissions
        db_role = db_session.query(Role).filter(Role.id == role_id).first()
        if not db_role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail='Requested role not found'
            )
        # 2. Get the permissions
        db_permissions = db_session.query(Permission).filter(Permission.id.in_(permission_ids)).all()
        if not db_permissions:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail='Requested permissions not found'
            )

        # 3. Remove the permissions from the role
        for db_permission in db_permissions:
            if db_permission in db_role.permissions:
                db_role.permissions.remove(db_permission)

        db_session.commit()
        return {
            'message': 'Permission removed from role'
        }
    except HTTPException as e:
        db_session.rollback()
        raise e
    except Exception as e:
        db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'An unexpected error occurred: {str(e)}'
        )


def update_permissions_in_role(permission: PermissionAssignRole, db_session: Session):
    '''updates the permission available in a role
    Only permissions passed to these function should remain in the role
    '''
    try:
        # get the permission and roles
        db_role = db_session.query(Role).filter(Role.id == permission.role_id).first()
        if not db_role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail='Requested role not found'
            )
        # fetch all the permissions in the file
        db_permissions = db_session.query(Permission).filter(Permission.id.in_(permission.permission_ids)).all()

        if not db_permissions or len(db_permissions) != len(permission.permission_ids):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail='One or more permissions not found'
            )
        # apply the permissions to the role automatically remove permissions not passed
        db_role.permissions = db_permissions

        db_session.commit()
    except HTTPException as e:
        db_session.rollback()
        raise e
    except Exception as e:
        db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'An unexpected error occurred: {str(e)}'
        )


def add_role_to_user(credentials: AddUpdateOrganisationRole, db_session: Session):
    '''This adds a role to a user'''
    try:
        # get the user and role
        db_user = db_session.query(User).filter_by(id=credentials.user_id).first()
        if not db_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail='Requested user resource not found'
            )
        db_org = db_session.query(Organisation).filter_by(id=credentials.org_id).first()
        if not db_org:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail='Requested organisation is not found'
            )
        db_role = db_session.query(Role).filter_by(id=credentials.role_id, organisation_id=credentials.org_id).first()
        if not db_role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail='Requested role is not found'
            )

        # check if the user is in the organisation
        if credentials.org_id not in [org.id for org in db_user.organisations]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail='User is not a member of the organisation'
            )
        # add the user to the org and assign such role to the user

        # checks if there is an established relationship between user and organisation first
        user_org_role = db_session.query(user_organisation_roles).filter(
            user_organisation_roles.c.user_id == credentials.user_id,
            user_organisation_roles.c.organisation_id == credentials.org_id
        ).first()

        # if such exists, it adds the role to that relationship, so the user has that role
        if user_org_role:
            # Update the role
            db_session.execute(
                user_organisation_roles.update().where(
                    user_organisation_roles.c.user_id == credentials.user_id,
                    user_organisation_roles.c.organisation_id == credentials.org_id
                ).values(role_id=credentials.role_id)
            )
        # if not it create a new record for the user, org role. Now this means if a user is not in an org, and a role wants to be added to them, it'll automatically add the user to the org. don't know if i should keep this
        else:
            new_user_org_role = user_organisation_roles.insert().values(
                user_id=credentials.user_id,
                organisation_id=credentials.org_id,
                role_id=credentials.role_id,
                status="active"
            )
            db_session.execute(new_user_org_role)

        db_session.commit()
        return {"message": f"Role {db_role.name} assigned to User {db_user.first_name} in Organization {db_org.name}."}
    except HTTPException as e:
        db_session.rollback()
        raise e
    except Exception as e:
        db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'An unexpected error occurred: {str(e)}'
        )


def remove_role_from_user(credentials: RemoveUserFromOrganisation, db_session: Session):
    '''Removes a role from a user in an organisation'''
    try:
        # get the user
        db_user = db_session.query(User).filter_by(id=credentials.user_id).first()
        if not db_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail='User not found'
            )
        # get the org
        db_org = db_session.query(Organisation).filter_by(id=credentials.org_id).first()
        if not db_org:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail='Organisation detail not found'
            )
        # get the user_org_role relationship
        db_user_org_role = db_session.query(user_organisation_roles).filter_by(
            user_id=credentials.user_id,
            organisation_id=credentials.org_id
            ).first()
        db_user_org_role.role_id = None
        db_session.commit()
    except HTTPException as e:
        db_session.rollback()
        raise e
    except Exception as e:
        db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'An unexpected error occurred: {str(e)}'
        )


def update_a_user_role(credentials: AddUpdateOrganisationRole, db_session: Session):
    '''Updates the role a user have in an organisation'''
    try:
        # get the user, org, and roles
        db_user_org_roles = db_session.query(user_organisation_roles).filter(
            user_id=credentials.user_id,
            organisation_id=credentials.org_id
        ).first()
        if not db_user_org_roles:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail='User organisation role not found'
            )
        db_user_org_roles.role_id = credentials.role_id
        return {
            'message': 'Role updated successfully',
        }
    except HTTPException as e:
        db_session.rollback()
        raise e
    except Exception as e:
        db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'An unexpected error occurred: {str(e)}'
        )


def add_permissions_to_user(permission: PermissionAssignUser, db_session: Session):
    '''This adds a list of permissions to a user within an organization context'''
    try:
        # 1. Get the user
        db_user = db_session.query(User).filter_by(id=permission.user).first()
        if not db_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail='User not found'
            )

        # 2. Get the permissions
        db_permissions = db_session.query(Permission).filter(Permission.id.in_(permission.permission_ids)).all()
        if not db_permissions or len(db_permissions) != len(permission.permission_ids):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail='One or more permissions not found'
            )

        # 3. Add each permission to the user with organization context
        for db_permission in db_permissions:
            db_session.execute(
                user_permissions.insert().values(
                    user_id=db_user.id,
                    organisation_id=permission.organisation_id,
                    permission_id=db_permission.id
                )
            )

        # Commit the changes
        db_session.commit()

        return {
            "message": f"Permissions successfully added to User: {db_user.email} for Organization: {permission.organisation_id}"
        }

    except HTTPException as e:
        db_session.rollback()
        raise e
    except Exception as e:
        db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'An unexpected error occurred: {str(e)}'
        )
