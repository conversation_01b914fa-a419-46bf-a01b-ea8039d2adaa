from datetime import datetime, timedelta, timezone

import google.auth.exceptions
import httpx
from app.core.settings import settings
from app.database.session import get_db
from app.models.model import (OTPKEY, Competitor, Organisation, Role,
                              SocialAccount, User, user_organisation_roles)
from app.schemas.pydantic_schema import (CreateUpdateOrganisation, RoleCreate,
                                         UpdateOrganisation)
from app.schemas.user import (ChangePasswordSchema, EmailRequest, LoginRequest,
                              ResetPasswordSchema, SetOauthUserPassword,
                              UserCreate, UserResponse, VerifyToken)
from app.services.roles_permissions import create_role
from app.utils.email_service import EmailService
from app.utils.logger import get_logger
from app.utils.openai_service import generate_roles
from app.utils.security import (create_access_token, create_refresh_token,
                                create_reset_token, create_verification_token,
                                decode_token, get_password_hash,
                                verify_password, verify_verification_token)
from fastapi import BackgroundTasks, Depends, HTTPException, Request, status
from fastapi.security import OAuth2Password<PERSON>earer
from google.auth.transport import requests
from google.oauth2 import id_token
from jinja2 import Template
from jose import ExpiredSignatureError, JWTError, jwt
from sqlalchemy.orm import Session

from app.events.publishers import publish_organization_created, publish_organization_updated, publish_user_login, publish_user_registered

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/v1/auth/login")

logger = get_logger(__name__)


# USER REGISTRATION SERVICE
async def create_user(
    db_session: Session,
    user_schema: UserCreate,
    background_tasks: BackgroundTasks,
    request: Request,
) -> UserResponse:
    """
    Create a new user and send a verification email.

    Parameters:
    - db: SQLAlchemy Session
    - user_create: UserCreate schema instance

    Returns:
    - The created UserResponse object
    """
    try:
        user_schema.email = user_schema.email.lower()
        if db_session.query(User).filter(
            User.email == user_schema.email
        ).first():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User with this email already exists",
            )

        user_schema.password = get_password_hash(user_schema.password)
        new_user = User(**user_schema.model_dump())
        logger.debug("New user account created")
        new_user.email = new_user.email.lower()
        new_user.status = "onboarded"
        db_session.add(new_user)
        db_session.commit()
        db_session.refresh(new_user)
        await verification(new_user, request, background_tasks)

        return UserResponse.model_validate(new_user)
    except HTTPException as e:
        logger.error(f"An HTTPError occured: {str(e)}")
        db_session.rollback()
        raise e
    except Exception as e:
        db_session.rollback()
        logger.error(f"An unexpected error occurred: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred. Please try again later.",
        )


async def verification(
    user,
    request: Request,
    background_tasks: BackgroundTasks,
):
    verification_token = create_verification_token(user.email)
    url = f"{request.url_for('verify_email', token=verification_token)}"
    template_data = {
        "verification_url": url,
        "full_name": f"{user.first_name.capitalize()} {user.last_name.capitalize()}",
    }
    await EmailService.send(
        title="Email Verification",
        template_name="registration_email.html",
        recipients=[user.email],
        background_task=background_tasks,
        template_data=template_data,
    )


async def verify_mail_service(token: str, db_session: Session):
    """Verification service to activate the user"""
    # collect and verify the token passed
    try:
        logger.info("Authenticating user process")
        response = verify_verification_token(token)
        if not response:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid Token"
            )
        db_user = db_session.query(User).filter(User.email == response).first()
        if not db_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="No user found"
            )
        db_user.is_verified = True
        db_session.commit()
        logger.info(f"User {response} verified successfully")
        return {"message": "Verification successfull, proceed to login"}
    except HTTPException as e:
        logger.error(f"An error occurred: {str(e)}")
        db_session.rollback()
        raise e
    except Exception as e:
        logger.error(f"An error occurred: {str(e)}")
        db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e.args[0])}",
        )


async def get_geolocation(ip):
    async with httpx.AsyncClient(timeout=30) as client:
        response = await client.get(f"http://ip-api.com/json/{ip}")
        return response.json()


# USER LOGIN SERVICE
async def authenticate_user(
    login_request: LoginRequest,
    background_tasks: BackgroundTasks,
    db_session: Session,
    request: Request
) -> dict:
    """
    Authenticate a user and generate an access token.

    Parameters:
    - db: SQLAlchemy Session
    - login_request: LoginRequest schema instance

    Returns:
    - The JWT access token if authentication is successful, else None
    """
    try:
        db_user = (
            db_session.query(User).filter(User.email == login_request.email).first()
        )
        if not db_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid user email"
            )
        if db_user.password:
            if not verify_password(login_request.password, db_user.password):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid user password",
                )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Authentication method not supported, use Google or Facebook auth",
            )
        if not db_user.is_verified:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Account not yet verified",
            )

        access_token = create_access_token(db_user.id, db_session)
        refresh_token = create_refresh_token(db_user.id)
        response = {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "user": UserResponse.model_validate(db_user)
        }
        logger.info(f"User {db_user.email} authenticated successfully")
        # send mail
        user_ip = await get_geolocation(request.client.host)
        location_data = f"{user_ip.get("city")}, {user_ip.get("country")}"
        template_data = {
            "full_name":
                f"{db_user.first_name.capitalize()} "
                f"{db_user.last_name.capitalize()}",
            "time": datetime.now(timezone.utc).strftime("%b %d, %Y at %I:%M %p"),
            "location": location_data
        }
        await EmailService.send(
            title="Sign-in Confirmation",
            template_name="sign-in-confirmation.html",
            recipients=[db_user.email],
            background_task=background_tasks,
            template_data=template_data,
        )

        return response
    except HTTPException as e:
        logger.error(f"HTTPException Error: {str(e)}")
        raise e
    except Exception as e:
        logger.error(f"Exception Error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occured: {str(e.args[0])}",
        )


async def set_oauth_user_password(
    user_detail: SetOauthUserPassword, db_session: Session
):
    """Sets the password for a user"""
    try:
        logger.info("setting password for an Oauth user")
        logger.info("verifying users exists in db")
        db_user = db_session.query(User).filter_by(email=user_detail.email).first()
        if not db_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Requested user not found"
            )

        logger.info("verifying users does not have a password set before")
        if db_user.password:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User already has a password, use change passwords to change your password",
            )
        logger.info(f"setting up new password for user: {db_user.email}")
        db_user.password = get_password_hash(user_detail.new_password)
        db_session.commit()
        return {"message": "Password successfully set"}
    except HTTPException as e:
        logger.error(f"An HTTPException error occurred: {str(e)}")
        raise e
    except Exception as e:
        db_session.rollback()
        logger.error(f"An unexpected error occurred: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e.args[0])}",
        )


async def google_signin(token: VerifyToken, db_session: Session):
    """implement google signin on the backend"""
    try:
        token = token.token
        # Verify the token with Google
        idinfo = id_token.verify_oauth2_token(
            token, requests.Request(), settings.GOOGLE_CLIENT_ID
        )
        # Check if the token is valid for the client
        if idinfo["aud"] != settings.GOOGLE_CLIENT_ID:
            raise ValueError("Token's audience doesn't match the client ID")

        email = idinfo["email"]
        # google_user_id = idinfo['sub'] my model does not have this
        full_name = idinfo.get("name", "")
        name_parts = full_name.split(" ", 1)
        first_name = name_parts[0]
        last_name = name_parts[1] if len(name_parts) > 1 else ""

        # Log the user in or create a new user account
        db_user = db_session.query(User).filter(User.email == email).first()
        if not db_user:
            db_user = User(
                email=email,
                first_name=first_name,
                last_name=last_name,
                is_verified=True,
                status="onboarded",
            )
            db_session.add(db_user)
            db_session.commit()
        return {
            "access_token": create_access_token(db_user.id, db_session),
            "refresh_token": create_refresh_token(db_user.id),
            "user": UserResponse.model_validate(db_user),
        }

    except google.auth.exceptions.GoogleAuthError as e:
        db_session.rollback()
        raise HTTPException(
            status_code=400, detail=f"Token verification failed: {str(e.args[0])}"
        )

    except ValueError as e:
        db_session.rollback()
        raise HTTPException(status_code=400, detail=f"Invalid token: {str(e.args[0])}")

    except Exception as e:
        db_session.rollback()
        raise HTTPException(
            status_code=500, detail=f"Internal server error: {str(e.args[0])}"
        )


async def get_current_user(
    token: str = Depends(oauth2_scheme), db_session: Session = Depends(get_db)
):
    """
    Returns the currently logged-in user based on the JWT token.

    Parameters:
    - token: The JWT token provided in the request
    - db_session: The database session to query the user

    Returns:
    - The User object corresponding to the decoded user_id from the token

    Raises:
    - HTTPException: If the token is invalid, expired, or the user is not found
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        # 1. Decode the token sent to it
        user_id = decode_token(token=token)

        if user_id is None:
            raise credentials_exception

        # 2. Get the user from the db
        user = db_session.query(User).filter(User.id == user_id, User.is_deleted == False).first()

        if user is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No user found with such details",
            )
        return user
    except HTTPException as e:
        raise e
    except ValueError as e:
        # Handle token-related issues (e.g., expired or invalid token)
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e.args[0]),
            headers={"WWW-Authenticate": "Bearer"},
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e.args[0])}",
        )


async def change_password_service(
    db_session: Session, change_password_schema: ChangePasswordSchema, user_id: str
):
    """
    Change the user's password.

    Parameters:
    - db: SQLAlchemy Session
    - user_id: ID of the user
    - change_password_schema: ChangePasswordSchema instance with old and new passwords

    Returns:
    - Success message if password is changed, else error message
    """
    try:
        # 1. get the user from the db
        db_user = db_session.query(User).filter(User.id == user_id).first()
        if not db_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
            )
        # 2. check to confirm the old password inputted is same as the old password
        if not verify_password(change_password_schema.old_password, db_user.password):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Old password does not match",
            )
        # 3. Check that the new password is not same as the old password
        if verify_password(change_password_schema.new_password, db_user.password):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Your new password cannot be same as your old password",
            )
        # 4. change the user password
        db_user.password = get_password_hash(change_password_schema.new_password)
        db_session.commit()
        return {"message": "Password changed successfully"}
    except HTTPException as e:
        db_session.rollback()
        raise e
    except Exception as e:
        db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e.args[0])}",
        )


async def forgot_password_service(
    db_session: Session,
    email_schema: EmailRequest,
    background_tasks: BackgroundTasks
):
    """checks if the user is in the db"""
    try:
        db_user = (
            db_session.query(User).filter(User.email == email_schema.email).first()
        )
        if not db_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="No user found"
            )
        token = create_reset_token()

        otp_credentials = (
            db_session.query(OTPKEY).filter(OTPKEY.user_id == db_user.id).first()
        )
        current_time = datetime.now(timezone.utc)
        if not otp_credentials:
            otp_credentials = OTPKEY(
                user_id=db_user.id,
                otp_signing_key=token,
                expiry_time=current_time + timedelta(minutes=5)

            )
            db_session.add(otp_credentials)
        else:
            otp_credentials.otp_signing_key = token
            otp_credentials.expiry_time = current_time + timedelta(minutes=5)

        db_session.commit()
        db_session.refresh(otp_credentials)
        # send the email
        template_data = {
            "token": token,
            "expiry": otp_credentials.expiry_time.strftime("%b %d, %Y at %I:%M %p")
        }
        await EmailService.send(
            title="Verification Code",
            template_name="password_reset_otp.html",
            recipients=[db_user.email],
            background_task=background_tasks,
            template_data=template_data,
        )
        return {"message": "Password reset mail sent. Please check your inbox"}
    # 5. handle exceptions
    except HTTPException as e:
        db_session.rollback()
        raise e
    except Exception as e:
        db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e.args[0])}",
        )


async def resend_token_service(
    db_session: Session,
    email_schema: EmailRequest,
    background_tasks: BackgroundTasks
):
    """checks if the user is in the db"""
    try:
        db_user = (
            db_session.query(User).filter(User.email == email_schema.email).first()
        )
        if not db_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="No user found"
            )
        # create a new token
        token = create_reset_token()

        otp_credentials = (
            db_session.query(OTPKEY).filter(OTPKEY.user_id == db_user.id).first()
        )

        if not otp_credentials:
            raise HTTPException(
                status_code=400, detail="No valid reset session available")
        old_token = otp_credentials.otp_signing_key
        expired_time = otp_credentials.expiry_time

        current_time = datetime.now(timezone.utc)
        otp_credentials.otp_signing_key = token
        otp_credentials.expiry_time = current_time + timedelta(minutes=5)

        db_session.commit()
        template_data = {
            "old_token": old_token,
            "token": token,
            "expiry": otp_credentials.expiry_time.strftime("%b %d, %Y at %I:%M %p"),
            "generated_time": current_time.strftime("%b %d, %Y at %I:%M %p"),
            "expired_time": expired_time.strftime("%b %d, %Y at %I:%M %p") if expired_time < current_time else current_time.strftime("%b %d, %Y at %I:%M %p")
        }
        await EmailService.send(
            title="Verification Code",
            template_name="resend_token_email.html",
            recipients=[db_user.email],
            background_task=background_tasks,
            template_data=template_data,
        )
        return {"message": "Password reset mail resent. Please check your inbox"}
    # 5. handle exceptions
    except HTTPException as e:
        db_session.rollback()
        raise e
    except Exception as e:
        db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e.args[0])}",
        )


async def verify_reset_token_service(
    token: str,
    user_email: str,
    db_session
):
    """Verifying the token sent to the backend for reseting password"""
    try:
        db_user = db_session.query(User).filter(User.email == user_email).first()
        if not db_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No user associated with that email",
            )
        # Check if the user has an associated OTP key
        if db_user and db_user.otp_key:
            # check if key is not expired
            if db_user.otp_key.expiry_time < datetime.now(timezone.utc):
                raise HTTPException(
                    status_code=400,
                    detail='OTP code expired. Please request a new one'
                )
            signing_key = db_user.otp_key.otp_signing_key
        else:
            raise HTTPException(
                status_code=400, detail="OTP key not found for this user"
            )

        if not (token == signing_key):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid Token"
            )
        db_session.delete(db_user.otp_key)
        db_session.commit()
        return {"message": "Token verified, proceed to input your new password"}

    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e.args[0])}",
        )


async def reset_password_service(
    db_session: Session,
    reset_password_schema: ResetPasswordSchema,
    request: Request,
    background_tasks: BackgroundTasks
):
    """
    Reset the user's password using a token.

    Parameters:
    - db: SQLAlchemy Session
    - token: to validate the user
    - new_password: New password to set
    - confirm_password: password

    Returns:
    - Success message if password is reset, else error message
    """
    try:
        # get the user
        db_user = (
            db_session.query(User)
            .filter(User.email == reset_password_schema.email)
            .first()
        )
        if not db_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="No user found"
            )

        # 3. perform the changing of passwords
        db_user.password = get_password_hash(
            reset_password_schema.new_password)
        db_session.commit()
        template_data = {
            "first_name": db_user.first_name.capitalize()
        }
        await EmailService.send(
            title="Reset Password Confirmation",
            template_name="password_reset.html",
            recipients=[db_user.email],
            background_task=background_tasks,
            template_data=template_data,
        )
        return {"message": "Password reset successfull"}
    except HTTPException as e:
        db_session.rollback()
        raise e
    except Exception as e:
        db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e.args[0])}",
        )


async def refresh_token_service(token: VerifyToken, db_session):
    """refreshes the access token"""
    # get the refresh token sent to the server
    try:
        # decode the token
        token = token.token
        try:
            payload = jwt.decode(token, settings.SECRET_KEY, settings.ALGORITHM)
        except ExpiredSignatureError:
            logger.warning("Token has expired")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Token has expired"
            )
        except JWTError as e:
            logger.warning(f"Invalid token: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token"
            )
        if not payload:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Refresh token tampered with",
            )
        if payload.get("type") != "refresh":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="A refresh token is required",
            )
        if not payload.get("user_id"):
            raise HTTPException(
                statu_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid refresh token"
            )
        token = create_access_token(payload.get("user_id"), db_session)
        return {"message": "Access token regenerated", "access_token": token}
    except HTTPException as e:
        raise e
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=f"{str(e.args[0])}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e.args[0])}",
        )


async def create_organisation_service(
    org_data: CreateUpdateOrganisation, db_session: Session, current_user: User
):
    """
    * This service creates an organisation along with its competitors and social accounts
    """
    """
    steps to achieve this
    1. get the current signed in user
    2. create the org and assign the user to the org, then assign a role to the user in the org
    """
    try:
        logger.info(f"Creating organisation data for user: {current_user}")
        # check if the organisation exists
        logger.debug(
            f"Step 1. Checking if organisation name {org_data.name} already exists"
        )
        db_org = (
            db_session.query(Organisation)
            .join(user_organisation_roles)
            .filter(
                Organisation.name == org_data.name,
                user_organisation_roles.c.user_id == current_user.id,
            )
            .first()
        )
        if db_org:
            logger.error(
                f"Organisation with this name: {db_org.name} already exists for user: {current_user}"
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Organisation already exists",
            )
        # create the organisation
        logger.info("Creating a new organisation")
        new_organisation = Organisation(
            **org_data.model_dump(exclude={"social_accounts", "competitors"})
        )
        db_session.add(new_organisation)
        db_session.flush()
        # db_session.refresh(new_organisation)
        logger.info(f"New organisation created: {new_organisation}")

        # add the current user as the admin of the org
        # create a role and attach it to the org
        logger.info("Retrieving admin role if present in organisation")
        admin_role = (
            db_session.query(Role)
            .filter_by(name="owner", organisation_id=new_organisation.id)
            .first()
        )

        # if no role, create one
        if not admin_role:
            role_details: RoleCreate = RoleCreate(
                org_id=new_organisation.id,
                name="owner",
                is_builtin=True,
            )
            admin_role = create_role(role_details, db_session)
            logger.info(f"Role created: {admin_role.name}")

        else:
            logger.info(f"Admin role exists: {admin_role.name}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No two persons can be owner to an organisation",
            )

        logger.info("Inserting the user, org and role to the secondary table")
        # insert into the user-organisation-roles table
        user_org_role = user_organisation_roles.insert().values(
            user_id=current_user.id,
            organisation_id=new_organisation.id,
            role_id=admin_role.id,
            is_owner=True,
            status="active",
        )
        db_session.execute(user_org_role)

        logger.info("Organisation created and added to secondary table")
        # 2. add social media and competitors if provided
        if org_data.social_accounts:
            for socials in org_data.social_accounts:
                logger.info(f"social: {socials}")
                new_socials = SocialAccount(
                    **socials.model_dump(), organisation_id=new_organisation.id
                )
                db_session.add(new_socials)
                db_session.flush()
        if org_data.competitors:
            logger.info("Adding competitors details")

            for competitor in org_data.competitors:
                logger.info(f"Cometitors: {competitor}")
                new_competitor = Competitor(
                    **competitor.model_dump(), organisation_id=new_organisation.id
                )
                db_session.add(new_competitor)
                db_session.flush()
        if current_user.status == "invited":
            current_user.status = "onboarded"
        if not current_user.is_onboarded:
            current_user.is_onboarded = True
        logger.info("User is onboarded")

        # add suggested roles
        try:
            logger.info("generating organisational roles")
            suggested_roles = await generate_roles(org_data)
            new_organisation.suggested_role = suggested_roles
        except HTTPException as e:
            logger.error(f"Error occurred generating roles: {str(e)}")
            raise e

        db_session.commit()

        logger.info(f"Organisation created successfully: {new_organisation}")

        # Publish organization creation event
        try:
            await publish_organization_created(
                organization_id=str(new_organisation.id),
                organization_name=str(new_organisation.name),
                created_by=str(current_user.id),
            )
        except Exception as e:
            logger.warning(f"Failed to publish organization creation event: {e}")

        return new_organisation
    except HTTPException as e:
        logger.error(f"An unexpected error has occurred: {str(e)}")
        raise e
    except Exception as e:
        db_session.rollback()
        logger.error(f"An unexpected error has occurred: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred",
        )


async def update_organisation(
    organisation_id: str, user_id, organisation_details: UpdateOrganisation, db_session
):
    """Update an organisation detail"""
    try:
        # confirm the organisation exists
        organisation = (
            db_session.query(Organisation).filter_by(id=organisation_id).first()
        )
        if not organisation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Requested organisation not found",
            )

        # check if the organisation name hasn't been taken before
        logger.info("checking if org name is not yet used")

        db_organisation = (
            db_session.query(user_organisation_roles)
            .join(
                Organisation,
                Organisation.id == user_organisation_roles.c.organisation_id,
            )
            .filter(
                user_organisation_roles.c.user_id
                == user_id,  # Ensures it's the current user
                user_organisation_roles.c.is_owner
                is True,  # Ensures the user is an owner
                Organisation.name
                == organisation_details.name,  # Matches the organization name
            )
            .first()
        )

        if db_organisation:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="An organisation with that name already exists for this user.",
            )

        logger.info(organisation_details)
        # update the organisation details
        organisation.name = (
            organisation_details.name
            if organisation_details.name
            else organisation.name
        )

        organisation.brand_logo = (
            organisation_details.brand_logo
            if organisation_details.brand_logo
            else organisation.brand_logo
        )

        organisation.primary_color = (
            organisation_details.primary_color
            if organisation_details.primary_color
            else organisation.primary_color
        )

        organisation.description = (
            organisation_details.description
            if organisation_details.description
            else organisation.description
        )

        organisation.brand_voice = (
            organisation_details.brand_voice
            if organisation_details.brand_voice
            else organisation.brand_voice
        )

        # the competitors list
        if organisation_details.competitors:
            logger.info("Adding competitors details")

            for competitor in organisation_details.competitors:
                logger.info(f"Competitors: {competitor}")
                new_competitor = Competitor(
                    **competitor.model_dump(), organisation_id=organisation.id
                )
                db_session.add(new_competitor)
                logger.debug(f"Competitor added: {new_competitor}")
                db_session.flush()
        db_session.commit()
        db_session.refresh(organisation)

        # Publish organization update event
        try:
            await publish_organization_updated(
                organization_id=organisation.id,
                organization_name=organisation.name,
                updated_by=current_user.id,
                changes=organisation_details.model_dump(exclude_unset=True),
            )
        except Exception as e:
            logger.warning(f"Failed to publish organization update event: {e}")

        return organisation
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"An error occurred: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error has occurred: {str(e.args[0])}",
        )


async def delete_organisation(
    db_session: Session, organisation_id: str, current_user: User
):
    """Delete an organisation and all its related data."""
    try:
        # Fetch the organisation
        organisation = (
            db_session.query(Organisation).filter_by(id=organisation_id).first()
        )
        if not organisation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Organisation not found"
            )

        # Check if the current user is the owner of the organisation
        user_org_role = (
            db_session.query(user_organisation_roles)
            .filter_by(
                user_id=current_user.id, organisation_id=organisation_id, is_owner=True
            )
            .first()
        )

        if not user_org_role:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You do not have permission to delete this organisation",
            )

        # Delete related data
        db_session.query(SocialAccount).filter_by(
            organisation_id=organisation_id
        ).delete()
        db_session.query(Competitor).filter_by(organisation_id=organisation_id).delete()
        db_session.query(user_organisation_roles).filter_by(
            organisation_id=organisation_id
        ).delete()

        # Delete the organisation
        db_session.delete(organisation)
        db_session.commit()

        return {"message": "Organisation and all related data deleted successfully"}
    except HTTPException as e:
        db_session.rollback()
        raise e
    except Exception as e:
        db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e.args[0])}",
        )


async def get_user_org_service(user: User, db_session: Session):
    """
    Retrieve all organisations a user belongs to.

    Parameters:
    - user: User object
    - db_session: SQLAlchemy Session

    Returns:
    - A list of organisations the user belongs to
    """
    try:
        organisations = (
            db_session.query(Organisation)
            .join(user_organisation_roles)
            .filter(user_organisation_roles.c.user_id == user.id)
            .all()
        )
        return organisations
    except Exception as e:
        logger.error(f"An error occurred while fetching organisations: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while fetching organisations",
        )


async def selected_user_org_service(
    user: User, db_session: Session, organisation_id: str
):
    """selects the organisation the user is working in"""
    try:
        # check if the user is in the organisation
        user_org = (
            db_session.query(Organisation).join(user_organisation_roles)
            .filter(
                user_organisation_roles.c.user_id == user.id,
                user_organisation_roles.c.organisation_id == organisation_id,
            )
            .first()
        )
        if not user_org:
            # user is not a part of this organisation
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="User is not a part of this organisation",
            )
        # user_org.last_seen = datetime.now(timezone.utc)
        # TODO: return access and refresh token to the user
        return user_org
    except HTTPException as e:
        logger.error(f"An error occurred while selecting organisation: {str(e)}")
        raise e
    except Exception as e:
        logger.error(f"An error occurred while selecting organisation: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while selecting organisation",
        )


async def has_completed_onboarding(user: User) -> bool:
    """
    Check if the user has completed onboarding.

    Parameters:
    - user: User object

    Returns:
    - True if the user has completed onboarding, False otherwise
    """
    return user.is_onboarded
