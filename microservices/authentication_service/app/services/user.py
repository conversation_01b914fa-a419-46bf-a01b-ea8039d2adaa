from datetime import datetime, timedelta, timezone
from typing import Optional

from fastapi import BackgroundTasks, HTTPException, status
from sqlalchemy import func
from sqlalchemy.orm import Session

from app.core.settings import settings
from app.models.model import (
    Invitation,
    InvitationStatus,
    Organisation,
    Permission,
    Role,
    User,
    UserProfile,
    user_organisation_roles,
    user_permissions,
)
from app.schemas.pydantic_schema import (
    InviteeSchema,
    RoleCreate,
    UpdateUserDetailsSchema,
)
from app.schemas.user import OrgUserResponse, UserProfileUpdate, UserResponse
from app.services.roles_permissions import create_role
from app.utils.email_service import EmailService
from app.utils.logger import get_logger
from app.utils.security import (
    create_verification_token,
    generate_temporary_password,
    get_password_hash,
    verify_verification_token,
)
from app.events.publishers import publish_user_invited

logger = get_logger(__name__)


async def generate_invitation_link(
    invitee: InviteeSchema,
    user: User,
    db_session: Session,
    background_tasks: BackgroundTasks,
):
    """generate an invitation link for users"""
    try:
        logger.info("Generating url invitation for a new user")
        # check if inviter has permission for the new user role creation
        inviter_role = get_inviter_role(
            user, invitee.organisation_id, db_session)
        if inviter_role != "owner" and invitee.role_name == "admin":
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail=f'As an {inviter_role}, You do not have the permission to create an {invitee.role_name}')
        db_org = get_organisation(invitee.organisation_id, db_session)
        db_invitee_role = get_or_create_role(invitee, db_org, db_session)

        db_invitee_user, temporary_password = get_or_create_invitee_user(invitee, db_session)
        add_user_to_organisation(
            db_invitee_user, db_invitee_role,
            invitee.organisation_id, db_session
        )
        assign_permissions_to_user(
            db_invitee_user, invitee.permissions, db_org, db_session
        )

        logger.info("Generating and sending an invite token for invitee")
        invite_link = create_verification_token(invitee.email)

        new_invitation = create_invitation_record(
            user=user,
            link=invite_link,
            db_org=db_org,
            db_invitee_user=db_invitee_user,
            db_invitee_role=db_invitee_role,
            db_session=db_session
        )
        await send_invitation_email(
            invitee=invitee,
            inviter=user,
            db_org=db_org,
            invitee_role=db_invitee_role.name,
            temp_password=temporary_password,
            background_tasks=background_tasks,
            inviter_role=inviter_role,
            invite_link=invite_link,
            invitation=new_invitation)

        db_session.commit()
        # send notification
        try:
            await publish_user_invited(
                organisation_id=db_org.id,
                invitee={
                    "first_name": invitee.first_name,
                    "last_name": invitee.last_name,
                    "email": invitee.email,
                    "user_id": db_invitee_user.id
                },
                inviter_name=user.email,
                inviter_id=user.id,
                role_name=db_invitee_role.name,
                organisation_name=db_org.name,
            )
        except Exception as e:
            logger.error(f"Error sending notification: {str(e)}")

        return {"message": "Invitation link sent to invitee"}

    except HTTPException as e:
        db_session.rollback()
        logger.error(f"An error occurred: {str(e)}")
        raise e
    except Exception as e:
        db_session.rollback()
        logger.error(f"An error occurred: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}",
        )


def get_inviter_role(user: User, org_id: str, db_session: Session) -> Role:
    logger.info("Getting the inviter details to determine permissibility")
    inviter_details = (
        db_session.query(user_organisation_roles)
        .filter(
            user_organisation_roles.c.user_id == user.id,
            user_organisation_roles.c.organisation_id == org_id,
        )
        .first()
    )
    logger.info(f"Inviter information: {inviter_details}")
    if not inviter_details:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Inviter is not part of this organisation",
        )
    inviter_role = (
        db_session.query(Role).filter(Role.id == inviter_details.role_id).first()
    )
    if not inviter_role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User does not have any role in the organisation",
        )
    return inviter_role.name


def get_organisation(org_id: str, db_session: Session) -> Organisation:
    logger.debug(f"Verifying that the organisation exists: {org_id}")
    db_org = db_session.query(Organisation).filter(Organisation.id == org_id).first()
    if not db_org:
        logger.error("Organisation id provided does not exist")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Requested organisation not found",
        )
    logger.info(f"Organisation exists: {db_org.name}")
    return db_org


def get_or_create_role(
    invitee: InviteeSchema, db_org: Organisation, db_session: Session
) -> Role:
    logger.info("Fetch or create the role to be assigned to the invitee")
    # confirm role name is not owner
    if invitee.role_name == "owner":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail='Owner role is protected'
        )
    db_invitee_role = (
        db_session.query(Role)
        .filter(
            Role.name == invitee.role_name,
            Role.organisation_id == invitee.organisation_id,
        )
        .first()
    )

    if not db_invitee_role:
        db_permissions = (
            db_session.query(Permission)
            .filter(Permission.id.in_(invitee.permissions))
            .all()
        )
        if not db_permissions or len(db_permissions) != len(invitee.permissions):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="One or more permissions not found",
            )
        db_invitee_role = Role(
            name=invitee.role_name,
            permissions=db_permissions,
            organisation_id=invitee.organisation_id,
        )
        db_session.add(db_invitee_role)
        db_session.flush()
        logger.info(
            f"A new role: {db_invitee_role.name} has been created in the organisation: {db_org.name}"
        )
    return db_invitee_role


def get_or_create_invitee_user(invitee: InviteeSchema, db_session: Session):
    logger.info(
        f"Checking to see if the invitee: {invitee.email} is already a registered user"
    )
    db_invitee_user = db_session.query(User).filter(User.email == invitee.email).first()
    if db_invitee_user:
        logger.debug("Invitee already has an account")
        return db_invitee_user, None
    else:
        logger.info("Invitee doesn't have an account")
        temporary_password = generate_temporary_password()
        db_invitee_user = User(
            email=invitee.email,
            first_name=invitee.first_name,
            last_name=invitee.last_name,
            password=get_password_hash(temporary_password)
        )
        db_session.add(db_invitee_user)
        db_session.flush()
        logger.debug(f"New user created for invitee: {db_invitee_user.email}")
    return db_invitee_user, temporary_password


def add_user_to_organisation(
    db_invitee_user: User, db_invitee_role: Role,
    org_id: str, db_session: Session
):
    logger.info(
        f"Checking to see if user: {db_invitee_user.email} is already a part of the organisation"
    )
    invitee_email = db_invitee_user.email
    invitee_in_org = (
        db_session.query(user_organisation_roles)
        .filter(
            user_organisation_roles.c.user_id == db_invitee_user.id,
            user_organisation_roles.c.organisation_id == org_id
        )
        .first()
    )
    if invitee_in_org:
        # Check for existing pending invitation
        existing_invite = db_session.query(Invitation).filter(
            Invitation.invitee_email == invitee_email,
            Invitation.organisation_id == org_id,
            Invitation.status == "pending",
            Invitation.expires_at > datetime.now(timezone.utc)
        ).first()

        if existing_invite:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User has a pending invite",
            )

        # check for expired pending invites
        existing_invite = db_session.query(Invitation).filter(
            Invitation.invitee_email == invitee_email,
            Invitation.organisation_id == org_id,
            Invitation.status == "pending",
            Invitation.expires_at < datetime.now(timezone.utc)
        ).first()

        if existing_invite:
            remove_user_to_organisation(
                db_session=db_session,
                db_user=db_invitee_user,
                org_id=org_id,
                remove_user=False
            )
            existing_invite.status = InvitationStatus.expired
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User is already a part of the organisation"
            )

    add_invitee_to_org = user_organisation_roles.insert().values(
        user_id=db_invitee_user.id, organisation_id=org_id, role_id=db_invitee_role.id
    )
    db_session.execute(add_invitee_to_org)
    db_session.flush()
    logger.debug(f"New user: {db_invitee_user.email} added to the organisation")


def remove_user_to_organisation(
    db_session, db_user, org_id, remove_user: bool = True
):
    """
    Remove the user from the organisation and optionally delete the user.

    Parameters:
    - db_session: The database session used for executing queries.
    - db_user: The user object to be removed from the organisation.
    - org_id: The ID of the organisation from which the user will be removed.
    - remove_user: A boolean flag indicating whether to delete the user entirely
      if they are not part of any other active organisation.
    """
    # remove the user from the organisation
    db_session.query(user_organisation_roles).filter(
        user_organisation_roles.c.user_id == db_user.id,
        user_organisation_roles.c.organisation_id == org_id
    ).delete(synchronize_session=False)

    # remove the permissions given to the user for that organisation
    db_session.query(user_permissions).filter(
        user_permissions.c.user_id == db_user.id,
        user_permissions.c.organisation_id == org_id
    ).delete(synchronize_session=False)
    if remove_user:
        other_orgs = db_session.query(user_organisation_roles).filter(
            user_organisation_roles.c.user_id == db_user.id,
            user_organisation_roles.c.status == "active"
        ).count()

        if other_orgs == 0 and db_user.status != "onboarded":
            logger.info(f"User {db_user.email} deleted as they are not in any other organisation and have not onboarded.")
            db_session.delete(db_user)


def assign_permissions_to_user(
    db_invitee_user: User, permissions: list,
    db_org: Organisation, db_session: Session
):
    logger.info("Assigning permissions to user")
    db_permissions = (
        db_session.query(Permission).filter(Permission.id.in_(permissions)).all()
    )
    for db_permission in db_permissions:
        db_session.execute(
            user_permissions.insert().values(
                user_id=db_invitee_user.id,
                organisation_id=db_org.id,
                permission_id=db_permission.id,
            )
        )
    logger.info("Permissions assigned to user")


def create_invitation_record(
    user: User,
    link: str,
    db_org: Organisation,
    db_invitee_user: User,
    db_invitee_role: Role,
    db_session: Session,
):
    logger.info("Making a note in Invitation model")
    new_invitation = Invitation(
        user_id=user.id,
        organisation_id=db_org.id,
        invitee_email=db_invitee_user.email,
        role_id=db_invitee_role.id,
        status="pending",
        sent_at=datetime.now(timezone.utc),
        invite_link=link,
        expires_at=datetime.now(timezone.utc) + timedelta(days=7),
    )
    db_session.add(new_invitation)
    logger.info("Invitee information successfully created")
    return new_invitation


async def send_invitation_email(
    invitee: InviteeSchema,
    inviter: User,
    db_org: Organisation,
    background_tasks: BackgroundTasks,
    inviter_role: str,
    invitee_role: str,
    invite_link: str,
    invitation: Invitation,
    temp_password: Optional[str] = None,
):
    email_link = (
            f"https://{settings.FRONTEND_INVITE_LINK}"
            f"?token={invite_link}&team={db_org.name}"
    )
    message = {
        "email": invitee.email,
        "organisation": db_org.name,
        "org_details": db_org.description,
        "link": email_link,
        "password": temp_password,
        "inviter_name": f"{inviter.first_name} {inviter.last_name}",
        "inviter_role": inviter_role,
        "role_name": invitee_role,
        "expires_at": invitation.expires_at.strftime("%b %d, %Y at %I:%M %p")
    }
    await EmailService.send(
        title="Organization Invitation",
        template_name="organization_invitation.html",
        recipients=[invitee.email],
        background_task=background_tasks,
        template_data=message,
    )
    logger.info(f"{message['inviter_name']} invited and email has been sent to invitee: {invitee.email}")


def verify_invite_links(
    token: str,
    response: str,
    db_session: Session,
):
    """Verify the invite link the invitee clicks"""
    try:
        # 1. Validate the token
        if not verify_verification_token(token):
            logger.error("Invalid invitation token")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid Token"
            )

        # 2. Fetch the invitation using the token
        invitation = (
            db_session.query(Invitation)
            .filter(
                Invitation.invite_link == token,
                Invitation.status == "pending",
            )
            .first()
        )
        if not invitation:
            logger.error("Invitation not found or already used/expired")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Invitation not found"
            )

        # 3. Fetch the user using the invitee_email from the invitation
        db_user = db_session.query(User).filter(
            User.email == invitation.invitee_email).first()
        if not db_user:
            logger.error("User not found for the provided email")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
            )

        # 4. Handle decline
        if response == "decline":
            logger.info(f"Invitation declined by user {db_user.email}")
            invitation.status = InvitationStatus.inactive
            invitation.is_valid = False
            invitation.expires_at = datetime.now(timezone.utc)
            remove_user_to_organisation(
                db_session=db_session,
                db_user=db_user,
                org_id=invitation.organisation_id,
                remove_user=True
            )
            db_session.commit()
            return {"message": "User declined joining team."}

        # 5. Handle accept
        if response == "accept":
            # Check if the invitation has expired
            if invitation.expires_at < datetime.now(timezone.utc):
                logger.error("Invitation link has expired")
                invitation.status = InvitationStatus.expired
                # remove user from organisation
                remove_user_to_organisation(
                    db_session=db_session,
                    db_user=db_user,
                    org_id=invitation.organisation_id,
                    remove_user=False
                )
                db_session.commit()
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="The invite link expired, request for another one",
                )
            # if not password or not password.new_password:
            #     raise HTTPException(
            #         status_code=status.HTTP_400_BAD_REQUEST,
            #         detail="Passwords are required",
            #     )
            # Update invitation and user
            invitation.is_valid = False
            invitation.status = InvitationStatus.active
            invitation.accepted_at = datetime.now(timezone.utc)
            db_session.query(user_organisation_roles).filter(
                user_organisation_roles.c.user_id == db_user.id,
                user_organisation_roles.c.organisation_id == invitation.organisation_id,
                user_organisation_roles.c.status == "inactive"
            ).update({user_organisation_roles.c.status: "active"})

            db_user.is_verified = True
            db_user.is_active = True
            # create profile for the user
            get_or_create_user_profile(
                db=db_session,
                user_details=db_user,
                organisation_id=invitation.organisation_id)

            db_session.commit()
            logger.info("User and invitation status updated successfully")
            return {"message": "User added to the organisation successfully"}

        # 6. Handle unknown response
        logger.error(f"Unprocessed status message: {response}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSED_ENTITY,
            detail="Unprocessed status message",
        )

    except HTTPException as e:
        db_session.rollback()
        logger.error(f"HTTPException occurred: {str(e)}")
        raise e
    except Exception as e:
        db_session.rollback()
        logger.error(f"An unexpected error occurred: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}",
        )


def fetch_team_members(db_session: Session, org_id: str, current_user: User):
    """Returns all the members in an organisation"""
    # edge case.
    # Only member of an organisation can get all members and they need to have a specific permission
    try:
        # get the organisation, then get all users that are related to that orgaisation
        logger.info("Start: Fetching all users in an organisation")
        logger.debug("Verifying the availability of the organisation")
        db_org = get_organisation(org_id, db_session)
        logger.info("Organisation authenticity verified")
        logger.info("Verifying current user is part of organisation")
        current_user_in_org = (
            db_session.query(User, User.id)
            .join(user_organisation_roles, User.id == user_organisation_roles.c.user_id)
            .filter(
                user_organisation_roles.c.user_id == current_user.id,
                user_organisation_roles.c.organisation_id == org_id,
            )
            .first()
        )
        if not current_user_in_org:
            logger.error(f"User: {current_user.email} not part of organisation")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You do not have permission for this resource",
            )
        logger.info(f"User: {current_user.email} is part of this organisation")
        logger.info("Fetching all users in the organisation")
        # if org is found then find the users
        db_users = (
            db_session.query(
                User,
                Role.name.label("role_name"),
                Permission.title.label("permission_title"),
            )
            .join(user_organisation_roles, User.id == user_organisation_roles.c.user_id)
            .join(Role, Role.id == user_organisation_roles.c.role_id)
            .outerjoin(user_permissions, User.id == user_permissions.c.user_id)
            .outerjoin(Permission, Permission.id == user_permissions.c.permission_id)
            .filter(
                user_organisation_roles.c.organisation_id == db_org.id,
                user_organisation_roles.c.status == "active",
            )
            .all()
        )
        # Group permissions by user
        user_permissions_map = {}
        for user, role_name, permission_title in db_users:
            if user.id not in user_permissions_map:
                user_permissions_map[user.id] = {
                    "user": user,
                    "role_name": role_name,
                    "permissions": [],
                }
            user_permissions_map[user.id]["permissions"].append(permission_title)
        logger.info(
            f"Organisation: {db_org.name} users fetched. count: {len(user_permissions_map)}"
        )
        return [
            OrgUserResponse(
                id=user_info["user"].id,
                first_name=user_info["user"].first_name,
                last_name=user_info["user"].last_name,
                email=user_info["user"].email,
                role_name=user_info["role_name"],
                permissions=user_info["permissions"],
            )
            for user_info in user_permissions_map.values()
        ]
    except HTTPException as e:
        logger.error(f"An HTTPException error occurred during fetching: {str(e)}")
        raise e
    except Exception as e:
        logger.error(f"An error occurred during fetching: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}",
        )


def delete_team_member(
    db_session: Session, org_id: str, user_id: str, current_user: User
):
    """Removes a user from a specific team"""
    try:
        # check org exists
        # function needs to be protected, only admin or one with permissions to remove users can use this
        logger.info("Start: Deleting a user from an org")
        logger.debug("Verifying the existence of the organisation")
        get_organisation(org_id, db_session)
        # check user exists
        logger.info("Organisation exists, verifying existence of user")

        logger.info("Verifying current user is part of organisation")
        current_user_in_org = (
            db_session.query(User, User.id)
            .join(user_organisation_roles, User.id == user_organisation_roles.c.user_id)
            .filter(
                user_organisation_roles.c.user_id == current_user.id,
                user_organisation_roles.c.organisation_id == org_id,
            )
            .first()
        )
        if not current_user_in_org:
            logger.error(f"User: {current_user.email} not part of organisation")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You do not have permission for this resource",
            )
        logger.info(f"User: {current_user.email} is part of this organisation")
        # check user exists in that organisation
        # FIXME: Convert the usage of secondary tables into using models
        # add a check to ensure user is not the organisation owner
        org_owner = (
            db_session.query(user_organisation_roles)
            .filter(
                user_organisation_roles.c.organisation_id == org_id,
                user_organisation_roles.c.user_id == user_id,
                user_organisation_roles.c.is_owner is True,
            )
            .first()
        )
        if org_owner:
            logger.error(f"User: {user_id} is the owner of this organisation")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You cannot remove the owner of this organisation",
            )

        logger.info("Attempting to soft delete user account: i.e user=inactive")
        user_exists = (
            db_session.query(user_organisation_roles)
            .filter(
                user_organisation_roles.c.user_id == user_id,
                user_organisation_roles.c.organisation_id == org_id,
            )
            .update({user_organisation_roles.c.status: "inactive"})
        )
        if not user_exists:
            logger.error("User does not exists in the organisation")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User is not part of this organisation",
            )

        db_session.commit()
        logger.debug("User account deleted")
        logger.info("End: Deleting a user in an organisation completed")
        return {"message": "User account deleted successfully"}
    except HTTPException as e:
        logger.error(f"An HTTPException error occurred: {str(e)}")
        raise e
    except Exception as e:
        logger.error(f"An Exception Error occurred: {str(e)}")
        db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}",
        )


def fetch_team_member(
    db_session: Session, org_id: str, user_id: str, current_user: User
):
    """Fetches a user from a specific team by organization and user ID"""
    try:
        logger.info("Start: Fetching a specific user details")
        logger.info("Verifying that organisation exists")
        db_org = get_organisation(org_id, db_session)
        logger.debug("Organisation exists")
        # confirm if the requester is in the organisation
        logger.info("Verifying current user is part of organisation")
        current_user_in_org = (
            db_session.query(User, User.id)
            .join(user_organisation_roles, User.id == user_organisation_roles.c.user_id)
            .filter(
                user_organisation_roles.c.user_id == current_user.id,
                user_organisation_roles.c.organisation_id == org_id,
            )
            .first()
        )
        if not current_user_in_org:
            logger.error(f"User: {current_user.email} not part of organisation")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You do not have permission for this resource",
            )
        logger.info(f"User: {current_user.email} is part of this organisation")

        # if org is found then find the users
        logger.info("Fetching requested user")
        db_user = (
            db_session.query(
                User,
                Role.name.label("role_name"),
                func.array_agg(Permission.title).label("permissions")
            )
            .select_from(user_organisation_roles)
            .join(Role, user_organisation_roles.c.role_id == Role.id)
            .join(User, user_organisation_roles.c.user_id == User.id)
            .outerjoin(user_permissions, user_permissions.c.user_id == User.id)
            .outerjoin(Permission, user_permissions.c.permission_id == Permission.id)
            .filter(
                user_organisation_roles.c.organisation_id == db_org.id,
                user_organisation_roles.c.user_id == user_id,
                user_organisation_roles.c.status == "active",
            )
            .group_by(User.id, Role.name)
            .first()
        )
        if not db_user:
            logger.error("Requested user does not exist")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found in the organisation",
            )

        user, role_name, permissions = db_user
        logger.info(f"End: Fetching of a specific user completed\n{user}: {role_name}")
        return OrgUserResponse(
            id=user.id,
            first_name=user.first_name,
            last_name=user.last_name,
            email=user.email,
            role_name=role_name,
            permissions=permissions if permissions else [],
        )
    except HTTPException as e:
        logger.error(f"An HTTPException occurred: {str(e)}")
        raise e
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}",
        )


def update_user_details_in_organisation(
    update_schema: UpdateUserDetailsSchema,
    user_id: str,
    org_id: str,
    db_session: Session,
    current_user: User,
):
    """Update user details in an organization"""
    try:
        db_org = get_organisation(org_id, db_session)
        db_user = db_session.query(User).filter(User.id == user_id).first()
        if not db_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Requested user not found"
            )

        user_in_org = (
            db_session.query(user_organisation_roles)
            .filter(
                user_organisation_roles.c.user_id == db_user.id,
                user_organisation_roles.c.organisation_id == org_id,
                user_organisation_roles.c.status == "active",
            )
            .first()
        )
        if not user_in_org:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Requested User is not part of this organization",
            )
        current_user_in_org = (
            db_session.query(User, User.id)
            .join(user_organisation_roles, User.id == user_organisation_roles.c.user_id)
            .filter(
                user_organisation_roles.c.user_id == current_user.id,
                user_organisation_roles.c.organisation_id == org_id,
                user_organisation_roles.c.status == "active",
            )
            .first()
        )
        if not current_user_in_org:
            logger.error(f"User: {current_user.email} not part of organisation")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You do not have permission for this resource",
            )

        if (update_schema.first_name and update_schema.first_name.strip()) or (update_schema.last_name and update_schema.last_name.strip()):
            data = UserProfileUpdate(
                full_name=f"{update_schema.first_name if update_schema.first_name else ''} {update_schema.last_name if update_schema.last_name else ''}".strip()
            )
            update_user_profile(
                db=db_session,
                user_details=db_user,
                new_profile=data,
                organisation_id=org_id)

        if update_schema.permissions:
            db_session.execute(
                user_permissions.delete().where(
                    user_permissions.c.user_id == db_user.id,
                    user_permissions.c.organisation_id == db_org.id,
                )
            )
            db_permissions = (
                db_session.query(Permission)
                .filter(Permission.id.in_(update_schema.permissions))
                .all()
            )
            if not db_permissions or len(db_permissions) != len(
                update_schema.permissions
            ):
                logger.error("Some permissions id provided are invalid")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="One or more permissions not found",
                )
            for db_permission in db_permissions:
                db_session.execute(
                    user_permissions.insert().values(
                        user_id=db_user.id,
                        organisation_id=db_org.id,
                        permission_id=db_permission.id,
                    )
                )
        if update_schema.role_name:
            db_role = (
                db_session.query(Role)
                .filter(
                    Role.name == update_schema.role_name, Role.organisation_id == org_id
                )
                .first()
            )
            if not db_role:
                role_details = RoleCreate(name=update_schema.role_name, org_id=org_id)
                new_role = create_role(role_details, db_session)
            db_session.query(user_organisation_roles).filter(
                user_organisation_roles.c.user_id == db_user.id,
                user_organisation_roles.c.organisation_id == org_id,
            ).update({user_organisation_roles.c.role_id: new_role.id})

        db_session.commit()

        return {
            "message": "User details successfully updated",
            "data": UserResponse.model_validate(db_user),
        }

    except HTTPException as e:
        logger.error(f"An HTTPException occurred: {str(e)}")
        raise e
    except Exception as e:
        db_session.rollback()
        logger.error(f"An error occurred: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}",
        )


def fetch_users_with_permission_or_admin(
    db_session: Session, org_id: str, permission_title: str
):
    """Fetch users with a specific permission or admin role in an organization"""
    try:
        logger.info("Start: Fetching users with specific permission or admin role")

        # Fetch users with the specified permission
        users_with_permission = (
            db_session.query(User)
            .join(user_permissions, user_permissions.c.user_id == User.id)
            .join(Permission, user_permissions.c.permission_id == Permission.id)
            .filter(
                user_permissions.c.organisation_id == org_id,
                Permission.title == permission_title,
            )
            .all()
        )

        # Fetch users with the admin role
        users_with_admin_role = (
            db_session.query(User)
            .join(user_organisation_roles, user_organisation_roles.c.user_id == User.id)
            .join(Role, user_organisation_roles.c.role_id == Role.id)
            .filter(
                user_organisation_roles.c.organisation_id == org_id,
                Role.name.in_(["admin", "owner"]),
            )
            .all()
        )

        # Combine the results and remove duplicates
        users = list(set(users_with_permission + users_with_admin_role))

        logger.info(
            "End: Fetching users with specific permission or admin role completed"
        )
        return users
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}",
        )


def fetch_user_permissions(user_id: str, organisation_id: str, db_session: Session):
    """Returns the role and all the permissions associated with a user within an organization context"""
    try:
        logger.info("Start: Fetching user role and permissions")
        logger.info("Searching for user")
        db_user = db_session.query(User).filter_by(id=user_id).first()
        if not db_user:
            logger.error("User does not exist")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
            )
        logger.debug(
            f"Fetching role and permissions for user: {user_id} in organization: {organisation_id}"
        )

        # Fetch the user's role in the specified organization
        user_role = (
            db_session.query(Role)
            .join(user_organisation_roles, user_organisation_roles.c.role_id == Role.id)
            .filter(
                user_organisation_roles.c.user_id == user_id,
                user_organisation_roles.c.organisation_id == organisation_id,
            )
            .first()
        )

        if not user_role:
            logger.error("User does not have a role in the organization")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User does not have any role in the organization",
            )

        if user_role.name == "admin" or user_role.name == "owner":
            # Fetch all permissions for the organization if the user is an admin
            permissions = db_session.query(Permission.title).all()
        else:
            # Fetch the user's permissions for this organization
            permissions = (
                db_session.query(Permission.title)
                .join(
                    user_permissions, user_permissions.c.permission_id == Permission.id
                )
                .filter(
                    user_permissions.c.user_id == user_id,
                    user_permissions.c.organisation_id == organisation_id,
                )
                .all()
            )
        permissions = [permission.title for permission in permissions]
        logger.debug(f"User role: {user_role.name}, permissions fetched: {permissions}")

        logger.info("End: Fetching user role and permissions completed")
        return {"role": user_role.name, "permissions": permissions}
    except HTTPException as e:
        logger.error(f"An HTTPException error occurred: {str(e)}")
        raise e
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}",
        )


def get_user_details(user_id: str, db_session: Session):
    """Returns the details of a user"""
    try:
        logger.info("Start: Fetching user details")
        db_user = db_session.query(User).filter_by(id=user_id).first()
        if not db_user:
            logger.error("User does not exist")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
            )
        logger.info("End: Fetching user details completed")
        return UserResponse.model_validate(db_user)
    except HTTPException as e:
        logger.error(f"An HTTPException error occurred: {str(e)}")
        raise e
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}",
        )


# def update_user(
#     user_id: str,
#     user_details: UpdateUserDetailsSchema,
#     db_session: Session
# ):
#     '''Update a user details'''
#     try:
#         # confirm the user exists
#         db_user = db_session.query(User).filter_by(id=user_id).first()
#         if not db_user:
#             raise HTTPException(
#                 status_code=status.HTTP_404_NOT_FOUND,
#                 detail='Requested user not found'
#             )


#         # get what to update
def fetch_user_roles(user_id: str, organisation_id: str, db_session: Session):
    """Returns all the roles of a user"""
    try:
        logger.debug("Fetching user roles")
        user_roles = (
            db_session.query(Role.name)
            .join(user_organisation_roles, user_organisation_roles.c.role_id == Role.id)
            .filter(
                user_organisation_roles.c.user_id == user_id,
                user_organisation_roles.c.organisation_id == organisation_id,
            )
            .all()
        )
        user_roles = [role.name for role in user_roles]
        logger.debug(f"User roles fetched: {user_roles}")
        logger.info("End: Fetching user roles completed")
        return user_roles
    except HTTPException as e:
        logger.error(f"An HTTPException error occurred: {str(e)}")
        raise e
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}",
        )


# User Profile CRUD Operations

def get_user_role_in_organisation(db, user_id, organisation_id):
    # Query the association table for the role_id
    result = db.execute(
        user_organisation_roles.select().where(
            (user_organisation_roles.c.user_id == user_id) &
            (user_organisation_roles.c.organisation_id == organisation_id)
        )
    ).first()
    if result and result.role_id:
        # Fetch the Role object
        role = db.query(Role).filter(Role.id == result.role_id).first()
        return role.name if role else None
    return None


def get_or_create_user_profile(
    db: Session,
    user_details: User,
    organisation_id: str
):
    try:
        # validate organisation_id
        org_deets = db.query(Organisation).filter_by(id=organisation_id).first()
        if not org_deets:
            raise HTTPException(
                status_code=400,
                detail="The requested organisation does not exists."
            )

        # Try to find existing profile
        instance = db.query(UserProfile).filter(
            UserProfile.user_id == user_details.id,
            UserProfile.organisation_id == organisation_id
        ).first()

        if not instance:
            logger.info(f"No user profile found for user {user_details.email} in organisation {org_deets.name}\ncreating a new one")

            # Extract name from user_details
            user_info = user_details.to_dict()
            first_name = user_info.get('first_name', '')
            last_name = user_info.get('last_name', '')
            full_name = f"{first_name} {last_name}".strip()

            user_role = get_user_role_in_organisation(db, user_details.id, organisation_id)

            profile_data = {
                "user_id": user_details.id,
                "organisation_id": organisation_id,
                "full_name": full_name or user_details.email.split('@')[0],
                "email_address": user_details.email,
                "phone_number": user_info.get('phone_number', ''),
                "user_role": user_role,
                "img_url": user_info.get('avatar_url', ''),
                "bio": ""
            }

            instance = UserProfile(**profile_data)
            db.add(instance)
            db.commit()
            db.refresh(instance)
        return instance
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")


def update_user_profile(
    db: Session,
    user_details: User,
    new_profile: UserProfileUpdate,
    organisation_id: str
):
    try:
        # Get the user profile first
        instance = db.query(UserProfile).filter(
            UserProfile.user_id == user_details.id,
            UserProfile.organisation_id == organisation_id
        ).first()

        if not instance:
            logger.error(f"User profile for {user_details.email} not found")
            raise HTTPException(status_code=404, detail="User profile not found")

        # Update only the fields that are provided
        for key, value in new_profile.model_dump(exclude_unset=True).items():
            setattr(instance, key, value)

        db.commit()
        db.refresh(instance)
        return instance
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")
