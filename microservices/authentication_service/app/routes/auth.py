from app.core.settings import settings
from app.database.session import get_db
from app.models.model import Organisation, Permission, Role, User
from app.schemas.pydantic_schema import (
    AddUpdateOrganisationRole,
    CreateUpdateOrganisation,
    InviteeSchema, PermissionAssignRole,
    PermissionAssignUser,
    PermissionCreate, RoleCreate,
    UpdateOrganisation,
    UpdateUserDetailsSchema
)
from app.schemas.user import (
    ChangePasswordSchema, EmailRequest,
    InvitedPasswordSchema, LoginRequest,
    OrgUserResponse, ResetPasswordSchema,
    SetOauthUserPassword, UserCreate, UserResponse,
    VerifyToken
)
from app.services import auth, roles_permissions, user, waitlist
from app.services.admin import (
    get_orgs_admin, get_perms_admin,
    get_roles_admin, get_users_admin
)
from app.utils.decorators import check_permission
from app.utils.email_service import EmailService
from app.utils.external_calls import send_notification_request
from app.utils.logger import get_logger
from app.utils.success_response import (
    auth_response, fail_response,
    success_response
)
from app.events.publishers import (
    publish_user_registered,
    publish_user_login,
    publish_organization_created,
    publish_organization_updated,
)
from fastapi import (
    APIRouter, BackgroundTasks,
    Depends, HTTPException,
    Request, status
)
from fastapi.responses import RedirectResponse
from sqlalchemy.orm import Session

router = APIRouter()
logger = get_logger(__name__)


# create roles and permissions
@router.post("/roles", tags=["Role"])
async def createRole(role: RoleCreate, db_session: Session = Depends(get_db)):
    try:
        response = roles_permissions.create_role(role, db_session)
        return success_response(200, "Role created successfully", response)
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        return fail_response(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            message="An unexpected error occurred. Please try again later.",
        )


@router.get("/roles", tags=["Role"])
async def getRoles(org_id: str, db_session: Session = Depends(get_db)):
    try:
        response = roles_permissions.get_roles(db_session, org_id)
        return success_response(200, "Roles fetched successfully", response)
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(500, str(e.args[0]))


@router.get("/roles/{role_id}", tags=["Role"])
async def getRole(role_id: str, db_session: Session = Depends(get_db)):
    try:
        response = roles_permissions.get_role(db_session=db_session, role_id=role_id)
        return success_response(200, "Role fetched successfully", response)
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(500, str(e.args[0]))


@router.post("/permissions", tags=["Permission"])
async def createPermission(
    permission: PermissionCreate, db_session: Session = Depends(get_db)
):
    try:
        response = roles_permissions.create_permission(permission, db_session)
        return success_response(200, "Permission created successfully", response)
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(500, str(e.args[0]))


@router.delete("/permissions/{permission_id}", tags=["Permission"])
async def deletePermission(
    permission_id: str, db_session: Session = Depends(get_db)
):
    try:
        response = roles_permissions.delete_permission(
            permission_id=permission_id,
            db_session=db_session
        )
        return success_response(200, "Permission deleted successfully", response)
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(500, str(e.args[0]))


@router.get("/permissions", tags=["Permission"])
async def getPermissions(db_session: Session = Depends(get_db)):
    try:
        response = roles_permissions.get_permissions(db_session)
        return success_response(200, "Permissions fetched successfully", response)
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(500, str(e.args[0]))


@router.get("/permissions/{permission_id}", tags=["Permission"])
async def getPermission(permission_id: str, db_session: Session = Depends(get_db)):
    try:
        response = roles_permissions.get_permission(
            db_session=db_session, permission=permission_id
        )
        return success_response(200, "Permission fetched successfully", response)
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(500, str(e.args[0]))


@router.post("/add-perms-to-roles", tags=["Roles-Permission"])
async def add_perm_to_role(
    permissions_data: PermissionAssignRole, db_session: Session = Depends(get_db)
):
    try:
        response = roles_permissions.add_permissions_to_role(
            permission=permissions_data, db_session=db_session
        )
        return success_response(200, "Permissions added to role successfully", response)
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(500, str(e.args[0]))


@router.post("/add-perms-to-user", tags=["Roles-Permission"])
async def add_perm_to_user(
    permissions_data: PermissionAssignUser, db_session: Session = Depends(get_db)
):
    try:
        response = roles_permissions.add_permissions_to_user(
            permission=permissions_data, db_session=db_session
        )
        return success_response(200, response["message"])
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(500, str(e.args[0]))


@router.post("/add-role-to-user", tags=["Roles-Permission"])
async def addRolesToUser(
    credentials: AddUpdateOrganisationRole, db_session: Session = Depends(get_db)
):
    try:
        response = roles_permissions.add_role_to_user(credentials, db_session)
        return success_response(200, response["message"])
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(500, str(e.args[0]))


@router.get("/onboarding-status", tags=["User"])
async def check_onboarding_status(current_user: User = Depends(auth.get_current_user)):
    """
    Check if the current user has completed onboarding.

    Returns:
    - A success response with the onboarding status
    """
    try:
        is_onboarded = await auth.has_completed_onboarding(current_user)
        return success_response(
            status_code=status.HTTP_200_OK,
            message="Onboarding status retrieved successfully",
            data={"is_onboarded": is_onboarded},
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            message=f"An unexpected error occurred: {str(e.args[0])}",
        )


@router.post("/register", tags=["User"], response_model=UserResponse)
async def register_user(
    new_user: UserCreate,
    background_tasks: BackgroundTasks,
    request: Request,
    db_session: Session = Depends(get_db),
):
    """
    This endpoint creates a new user and returns the information of the user.
    """
    try:
        # create a new user
        created_user = await auth.create_user(
            db_session, new_user, background_tasks, request
        )

        if not created_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="User creation failed"
            )

        return success_response(
            status_code=status.HTTP_201_CREATED,
            message="New user created successfully, check your mail for verification",
            data=created_user,
        )

    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            message=f"An unexpected error occured: {str(e.args[0])}",
        )


@router.get("/verify_email/{token}", name="verify_email", tags=["User"])
async def verify_email(token: str, db_session: Session = Depends(get_db)):
    """Verify the email of the user"""
    try:
        await auth.verify_mail_service(token, db_session)
        return RedirectResponse(url=f"https://{settings.REDIRECT_URL}")
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)


@router.post("/google_auth", tags=["User"])
async def googleAuth(token: VerifyToken, db_session: Session = Depends(get_db)):
    """Google register or signin"""
    try:
        response = await auth.google_signin(token, db_session)
        return auth_response(
            200, "Signin successfull", response.get("access_token"), response
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception:
        return fail_response(400, "Authentication unsuccessfull")


@router.post("/login", tags=["User"], response_model=UserResponse)
async def login(
    background_tasks: BackgroundTasks,
    login_user: LoginRequest,
    request: Request,
    db_session: Session = Depends(get_db),
):
    """
    This endpoint is responsible for logging in registered users.
    It generates a token which will be used for subsequent requests.
    """
    try:
        response = await auth.authenticate_user(
            login_request=login_user,
            background_tasks=background_tasks,
            db_session=db_session,
            request=request
        )

        return auth_response(
            status_code=status.HTTP_200_OK,
            message="Authentication successfull",
            data=response,
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            message=f"An unexpected error occured: {str(e.args[0])}",
        )


@router.post("/set-password", tags=["User"])
async def set_password_for_oauth_user(
    user_detail: SetOauthUserPassword, db_session: Session = Depends(get_db)
):
    try:
        response = await auth.set_oauth_user_password(user_detail, db_session)
        return success_response(200, response["message"])
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(500, str(e.args[0]))


@router.post("/change_password", tags=["User"])
async def change_password(
    change_password: ChangePasswordSchema,
    db_session: Session = Depends(get_db),
    user: dict = Depends(auth.get_current_user),
):
    """
    Change a logged-in user's password from the old password to a new password.

    Parameters:
    - old_password: Password to change from
    - new_password: Password to change to
    """
    try:
        response = await auth.change_password_service(
            db_session, change_password, user.id
        )

        return success_response(status_code=200, message=response["message"])
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)


@router.post("/forgotten_password", tags=["User"])
async def forgotten_password(
    user_email: EmailRequest,
    background_tasks: BackgroundTasks,
    db_session: Session = Depends(get_db),
):
    """User initites the forgotten password handshake
    This is the step one, where the user email is verified and a mail is sent to the user's email
    """
    try:
        response = await auth.forgot_password_service(
            db_session=db_session,
            email_schema=user_email,
            background_tasks=background_tasks
        )
        return success_response(status_code=200, message=response["message"])
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(500, str(e.args[0]))


@router.post("/resend_token", tags=["User"])
async def resend_forgotten_password_token(
    user_email: EmailRequest,
    background_tasks: BackgroundTasks,
    db_session: Session = Depends(get_db),
):
    """User requests for a new token"""
    try:
        response = await auth.resend_token_service(
            db_session, user_email, background_tasks
        )
        return success_response(status_code=200, message=response["message"])
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(500, str(e.args[0]))


@router.post("/verify_token", tags=["User"])
async def verify_reset_token(
    token: VerifyToken,
    user_email: str,
    db_session: Session = Depends(get_db)
):
    """This is the step 2 of the forgotten password handshake
    the user inputs the token and it is verified"""
    try:
        # 1. Verify the token is valid
        response = await auth.verify_reset_token_service(
            token=token.token,
            user_email=user_email,
            db_session=db_session)

        # 2. return responses
        return success_response(status_code=200, message=response["message"])
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)


@router.post("/reset_password", tags=["User"])
async def reset_forgotten_password(
    reset_password_schema: ResetPasswordSchema,
    request: Request,
    background_tasks: BackgroundTasks,
    db_session: Session = Depends(get_db)
):
    """This is the 3rd and last part of the forgotten password handshake
    user can now provide their new password and confirm new password
    this will then change the password on their account"""
    try:
        response = await auth.reset_password_service(
            db_session=db_session,
            reset_password_schema=reset_password_schema,
            request=request,
            background_tasks=background_tasks)
        return success_response(200, response["message"])
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)


@router.post("/refresh_token", tags=["User"])
async def refresh_token(token: VerifyToken, db_session: Session = Depends(get_db)):
    """rereshes the access token"""
    try:
        response = await auth.refresh_token_service(token, db_session)
        return auth_response(200, response.get("message"), response.get("access_token"))
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)


@router.get('/me', tags=["User"])
async def get_user(
     current_user: User = Depends(auth.get_current_user)
):
    '''Gets a user details'''
    try:
        response = {
            "id": current_user.id,
            "avatar_url": current_user.avatar_url,
            "first_name": current_user.first_name,
            "last_name": current_user.last_name,
            "email": current_user.email,
            "is_active": current_user.is_active,
            "is_onboarded": current_user.is_onboarded,
            "is_verified": current_user.is_verified,
            "status": current_user.status
        }
        return success_response(200, 'User details retrieved', response)
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(500, str(e.args[0]))


@router.post("/org", tags=["Organisation"])
async def create_company(
    org_data: CreateUpdateOrganisation,
    current_user: User = Depends(auth.get_current_user),
    db_session: Session = Depends(get_db),
):
    """
    This creates an adds a users to the org
    """
    try:
        org = await auth.create_organisation_service(org_data, db_session, current_user)
        # Update user status to onboarded if they were invited
        if current_user.status == "invited":
            current_user.status = "onboarded"
            db_session.commit()

        return success_response(201, "Onboarding completed", data={"organization": org})
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(e.status_code, e.detail)


@router.put("/org/{org_id}", tags=["Organisation"])
@check_permission(["edit settings"])
async def update_organisation_details(
    organisation_id: str,
    organisation_details: UpdateOrganisation,
    current_user: User = Depends(auth.get_current_user),
    db_session: Session = Depends(get_db),
):
    """Update an organisation details"""
    try:
        response = await auth.update_organisation(
            organisation_id, current_user.id, organisation_details, db_session
        )

        return success_response(200, "Organisation update successfull", response)
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(500, str(e.args[0]))


@router.get("/user/organisations", tags=["Organisation"])
async def get_user_org(
    current_user: User = Depends(auth.get_current_user),
    db_session: Session = Depends(get_db),
):
    """
    Returns the organisations of the current user
    """
    try:
        org = await auth.get_user_org_service(current_user, db_session)
        return success_response(200, "Organisation returned", org)
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(e.status_code, e.detail)  # check this out


@router.get("/verify-organisation/{organisation_id}", tags=["Organisation"])
async def get_org(organisation_id: str, db_session: Session = Depends(get_db)):
    """
    Check if an organisation exists by its ID.

    Parameters:
    - organisation_id: The ID of the organisation to check

    Returns:
    - A success message if the organisation exists
    - An error message if the organisation does not exist
    """
    try:
        org = user.get_organisation(organisation_id, db_session)
        return success_response(200, "Organisation returned", org)
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(500, str(e.args[0]))


@router.post("/org-share-invite", tags=["Organisation"])
@check_permission(["admin_or_owner"])
async def join_company(
    invitee_data: InviteeSchema,
    request: Request,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(auth.get_current_user),
    db_session: Session = Depends(get_db),
):
    """
    This endpoint allows a user to join an existing organisation with a specific role
    """
    try:
        # make the call to the services
        response = await user.generate_invitation_link(
            invitee=invitee_data,
            user=current_user,
            db_session=db_session,
            background_tasks=background_tasks,
        )

        return success_response(200, response["message"])
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(500, str(e.args[0]))


@router.post("/verify-invite-link", tags=["Organisation"])
async def verifyInviteLink(
    token: str,
    status: str,
    db_session: Session = Depends(get_db),
):
    """
    This endpoint validates the invitation token sent to an invitee
    """
    try:
        # send the details to the service
        response = user.verify_invite_links(token, status, db_session)
        return success_response(200, response.get("message", ""))
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(500, str(e.args[0]))


@router.get(
    "/organisation/{organisation_id}/users",
    tags=["Organisation"],
    response_model=OrgUserResponse,
)
# @check_permission(["admin_or_owner"])
async def get_all_org_users(
    organisation_id: str,
    db_session: Session = Depends(get_db),
    current_user: User = Depends(auth.get_current_user),
):
    """Returns all users in an organisation"""
    try:
        response = user.fetch_team_members(db_session, organisation_id, current_user)
        return success_response(200, "Users details returned", response)
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(500, str(e.args[0]))


@router.get("/organisation/{organisation_id}", tags=["Organisation"])
async def selected_organisation(
    organisation_id: str,
    db_session: Session = Depends(get_db),
    current_user: User = Depends(auth.get_current_user),
):
    """Returns all users in an organisation"""
    try:
        response = await auth.selected_user_org_service(
            db_session=db_session, organisation_id=organisation_id, user=current_user
        )
        return success_response(200, "Organisation selected", response)
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(500, str(e.args[0]))


@router.patch("/organisation/{organisation_id}/{user_id}", tags=["Organisation"])
@check_permission(["admin_or_owner"])
async def soft_delete_org_user(
    organisation_id: str,
    user_id: str,
    db_session: Session = Depends(get_db),
    current_user: User = Depends(auth.get_current_user),
):
    """Soft deletes a user from an organisation"""
    try:
        response = user.delete_team_member(
            db_session, organisation_id, user_id, current_user
        )

        return success_response(200, response["message"])
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(500, str(e.args[0]))


@router.delete("/organisation", tags=["Organisation"])
@check_permission(["owner"])
async def delete_organisation(
    organisation_id: str,
    db_session: Session = Depends(get_db),
    current_user: User = Depends(auth.get_current_user),
):
    """Delete an organisation"""
    try:
        response = await auth.delete_organisation(
            db_session, organisation_id, current_user
        )

        return success_response(200, response["message"])
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(500, str(e.args[0]))


@router.get(
    "/organisation/{organisation_id}/users-with-permission", tags=["Organisation"]
)
async def get_users_with_permission_or_admin(
    organisation_id: str,
    permission: str = "can review",
    db_session: Session = Depends(get_db),
):
    """Fetch users with a specific permission or admin role in an organization"""
    try:
        users = user.fetch_users_with_permission_or_admin(
            db_session, organisation_id, permission
        )
        return success_response(
            200,
            "Users fetched successfully",
            [UserResponse.model_validate(u) for u in users],
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(500, str(e.args[0]))


@router.get("/user/{user_id}/permissions", tags=["User"])
async def get_user_permissions(
    user_id: str, organisation_id: str, db_session: Session = Depends(get_db)
):
    """Returns all permissions associated with a user"""
    try:
        response = user.fetch_user_permissions(user_id, organisation_id, db_session)
        return success_response(200, "User permissions fetched successfully", response)
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(500, str(e.args[0]))


# @router.get("/user/{user_id}", tags=["User"])
# async def get_org_user_details(user_id: str, db_session: Session = Depends(get_db)):
#     """Returns details of a specific user in the organisation"""
#     try:
#         response = user.get_user_details(db_session=db_session, user_id=user_id)
#         return success_response(200, "User detail fetched successfully", response)
#     except HTTPException as e:
#         return fail_response(e.status_code, e.detail)
#     except Exception as e:
#         return fail_response(500, str(e.args[0]))


@router.get("/organisation/{organisation_id}/{user_id}", tags=["Organisation"])
@check_permission(["can read"])
async def get_an_org_user(
    organisation_id: str,
    user_id: str,
    db_session: Session = Depends(get_db),
    current_user: User = Depends(auth.get_current_user),
):
    """Returns details of a specific user in the organisation"""
    try:
        response = user.fetch_team_member(
            db_session, organisation_id, user_id, current_user
        )
        return success_response(200, "User detail fetched successfully", response)
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(500, str(e.args[0]))


@router.put("/organisation/{organisation_id}/{user_id}", tags=["Organisation"])
@check_permission(["can write"])
async def update_an_org_user(
    user_data: UpdateUserDetailsSchema,
    organisation_id: str,
    user_id: str,
    db_session: Session = Depends(get_db),
    current_user: User = Depends(auth.get_current_user),
):
    """Updates the entries of a user"""
    try:
        response = user.update_user_details_in_organisation(
            update_schema=user_data,
            user_id=user_id,
            org_id=organisation_id,
            db_session=db_session,
            current_user=current_user,
        )
        return success_response(200, response["message"], response["data"])
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(500, str(e.args[0]))


# get all users, organisations, all roles, all permissions, invitations,
@router.get("/orgs", tags=["BackDoor"])
async def Get_all_Orgs(db_session: Session = Depends(get_db)):
    try:
        response = get_orgs_admin(db_session)
        return success_response(200, "All orgs returned", data=response)
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(500, str(e.args[0]))


@router.get("/users", tags=["BackDoor"])
async def Get_all_Users(db_session: Session = Depends(get_db)):
    try:
        response = get_users_admin(db_session)
        return success_response(200, "All users returned", data=response)
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(500, str(e.args[0]))


@router.get("/adminroles", tags=["BackDoor"])
async def Get_all_Roles(db_session: Session = Depends(get_db)):
    try:
        response = get_roles_admin(db_session)
        return success_response(200, "All Roles returned", data=response)
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(500, str(e.args[0]))


@router.get("/perms", tags=["BackDoor"])
async def Get_all_Perms(db_session: Session = Depends(get_db)):
    try:
        response = get_perms_admin(db_session)
        return success_response(200, "All perms returned", data=response)
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(500, str(e.args[0]))


@router.get("/waitlist", tags=["WaitList"])
async def Get_all_WaitList_Users(db_session: Session = Depends(get_db)):
    try:
        response = waitlist.get_user_in_waitlist(db_session)
        return success_response(
            200, "All users in the waitlist returned", data=response
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(500, str(e.args[0]))


@router.post("/waitlist", tags=["WaitList"])
async def Add_to_WaitList(email: str, db_session: Session = Depends(get_db)):
    try:
        response = waitlist.add_user_to_waitlist(email, db_session)
        return success_response(
            200, "A new user have been added to waitlist", data=response
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(500, str(e.args[0]))


@router.get("/user/roles/{organisation_id}/{user_id}", tags=["User"])
async def get_user_roles(
    organisation_id: str, user_id: str, db_session: Session = Depends(get_db)
):
    """Returns all roles associated with a user"""
    try:
        response = user.fetch_user_roles(user_id, organisation_id, db_session)
        return success_response(200, "User roles fetched successfully", response)
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(500, str(e.args[0]))

@router.get("/org_details/{organisation_id}")
def get_organisation_info(organisation_id: str, db: Session = Depends(get_db)):
    organisation = db.query(Organisation).filter_by(id=organisation_id).first()
    if not organisation:
        raise HTTPException(404, "Organisation not found")
    return {
        "id": organisation.id,
        "name": organisation.name,
        "industry": organisation.industry,
        "description": organisation.description,
        "company_size": organisation.company_size,
        "target_audience": organisation.target_audience,
        "brand_voice": organisation.brand_voice,
        "business_tone": organisation.business_tone,
        "primary_color": organisation.primary_color,
        "brand_logo": organisation.brand_logo,
        "competitors": [
            {"name": c.competitor_name, "link": c.competitor_link, "data": c.data} 
            for c in organisation.competitors
        ],
        "teams": [
            {"name": t.name, "role": t.role, "description": t.description, "picture_url": t.picture_url, "team_type": t.team_type} 
            for t in organisation.teams
        ]
    }


@router.get("/organisation/{organisation_id}/user/{user_id}")
def get_user_info(organisation_id: str, user_id: str, db: Session = Depends(get_db)):
    user_roles = user.fetch_user_roles(user_id, organisation_id, db)
    db_user = db.query(User).filter(User.id == user_id, User.organisations.any(id=organisation_id)).first()
    if not db_user:
        raise HTTPException(status_code=404, detail="User not found in this organisation")

    user_permissions = db.query(Permission).filter(Permission.users.any(id=user_id)).all()
    return {
        "email": db_user.email,
        "first_name": db_user.first_name,
        "last_name": db_user.last_name,
        "avatar_url": db_user.avatar_url,
        "is_active": db_user.is_active,
        "status": db_user.status,
        "roles": user_roles,
        "permissions": [perm.title for perm in user_permissions]
    }
