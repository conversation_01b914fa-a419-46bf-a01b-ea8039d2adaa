from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.database.session import get_db
from app.models.model import User
from app.services import user, auth
from app.schemas.user import UserProfilePublic, UserProfileUpdate
from app.utils.logger import get_logger
from app.utils.success_response import fail_response

router = APIRouter()
logger = get_logger(__name__)


# User Profile Routes
@router.get("/profile", response_model=UserProfilePublic, tags=["User"])
def read_user_profile(
    organization_id: str,
    user_details: User = Depends(auth.get_current_user),
    db: Session = Depends(get_db),
):
    try:
        logger.info(f"Getting user profile for user: {user_details.email}")
        return user.get_or_create_user_profile(
            db=db,
            user_details=user_details,
            organisation_id=organization_id
        )
    except HTTPException as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        return fail_response(500, "An unexpected error occurred")


@router.put("/profile", response_model=UserProfilePublic, tags=["User"])
def update_user_profile(
    user_details: Annotated[User, Depends(auth.get_current_user)],
    organization_id: str,
    new_profile: UserProfileUpdate,
    db: Session = Depends(get_db),
):
    try:
        logger.info(f"Updating user profile for user: {user_details.email}")
        updated = user.update_user_profile(
            db=db,
            user_details=user_details,
            new_profile=new_profile,
            organisation_id=organization_id
        )
        if not updated:
            raise HTTPException(status_code=404, detail="User profile not found")
        return updated
    except HTTPException as e:
        logger.error(f"{e.detail}: {str(e)}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        return fail_response(500, "An unexpected error occurred")
