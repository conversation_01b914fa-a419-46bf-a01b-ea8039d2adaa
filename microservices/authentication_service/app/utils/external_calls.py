import httpx
from fastapi import HTTPException
from app.core.settings import settings

SETTINGS_SERVICE_URL = settings.SETTINGS_SERVICE_URL
timeout = httpx.Timeout(10.0, connect=10.0, read=15.0, write=20.0)


async def send_notification_request(
    organization_id: str,
    message: str,
    auth_token: str,
    timeout: int = 10,
) -> dict:
    """
    Sends a notification to the /notify endpoint.

    Args:
        organization_id (str): The ID of the organization.
        message (str): The notification message.
        auth_token (str): The JWT authentication token.
        api_url (str): The base URL of the API service.
        timeout (int): The timeout for the request in seconds.

    Returns:
        dict: The response from the /notify endpoint.

    Raises:
        HTTPException: If sending the notification fails.
    """
    try:
        headers = {"Authorization": f"Bearer {auth_token}"}
        payload = {"organization_id": organization_id, "message": message}

        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{SETTINGS_SERVICE_URL}/notify",
                json=payload,
                headers=headers,
                timeout=timeout,
            )
            response.raise_for_status()
            return response.json()

    except httpx.HTTPStatusError as e:
        error_details = e.response.json()
        raise HTTPException(
            status_code=e.response.status_code,
            detail=error_details.get("message", "Failed to send notification."),
        )
    except httpx.RequestError as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error connecting to the notification service: {str(e)}",
        )
