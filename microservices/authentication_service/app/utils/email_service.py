from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, BackgroundTasks
from app.schemas.user import EmailSchema
from fastapi_mail import MessageSchema, ConnectionConfig, FastMail
from typing import List
from app.core.settings import settings
import logging

logging.basicConfig(level=logging.INFO)

conf = ConnectionConfig(
    MAIL_USERNAME=settings.MAIL_USERNAME,
    MAIL_PASSWORD=settings.MAIL_PASSWORD,
    MAIL_FROM=settings.MAIL_FROM,
    MAIL_FROM_NAME=settings.MAIL_FROM_NAME,
    MAIL_PORT=settings.MAIL_PORT,
    MAIL_SERVER=settings.MAIL_SERVER,
    MAIL_STARTTLS=True,
    MAIL_SSL_TLS=False,
    USE_CREDENTIALS=True,
    VALIDATE_CERTS=True,
    TEMPLATE_FOLDER=settings.TEMPLATE_FOLDER,
    # SUPPRESS_SEND=True
)


class EmailService:
    def __init__(self) -> None:
        pass

    @classmethod
    async def send(
        cls,
        title: str,
        template_name: str,
        recipients: List[str],
        background_task: BackgroundTasks,
        template_data: dict,
        email_type: str = "default",
        attachments: List[str] = None,
    ):
        if attachments:
            message_schema = MessageSchema(
                subject=title,
                recipients=recipients,
                template_body=template_data,
                subtype="html",
                attachments=attachments,
            )
        else:
            message_schema = MessageSchema(
                subject=title,
                recipients=recipients,
                template_body=template_data,
                subtype="html",
            )
        fm = FastMail(conf)
        if settings.ENV == "DEVELOPMENT":
            print('Development Email Handler')
            logging.info(message_schema)
        background_task.add_task(
            fm.send_message, message_schema, template_name=template_name
        )
