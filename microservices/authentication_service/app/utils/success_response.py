from typing import Optional
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder


def success_response(status_code: int, message: str, data: Optional[dict] = None):
    '''Returns a JSON response for success responses'''

    response_data = {
        "status_code": status_code,
        "success": True,
        "message": message
    }

    if data is not None:
        response_data["data"] = data

    return JSONResponse(status_code=status_code, content=jsonable_encoder(response_data))


def auth_response(status_code: int, message: str, access_token: Optional[str] = None, data: Optional[dict] = None):
    '''Returns a JSON response for successful auth responses'''

    response_data = {
        "status_code": status_code,
        "message": message
    }

    if data is not None:
        response_data["data"] = data
    if access_token is not None:
        response_data["access_token"] = access_token

    return JSONResponse(status_code=status_code, content=jsonable_encoder(response_data))


def fail_response(status_code: int, message: str, data: Optional[dict] = None):
    '''Returns a JSON response for failed responses'''

    response_data = {
        "status_code": status_code,
        "success": False,
        "message": message
    }
    
    if data is not None:
        response_data["data"] = data

    return JSONResponse(status_code=status_code, content=jsonable_encoder(response_data))