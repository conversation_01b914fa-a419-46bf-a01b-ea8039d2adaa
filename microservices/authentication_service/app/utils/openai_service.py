import json
import re

from google import genai
import httpx
from app.core.settings import settings
from app.utils.logger import get_logger
from fastapi import HTTPException

logger = get_logger(__name__)

client = genai.Client(api_key=settings.GEMINI_API_KEY)


# setup the gen AI
async def generate_roles_with_gemini(prompt: str):
    """Query gemini to generate roles"""
    try:
        logger.info("starting the call to gemini")
        response = client.models.generate_content(model="gemini-2.0-flash", contents=prompt)
        # Remove markdown code block indicators if present
        response_text = response.text.strip()
        response_text = re.sub(r"```(json)?", "", response_text)
        response_text = re.sub(r"```", "", response_text)

        return response_text
    except Exception as e:
        logger.error(f"an exception occurred: {str(e)}")
        raise HTTPException(
            status_code=400,
            detail="An error occurred while requesting",
        )


async def query_openai(prompt: str) -> str:
    """
    Query the OpenAI API with a given prompt and return the response.
    """
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                "https://api.openai.com/v1/chat/completions",
                headers={"Authorization": f"Bearer {settings.OPENAI_API_KEY}"},
                json={
                    "model": "gpt-4o-mini",
                    "messages": [
                        {
                            "role": "system",
                            "content": """
                            You are an intelligent assistant that uses a combination of a predefined knowledge base and real-time
                            search to provide accurate, concise, and context-aware responses without hallucinations,
                            while remembering past conversations and ensuring coherence in tone and style.
                            You are a highly concise assistant. Respond to the query with only the necessary information,
                            avoiding filler phrases like "Sure" or "If you need more help.
                            """,
                        },
                        {"role": "user", "content": prompt},
                    ],
                },
                timeout=60,
            )
            response.raise_for_status()
            response_data = response.json()
        except httpx.HTTPStatusError as e:
            # Handle specific HTTP errors
            logger.error(f"Request Error: {e.response.json()}")
            raise HTTPException(
                status_code=e.response.status_code,
                detail=f"{e.response.json()['error']['message']}",
            )
        except httpx.RequestError as e:
            # Handle request errors
            logger.error(f"Request error: {e.response.json()}")
            raise HTTPException(
                status_code=e.response.status_code,
                detail="An error occurred while requesting",
            )
        return response_data["choices"][0]["message"]["content"].strip()


async def generate_roles(data):
    """
    Generates role suggestions based on onboarding data.
    """
    prompt = f"""
        Business Name: {data.name}
        Industry: {data.industry}
        Business Size: {data.company_size}
        Business Description: {data.description}
        Target Audience: {data.target_audience}

        Based on the above information, suggest roles that fit this organization. For each role, provide a short description.

        Respond with ONLY a JSON array of objects, without any extra text, formatting, or markdown. Each object should have two keys: "role" and "description". Do not wrap the response in backticks or any other syntax. Do not include any explanation or comments.
        """

    try:
        suggested_roles = await generate_roles_with_gemini(prompt)
        parsed_roles = json.loads(suggested_roles)
    except HTTPException as e:
        logger.error(f"an http exception occurred: {str(e)}")
        raise e
    except json.JSONDecodeError as e:
        logger.error(f'an unexpected error occurred in parsing from gemini: {str(e)}')
        raise HTTPException(
            status_code=500,
            detail="Failed to parse roles from OpenAI response",
        )

    return {"roles": parsed_roles}
