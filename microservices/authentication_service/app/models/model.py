import enum
from datetime import datetime, timedelta, timezone

from sqlalchemy import (
    <PERSON>olean,
    Column,
    DateTime,
    Enum,
    ForeignKey,
    Index,
    String,
    Table,
    Text,
    UniqueConstraint,
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import text

from app.models.base_model import BaseTableModel

user_organisation_roles = Table(
    'user_organisation_roles', BaseTableModel.metadata,
    Column("user_id", String, ForeignKey("users.id", ondelete="CASCADE"), primary_key=True),
    <PERSON>umn("organisation_id", String, ForeignKey("organisations.id", ondelete="CASCADE"), primary_key=True),
    <PERSON>umn('role_id', String, ForeignKey('roles.id', ondelete='CASCADE'), nullable=True),
    Column('is_owner', Boolean, server_default=text('false')),
    <PERSON>umn('status',  Enum('active', 'inactive', 'pending', name='user_organisation_status'), nullable=False, default="active"),
    <PERSON>umn('last_seen', DateTime(timezone=True), nullable=True),

    # Add a unique constraint to prevent a user from having duplicate organisations
    UniqueConstraint('user_id', 'organisation_id', name='unq_user_organisation')
)

role_permissions = Table(
    'role_permissions', BaseTableModel.metadata,
    Column('role_id', ForeignKey('roles.id'), primary_key=True),
    Column('permission_id', ForeignKey('permissions.id'), primary_key=True)
)

user_permissions = Table(
    'users_permissions', BaseTableModel.metadata,
    Column('user_id', ForeignKey('users.id', ondelete='CASCADE'), primary_key=True),
    Column("organisation_id", String, ForeignKey("organisations.id", ondelete="CASCADE"), primary_key=True),
    Column('permission_id', ForeignKey('permissions.id', ondelete='CASCADE'), primary_key=True)
)

class WaitList(BaseTableModel):
    __tablename__ = "waitlist"
    email = Column(String, unique=True, index=True, nullable=False)


class User(BaseTableModel):
    __tablename__ = "users"

    email = Column(String, unique=True, index=True, nullable=False)
    password = Column(String, nullable=True)
    first_name = Column(String, nullable=True)
    last_name = Column(String, nullable=True)
    avatar_url = Column(String, nullable=True)
    is_active = Column(Boolean, server_default=text("true"))
    is_superadmin = Column(Boolean, server_default=text("false"))
    is_deleted = Column(Boolean, server_default=text("false"))
    is_verified = Column(Boolean, server_default=text("false"))
    is_onboarded = Column(Boolean, server_default=text("false"))
    status = Column(Enum('invited', 'onboarded', name='user_status'), nullable=False, default='invited')

    # profile = relationship(
    #     "Profile", uselist=False, back_populates="user", cascade="all, delete-orphan"
    # )
    token_login = relationship(
        "TokenLogin", back_populates="user", uselist=False, cascade="all, delete-orphan"
    )
    otp_key = relationship('OTPKEY', back_populates="user", uselist=False, cascade="all, delete-orphan")
    organisations = relationship(
        "Organisation", secondary=user_organisation_roles, back_populates="users")
    invitations = relationship("Invitation", back_populates="user", cascade="all, delete-orphan")
    reset_password_tokens = relationship("ResetPasswordToken", back_populates="user", cascade="all,delete-orphan")
    permissions = relationship(
        'Permission',
        secondary=user_permissions,
        back_populates='users',
        lazy='dynamic'
    )
    profiles = relationship(
        "UserProfile",
        back_populates="user",
        cascade="all, delete-orphan"
    )

    def to_dict(self):
        obj_dict = super().to_dict()
        obj_dict.pop("password")
        return obj_dict

    def __str__(self):
        return self.email


class UserProfile(BaseTableModel):
    __tablename__ = "user_profiles"

    # Foreign key references with proper cascading
    user_id = Column(
        String,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    organisation_id = Column(
        String,
        ForeignKey("organisations.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )

    # Profile fields
    full_name = Column(String, nullable=False)
    email_address = Column(String, index=True, nullable=False)
    phone_number = Column(String, nullable=True)
    user_role = Column(String, nullable=True)
    img_url = Column(String, nullable=True)
    bio = Column(Text, nullable=True)

    # Relationships
    user = relationship("User", back_populates="profiles")
    organisation = relationship("Organisation", back_populates="user_profiles")

    # Table constraints and indexes
    __table_args__ = (
        # Unique constraint to ensure one profile per user per organization
        UniqueConstraint('user_id', 'organisation_id', name='unique_user_org_profile'),
        # Composite index for performance on common queries
        Index('idx_user_org_profile', 'user_id', 'organisation_id'),
    )


class Organisation(BaseTableModel):
    __tablename__ = "organisations"

    name = Column(String, nullable=False)
    industry = Column(String, nullable=True)
    description = Column(String, nullable=True)
    company_size = Column(String, nullable=True)
    target_audience = Column(String, nullable=True)
    brand_voice = Column(String, nullable=True)
    business_tone = Column(String, nullable=True)
    primary_color = Column(String, nullable=True)
    brand_logo = Column(String, nullable=True)
    suggested_role = Column(JSONB, nullable=True)

    users = relationship(
        "User", secondary=user_organisation_roles, back_populates="organisations"
    )
    invitations = relationship("Invitation", back_populates="organisation", cascade="all, delete-orphan")
    social_account = relationship('SocialAccount', back_populates='organisation', cascade='all, delete-orphan')
    competitors = relationship('Competitor', back_populates='organisation', cascade='all, delete-orphan')
    teams = relationship('TeamMember', back_populates='organisation', cascade='all, delete-orphan')
    roles = relationship('Role',  back_populates='organisation', cascade='all, delete-orphan')
    user_profiles = relationship(
        "UserProfile",
        back_populates="organisation",
        cascade="all, delete-orphan"
    )

    def __str__(self):
        return self.name


class Competitor(BaseTableModel):
    __tablename__ = "competitors"

    organisation_id = Column(
        String, ForeignKey("organisations.id", ondelete="CASCADE"), nullable=False
    )
    competitor_name = Column(String, nullable=False)
    competitor_link = Column(String, nullable=False)
    data = Column(Text, nullable=True)

    organisation = relationship("Organisation", back_populates="competitors")


class TeamMember(BaseTableModel):
    __tablename__ = "team_members"

    name = Column(String, nullable=False)
    role = Column(String, nullable=False)
    description = Column(Text, nullable=False)
    picture_url = Column(String, nullable=False)
    team_type = Column(String, nullable=True)  # e.g Executive team, Development team
    organisation_id = Column(
        String, ForeignKey("organisations.id", ondelete="CASCADE"), nullable=False
    )

    organisation = relationship('Organisation', back_populates='teams')


class ResetPasswordToken(BaseTableModel):
    """
    Represents password reset tokens
    """
    __tablename__ = "reset_password_tokens"
    user_id: Mapped[str] = mapped_column(String, ForeignKey("users.id", ondelete="CASCADE"), unique=True)
    jti: Mapped[str] = mapped_column(String)

    user = relationship('User', back_populates='reset_password_tokens')


class Role(BaseTableModel):
    __tablename__ = 'roles'

    name = Column(String(50), nullable=False)
    description = Column(Text, nullable=True)
    is_builtin = Column(Boolean, default=False)
    organisation_id = Column(String, ForeignKey('organisations.id', ondelete='CASCADE'), nullable=False)

    permissions = relationship(
        'Permission',
        secondary=role_permissions,
        back_populates='roles',
        lazy='dynamic'
    )
    organisation = relationship('Organisation', back_populates='roles')

    __table_args__ = (UniqueConstraint('name', 'organisation_id', name='uix_organisation_role_name'),)

    def __str__(self):
        return f'{self.name} - orgs: {self.organisation.name}'


class Permission(BaseTableModel):
    __tablename__ = 'permissions'

    title = Column(String(50), unique=True, nullable=False)

    roles = relationship(
        'Role',
        secondary=role_permissions,
        back_populates='permissions',
        lazy='dynamic'
    )
    users = relationship(
        'User',
        secondary=user_permissions,
        back_populates='permissions',
        lazy='dynamic'
    )

    def __str__(self):
        return self.title


class InvitationStatus(enum.Enum):
    active = "active"
    inactive = "inactive"
    pending = "pending"
    expired = "expired"


class Invitation(BaseTableModel):
    __tablename__ = "invitations"

    user_id = Column(String, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    organisation_id = Column(
        String, ForeignKey("organisations.id", ondelete="CASCADE"), nullable=False
    )
    invitee_email = Column(String, nullable=False)
    role_id = Column(String, ForeignKey("roles.id"), nullable=False)
    status = Column(Enum(InvitationStatus), nullable=False, default=InvitationStatus.pending)
    invite_link = Column(String, nullable=False)
    sent_at = Column(DateTime(timezone=True), nullable=False)
    accepted_at = Column(DateTime(timezone=True), nullable=True)
    expires_at = Column(DateTime(timezone=True), nullable=True)
    is_valid = Column(Boolean, default=True)

    user = relationship("User", back_populates="invitations")
    organisation = relationship("Organisation", back_populates="invitations")
    role = relationship('Role')


class SocialAccount(BaseTableModel):
    __tablename__ = "socialaccounts"

    organisation_id = Column(
        String, ForeignKey("organisations.id", ondelete="CASCADE"), nullable=False
    )
    platform_name = Column(String, nullable=False)
    username = Column(String, index=True, nullable=False)
    platform_link = Column(String, nullable=False)
    # refresh_token = Column(String, nullable=True)

    organisation = relationship("Organisation", back_populates="social_account")


class TokenLogin(BaseTableModel):
    __tablename__ = "token_logins"

    user_id = Column(
        String, ForeignKey("users.id", ondelete="CASCADE"), unique=True, nullable=False
    )
    token = Column(String, nullable=False)
    expiry_time = Column(DateTime(timezone=True), nullable=False)

    user = relationship("User", back_populates="token_login")


class OTPKEY(BaseTableModel):
    __tablename__ = "otp_keys"

    user_id = Column(
        String, ForeignKey("users.id", ondelete="CASCADE"), unique=True, nullable=False
    )
    otp_signing_key = Column(String, nullable=True)
    expiry_time = Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc) + timedelta(minutes=10))

    user = relationship("User", back_populates="otp_key")
