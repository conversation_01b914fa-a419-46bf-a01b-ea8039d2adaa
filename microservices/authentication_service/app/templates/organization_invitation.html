<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Ellum AI Organization Invite</title>
    <style>
        /* Email Client Reset */
        body, table, td, p, a, li, blockquote {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        
        table, td {
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        
        img {
            -ms-interpolation-mode: bicubic;
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
        }
        
        /* Base Styles */
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333333;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
            width: 100% !important;
            min-width: 100%;
        }
        
        /* Main Container */
        .email-wrapper {
            background-color: #f8f9fa;
            padding: 24px 0;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        /* Header */
        .header {
            background-color: #ffffff;
            padding: 32px 40px 24px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .logo {
            font-size: 28px;
            font-weight: 700;
            color: #1a73e8;
            margin-bottom: 8px;
            letter-spacing: -0.5px;
        }
        
        .tagline {
            font-size: 14px;
            color: #6b7280;
            margin: 0;
        }
        
        /* Network Visual - Simplified for Email */
        .network-visual {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border-radius: 8px;
            margin: 20px auto;
            padding: 30px 20px;
            text-align: center;
            height: 80px;
            display: table;
            width: 90%;
        }
        
        .network-content {
            display: table-cell;
            vertical-align: middle;
            text-align: center;
        }
        
        .network-icons {
            font-size: 24px;
            color: #1a73e8;
        }
        
        /* Main Content */
        .content {
            padding: 10px 40px;
            text-align: center;
        }
        
        .greeting {
            font-size: 24px;
            font-weight: 700;
            color: #111827;
            margin-bottom: 8px;
            text-align: center;
        }
        
        .subheading {
            font-size: 16px;
            color: #6b7280;
            margin-bottom: 24px;
            text-align: center;
            font-weight: 500;
        }
        
        .message {
            font-size: 16px;
            color: #4b5563;
            margin-bottom: 32px;
            line-height: 1.6;
            text-align: left;
        }
        
        .org-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 30px 0;
            border-left: 4px solid #4285f4;
            text-align: left;
        }

        .org-name {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .org-details {
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 8px;
        }
        
        .invited-by {
            color: #6c757d;
            font-size: 14px;
        }
        
        /* CTA Button */
        .cta-container {
            text-align: center;
            margin: 40px 0;
        }
        
        .cta-button {
            display: inline-block;
            background-color: #1a73e8;
            color: #ffffff !important;
            text-decoration: none;
            padding: 18px 40px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 16px;
            border: none;
            text-align: center;
        }
        
        .cta-button:hover {
            background-color: #1557b0 !important;
        }
        
        .fallback-link {
            font-size: 14px;
            color: #6b7280;
            margin-top: 16px;
            text-align: center;
        }
        
        .fallback-link a {
            color: #1a73e8;
            text-decoration: none;
            font-weight: 500;
        }
        
        /* Secondary Content */
        .secondary-message {
            font-size: 14px;
            color: #6b7280;
            margin: 24px 0;
            text-align: center;
        }
        
        .action-link {
            font-size: 14px;
            color: #6b7280;
            margin: 16px 0;
            text-align: center;
        }
        
        .action-link a {
            color: #1a73e8;
            text-decoration: none;
            font-weight: 500;
        }
        
        /* Credentials Section */
        .credentials-section {
            background-color: #f0f7ff;
            border: 2px solid #e6f3ff;
            padding: 24px;
            margin: 32px 0;
            border-radius: 12px;
        }
        
        .credentials-section h3 {
            font-size: 16px;
            font-weight: 600;
            color: #111827;
            margin-bottom: 16px;
            text-align: center;
        }
        
        .credential-item {
            background-color: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            margin-bottom: 12px;
            padding: 12px 16px;
        }
        
        .credential-item table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .credential-label {
            font-size: 14px;
            font-weight: 500;
            color: #6b7280;
            width: 40%;
            text-align: left;
        }
        
        .credential-value {
            font-size: 14px;
            font-weight: 600;
            color: #111827;
            background-color: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            text-align: right;
        }
        
        .credentials-note {
            font-size: 12px;
            color: #6b7280;
            text-align: center;
            margin-top: 16px;
            font-style: italic;
        }
        
        /* Personal Touch Section */
        .personal-touch {
            background-color: #f8f9fa;
            padding: 24px;
            margin: 32px 0;
            border-radius: 8px;
            text-align: left;
            border-left: 4px solid #1a73e8;
        }
        
        .personal-touch h3 {
            font-size: 16px;
            font-weight: 600;
            color: #111827;
            margin-bottom: 8px;
        }
        
        .personal-touch p {
            font-size: 14px;
            color: #4b5563;
            margin-bottom: 12px;
        }
        
        .feature-request {
            color: #1a73e8;
            text-decoration: none;
            font-weight: 500;
            font-size: 14px;
        }
        
        /* Urgency Notice */
        .urgency-notice {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 15px;
            margin: 25px 0;
            color: #92400e;
            font-size: 14px;
        }
        
        .urgency-icon {
            font-size: 18px;
            margin-right: 10px;
        }
        
        /* Signature */
        .signature {
            margin-top: 32px;
            font-size: 16px;
            color: #111827;
            text-align: left;
        }
        
        .signature-name {
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .signature-title {
            font-size: 14px;
            color: #6b7280;
        }
        
        /* Footer */
        .footer {
            background-color: #f8f9fa;
            padding: 32px 40px;
            border-top: 1px solid #e5e7eb;
            text-align: center;
        }
        
        .social-links {
            margin-bottom: 24px;
            text-align: center;
        }
        
        .social-links table {
            margin: 0 auto;
            border-collapse: collapse;
        }
        
        .social-links td {
            padding: 0 8px;
        }
        
        .social-links a {
            display: inline-block;
            padding: 8px 12px;
            background-color: #1a73e8;
            color: white !important;
            text-decoration: none;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            text-align: center;
        }
        
        .social-links a:hover {
            background-color: #1557b0 !important;
        }
        
        .contact-info {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 16px;
        }
        
        .contact-info a {
            color: #1a73e8;
            text-decoration: none;
        }
        
        .legal-links {
            font-size: 12px;
            color: #9ca3af;
        }
        
        .legal-links a {
            color: #9ca3af;
            text-decoration: none;
            margin: 0 8px;
        }
        
        /* Mobile Responsive */
        @media only screen and (max-width: 600px) {
            .email-wrapper {
                padding: 16px !important;
            }
            
            .header,
            .content,
            .footer {
                padding-left: 24px !important;
                padding-right: 24px !important;
            }
            
            .logo {
                font-size: 24px !important;
            }
            
            .greeting {
                font-size: 20px !important;
            }
            
            .message {
                font-size: 15px !important;
            }
            
            .cta-button {
                display: block !important;
                width: 90% !important;
                padding: 14px 24px !important;
                margin: 0 auto !important;
            }
            
            .personal-touch {
                margin: 24px 0 !important;
                padding: 20px !important;
            }
            
            .network-visual {
                width: 95% !important;
                padding: 20px 15px !important;
                height: 60px !important;
            }
        }
    </style>
</head>
<body>
    <table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color: #f8f9fa; padding: 24px 0;">
        <tr>
            <td align="center">
                <table width="600" cellpadding="0" cellspacing="0" border="0" style="max-width: 600px; background-color: #ffffff; border-radius: 12px;">
                    <!-- Header -->
                    <tr>
                        <td class="header">
                            <div class="logo">Ellum AI</div>
                            <p class="tagline">AI-Powered Social Media Management</p>
                        </td>
                    </tr>
                    
                    <!-- Network Visual -->
                    <tr>
                        <td style="padding: 0 40px;">
                            <div class="network-visual">
                                <div class="network-content">
                                    <div class="network-icons">👥 🌐 📊 🚀</div>
                                </div>
                            </div>
                        </td>
                    </tr>

                    <!-- Main Content -->
                    <tr>
                        <td class="content">
                            <div class="greeting">Join the team!</div>
                            <div class="subheading">You've been invited to collaborate</div>
                            
                            <div class="message">
                                <strong>{{ inviter_name }}</strong> has invited you to join their team <strong>{{ organisation }}</strong> on Ellum AI as a <strong>{{ role_name }}</strong>. Start collaborating on social media campaigns and boost your collective impact!
                            </div>
                            
                            <div class="org-info" style="text-align: center;">
                                <div class="org-name">{{ organisation }}</div>
                                <div class="org-details">{{ org_details }}</div>
                                <div class="invited-by">Invited by <strong>{{ inviter_name }}</strong>, {{ inviter_role }}</div>
                            </div>
                        </td>
                    </tr>
                    
                    <!-- CTA Button -->
                    <tr>
                        <td class="content">
                            <div class="cta-container">
                                <a href="{{ link }}" class="cta-button">Accept Invitation</a>
                                <div class="fallback-link">
                                    Button not working? <a href="{{ link }}">Click here instead</a>
                                </div>
                            </div>
                            
                            <div class="secondary-message">
                                Join now to access shared content calendars, team analytics, and collaborative planning tools.
                            </div>
                            
                            <div class="action-link">
                                Or <a href="{{ link }}">accept the invitation here</a>
                            </div>
                        </td>
                    </tr>
                    
                    <!-- Credentials Section -->
                    <tr>
                        <td class="content">
                            <div class="credentials-section">
                                <h3>Your Login Credentials</h3>
                                <div class="credential-item">
                                    <table width="100%" cellpadding="0" cellspacing="0" border="0">
                                        <tr>
                                            <td class="credential-label">Email:</td>
                                            <td class="credential-value">{{ email }}</td>
                                        </tr>
                                    </table>
                                </div>
                                <!-- Conditional password display -->
                                <div class="credential-item" style="display: {% if password and password != 'None' %}block{% else %}none{% endif %};">
                                    <table width="100%" cellpadding="0" cellspacing="0" border="0">
                                        <tr>
                                            <td class="credential-label">Temporary Password:</td>
                                            <td class="credential-value">{{ password }}</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="credentials-note" style="display: {% if password and password != 'None' %}block{% else %}none{% endif %};">
                                    Keep these credentials secure. You can change your password after logging in.
                                </div>
                            </div>
                        </td>
                    </tr>
                    
                    <!-- Personal Touch Section -->
                    <tr>
                        <td class="content">
                            <div class="personal-touch">
                                <h3>We're building for you</h3>
                                <p>Your feedback shapes our product. We'd love to hear what features would make Ellum AI perfect for your needs.</p>
                                <a href="#" class="feature-request">Request a Feature →</a>
                            </div>
                            
                            <div class="urgency-notice">
                                <span class="urgency-icon">⏰</span>
                                This invitation will expire in 7 days on {{ expires_at }} UTC. Accept now to get started with your team.
                            </div>
                            
                            <!-- Signature -->
                            <div class="signature">
                                <div class="signature-name">Richard C.</div>
                                <div class="signature-title">Founder, Ellum AI</div>
                            </div>
                        </td>
                    </tr>
                    
                    <!-- Footer -->
                    <tr>
                        <td style="background-color: #f8f9fa; padding: 32px 40px; border-top: 1px solid #e5e7eb; text-align: center;">
                            <!-- Social Links -->
                            <table width="100%" cellpadding="0" cellspacing="0" border="0">
                                <tr>
                                    <td align="center" style="padding-bottom: 24px;">
                                        <table cellpadding="0" cellspacing="0" border="0">
                                            <tr>
                                                <td style="padding: 0 8px;">
                                                    <a href="#facebook" style="display: inline-block; background-color: #1a73e8; padding: 8px; border-radius: 6px; text-decoration: none;">
                                                        <img src="https://img.icons8.com/ios-filled/50/ffffff/facebook-new.png" alt="Facebook" width="16" height="16" style="display: block;">
                                                    </a>
                                                </td>
                                                <td style="padding: 0 8px;">
                                                    <a href="#instagram" style="display: inline-block; background-color: #1a73e8; padding: 8px; border-radius: 6px; text-decoration: none;">
                                                        <img src="https://img.icons8.com/ios-filled/50/ffffff/instagram-new--v1.png" alt="Instagram" width="16" height="16" style="display: block;">
                                                    </a>
                                                </td>
                                                <td style="padding: 0 8px;">
                                                    <a href="#twitter" style="display: inline-block; background-color: #1a73e8; padding: 8px; border-radius: 6px; text-decoration: none;">
                                                        <img src="https://img.icons8.com/ios-filled/50/ffffff/twitter.png" alt="Twitter" width="16" height="16" style="display: block;">
                                                    </a>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                            
                            <div style="font-size: 14px; color: #6b7280; margin-bottom: 16px;">
                                Need help? Contact us at <a href="mailto:<EMAIL>" style="color: #1a73e8; text-decoration: none;"><EMAIL></a>
                            </div>
                            
                            <div style="font-size: 12px; color: #9ca3af;">
                                <a href="#unsubscribe" style="color: #9ca3af; text-decoration: none; margin: 0 8px;">Unsubscribe</a> |
                                <a href="#contact" style="color: #9ca3af; text-decoration: none; margin: 0 8px;">Contact</a> |
                                <a href="#privacy" style="color: #9ca3af; text-decoration: none; margin: 0 8px;">Privacy Policy</a>
                            </div>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>
</html>