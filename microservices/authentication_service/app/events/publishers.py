"""
Event publishers for Authentication service.
"""

from typing import Optional

from .client import get_event_client
from app.utils.logger import get_logger

logger = get_logger(__name__)


async def publish_user_registered(
    user_id: str,
    email: str,
    organization_id: str,
    username: Optional[str] = None,
    first_name: Optional[str] = None,
    last_name: Optional[str] = None,
    correlation_id: Optional[str] = None,
) -> str:
    """Publish a user registration event."""
    try:
        event_client = get_event_client()

        data = {
            "user_id": user_id,
            "email": email,
            "organization_id": organization_id,
            "username": username,
            "first_name": first_name,
            "last_name": last_name,
        }

        return await event_client.publish_event(
            event_type="auth.user.registered",
            data=data,
            correlation_id=correlation_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    except Exception as e:
        logger.error(f"Failed to publish user registered event: {e}")
        # Don't raise - we don't want event publishing failures to break user registration
        return ""


async def publish_user_login(
    user_id: str,
    organization_id: str,
    ip_address: Optional[str] = None,
    user_agent: Optional[str] = None,
    login_method: str = "password",
    correlation_id: Optional[str] = None,
) -> str:
    """Publish a user login event."""
    try:
        event_client = get_event_client()

        data = {
            "user_id": user_id,
            "organization_id": organization_id,
            "ip_address": ip_address,
            "user_agent": user_agent,
            "login_method": login_method,
        }

        return await event_client.publish_event(
            event_type="auth.user.login",
            data=data,
            correlation_id=correlation_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    except Exception as e:
        logger.error(f"Failed to publish user login event: {e}")
        return ""


async def publish_user_logout(
    user_id: str,
    organization_id: str,
    correlation_id: Optional[str] = None,
) -> str:
    """Publish a user logout event."""
    try:
        event_client = get_event_client()
        
        data = {
            "user_id": user_id,
            "organization_id": organization_id,
        }
        
        return await event_client.publish_event(
            event_type="auth.user.logout",
            data=data,
            correlation_id=correlation_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    except Exception as e:
        logger.error(f"Failed to publish user logout event: {e}")
        return ""


async def publish_user_role_changed(
    user_id: str,
    organization_id: str,
    old_role: str,
    new_role: str,
    changed_by: str,
    correlation_id: Optional[str] = None,
) -> str:
    """Publish a user role change event."""
    try:
        event_client = get_event_client()
        
        data = {
            "user_id": user_id,
            "organization_id": organization_id,
            "old_role": old_role,
            "new_role": new_role,
            "changed_by": changed_by,
        }
        
        return await event_client.publish_event(
            event_type="auth.user.role_changed",
            data=data,
            correlation_id=correlation_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    except Exception as e:
        logger.error(f"Failed to publish user role changed event: {e}")
        return ""


async def publish_organization_created(
    organization_id: str,
    organization_name: str,
    created_by: str,
    correlation_id: Optional[str] = None,
) -> str:
    """Publish an organization creation event."""
    try:
        event_client = get_event_client()

        data = {
            "organization_id": organization_id,
            "organization_name": organization_name,
            "created_by": created_by,
        }

        return await event_client.publish_event(
            event_type="auth.organization.created",
            data=data,
            correlation_id=correlation_id,
            user_id=created_by,
            organization_id=organization_id,
        )
    except Exception as e:
        logger.error(f"Failed to publish organization created event: {e}")
        return ""


async def publish_organization_updated(
    organization_id: str,
    organization_name: str,
    updated_by: str,
    changes: dict,
    correlation_id: Optional[str] = None,
) -> str:
    """Publish an organization update event."""
    try:
        event_client = get_event_client()
        
        data = {
            "organization_id": organization_id,
            "organization_name": organization_name,
            "updated_by": updated_by,
            "changes": changes,
        }
        
        return await event_client.publish_event(
            event_type="auth.organization.updated",
            data=data,
            correlation_id=correlation_id,
            user_id=updated_by,
            organization_id=organization_id,
        )
    except Exception as e:
        logger.error(f"Failed to publish organization updated event: {e}")
        return ""


async def publish_user_invited(
    invitee: dict,
    organisation_id: str,
    organisation_name: str,
    inviter_id: str,
    inviter_name: str,
    role_name: str,
    correlation_id: Optional[str] = None,
) -> str:
    """
    Publish an event when a user is invited to an organisation.
    Payload includes invitee details, organisation name, inviter, and role.
    """
    try:
        event_client = get_event_client()

        data = {
            "invitee": invitee,
            "organisation_id": organisation_id,
            "organisation_name": organisation_name,
            "inviter_id": inviter_id,
            "inviter_name": inviter_name,
            "role_name": role_name,
        }

        return await event_client.publish_event(
            event_type="auth.user.invited",
            data=data,
            correlation_id=correlation_id,
            user_id=inviter_id,
            organization_id=organisation_id,
        )
    except Exception as e:
        logger.error(f"Failed to publish user invited event: {e}")
        return ""
