# Use the official Python image from the Docker Hub
FROM python:3.12-alpine

# Set the working directory
WORKDIR /app

# Copy the requirements file into the container
COPY . /app

# Install the dependencies and curl
RUN apk add --no-cache curl && pip install -r requirements.txt

# create a directory for server logs
RUN mkdir -p /app/logs

EXPOSE 7777

# Command to run the application
CMD ["uvicorn", "--workers", "5", "--host", "0.0.0.0", "--port", "7777", "app.main:app"]
