# Authentication Service

The Authentication Service is responsible for user authentication, registration, and JWT token management in the EllumAI backend system.

## Features

- User registration and authentication
- JWT token generation and validation
- Password hashing and verification
- User profile management

## API Endpoints

The service exposes the following endpoints:

- `/api/v1/auth/register` - Register a new user
- `/api/v1/auth/login` - Authenticate a user and get JWT token
- `/api/v1/auth/token` - Get a new access token using refresh token
- `/auth_status` - Check if the service is running

Full API documentation is available at `/api/v1/auth/docs` when the service is running.

## Environment Variables

Create a `.env` file in the service directory with the following variables:

```
DATABASE_URL=postgresql://username:password@host:port/database
SECRET_KEY=your_secret_key
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7
```

## Running the Service

### Without Docker

1. Navigate to the authentication service directory:
   ```bash
   cd microservices/authentication_service
   ```

2. Create a virtual environment:
   ```bash
   python -m venv venv
   ```

3. Activate the virtual environment:
   - On Windows: `venv\Scripts\activate`
   - On macOS/Linux: `source venv/bin/activate`

4. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

5. Initialize and run migrations:
   ```bash
   alembic revision --autogenerate -m "initial migrations"
   alembic upgrade head
   ```

6. Start the service:
   ```bash
   uvicorn app.main:app --reload --host 0.0.0.0 --port 7777
   ```

### With Docker

1. Build and run the Docker container:
   ```bash
   docker build -t authentication_service .
   docker run -p 7777:7777 authentication_service
   ```

### Using Docker Compose

From the root of the project:
```bash
docker-compose up authentication_service
```
