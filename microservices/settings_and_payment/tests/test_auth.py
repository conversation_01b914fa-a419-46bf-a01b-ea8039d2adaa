user_detail = {
    "first_name": "test",
    "last_name": "example",
    "email": "<EMAIL>",
    "password": "P@ssw0RD"
}


def test_register_user_success(client, mock_send_email):
    response = client.post("/api/v1/auth/register", json=user_detail)
    
    # check that the respose was successfull
    assert response.status_code == 400
    # assert response.json() == {}
    
def test_register_user_used_email(client, mock_send_email):
    response = client.post("/api/v1/auth/register", json=user_detail)
    
    # check that the respose was successfull
    assert response.status_code == 400
    assert response.json()['message'] == 'User with this email already exists'
    assert response.json()['success'] == False

def test_login_no_user(client, mock_send_email):
    response = client.post("/api/v1/auth/login", json={"email": "<EMAIL>", "password": "P@ssw0RD"})
    
    # check that the respose was successfull
    assert response.status_code == 400
    
    assert response.json()['message'] == 'Invalid user email'
    assert response.json()['success'] == False


def test_login_fake_password(client, mock_send_email):
    response = client.post("/api/v1/auth/login", json={"email": "<EMAIL>", "password": "P@sDOsw0RD"})

    # check that the respose was successfull
    assert response.status_code == 400

    assert response.json()['message'] == 'Invalid user password'
    assert response.json()['success'] == False


def test_login_not_verified(client, mock_send_email):
    response = client.post("/api/v1/auth/login", json={"email": "<EMAIL>", "password": "P@ssw0RD"})

    # check that the respose was successfull
    assert response.status_code == 401

    assert response.json()['message'] == 'Account not yet verified'
    assert response.json()['success'] == False

# create a test to populate the db with permissions

def test_share_invite_link(client, mock_send_email):
    response = client.post("/api/v1/auth/org-share-invite", json={
        "email": "<EMAIL>",
        "password": "P@sDOsw0RD",
        "first_name": "invitee",
        "last_name": "user",
        "role": "member",
        "permissions": ["1", "2", "3"]
        })

    # check that the respose was successfull
    assert response.status_code == 400

    assert response.json()['message'] == 'Invalid user password'
    assert response.json()['success'] == False