'''
houses the test setup and fixtures
'''
import pytest
from fastapi.testclient import TestClient
from app.main import app
from app.database.session import engine, Base


@pytest.fixture(scope="module")
def test_db():
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)

# define a fixture for the test client
@pytest.fixture(scope="module")
def client():
    with TestClient(app) as test_client:
        yield test_client


# mock the email sending function
@pytest.fixture
def mock_send_email(mocker):
    mocker.patch("app.utils.email_service.EmailService.send", return_value=True)


# mock google oauth token verification
# @pytest.fixture
# def mock_google_auth(mocker):
#     mocker.patch("app.auth.google_signin", return_value={"email": "<EMAIL>"})


