"""
Integration tests for the event-driven architecture.
"""

import asyncio
import json
import pytest
import uuid
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock, patch

from app.events.bus import EventBus, init_event_bus
from app.events.models import Event, EventMetadata, EventType
from app.events.handlers import get_event_handler
from app.events.monitoring import get_event_monitor
from app.events.persistence import get_event_store


@pytest.fixture
async def mock_redis():
    """Mock Redis client for testing."""
    mock_redis = AsyncMock()
    mock_redis.ping.return_value = True
    mock_redis.xadd.return_value = "**********-0"
    mock_redis.publish.return_value = 1
    mock_redis.xgroup_create.return_value = True
    mock_redis.xreadgroup.return_value = []
    mock_redis.xrange.return_value = []
    mock_redis.xtrim.return_value = 0
    mock_redis.xack.return_value = 1
    return mock_redis


@pytest.fixture
async def event_bus(mock_redis):
    """Create an event bus instance for testing."""
    with patch('app.events.bus.redis.from_url', return_value=mock_redis):
        bus = EventBus("redis://localhost:6379", "test_service")
        await bus.connect()
        return bus


@pytest.fixture
def sample_event():
    """Create a sample event for testing."""
    metadata = EventMetadata(
        source_service="test_service",
        correlation_id="test-correlation-123",
        user_id="user-123",
        organization_id="org-456"
    )
    
    return Event(
        type=EventType.AUTH_USER_REGISTERED,
        data={
            "user_id": "user-123",
            "email": "<EMAIL>",
            "organization_id": "org-456",
            "username": "testuser"
        },
        metadata=metadata
    )


class TestEventBus:
    """Test the EventBus functionality."""
    
    @pytest.mark.asyncio
    async def test_event_bus_connection(self, event_bus):
        """Test event bus connection."""
        assert event_bus._redis is not None
        await event_bus._redis.ping()
    
    @pytest.mark.asyncio
    async def test_publish_event(self, event_bus, mock_redis):
        """Test event publishing."""
        event_id = await event_bus.publish(
            event_type=EventType.AUTH_USER_REGISTERED,
            data={
                "user_id": "user-123",
                "email": "<EMAIL>",
                "organization_id": "org-456"
            },
            user_id="user-123",
            organization_id="org-456"
        )
        
        assert event_id is not None
        assert len(event_id) > 0
        
        # Verify Redis calls
        mock_redis.xadd.assert_called_once()
        mock_redis.publish.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_subscribe_to_events(self, event_bus, mock_redis):
        """Test event subscription."""
        await event_bus.subscribe(
            event_types=[EventType.AUTH_USER_REGISTERED],
            use_streams=True,
            use_pubsub=False
        )
        
        # Verify Redis calls for stream setup
        mock_redis.xgroup_create.assert_called()
    
    @pytest.mark.asyncio
    async def test_event_serialization(self, sample_event):
        """Test event serialization and deserialization."""
        # Test to_dict
        event_dict = sample_event.to_dict()
        assert isinstance(event_dict, dict)
        assert event_dict["type"] == EventType.AUTH_USER_REGISTERED
        assert event_dict["data"]["user_id"] == "user-123"
        
        # Test from_dict
        reconstructed_event = Event.from_dict(event_dict)
        assert reconstructed_event.id == sample_event.id
        assert reconstructed_event.type == sample_event.type
        assert reconstructed_event.data == sample_event.data


class TestEventHandlers:
    """Test event handler functionality."""
    
    @pytest.mark.asyncio
    async def test_event_handler_registration(self):
        """Test event handler registration."""
        handler = get_event_handler()
        
        # Create a test handler
        test_handler_called = False
        
        async def test_handler(event: Event):
            nonlocal test_handler_called
            test_handler_called = True
        
        # Register handler
        handler.register(EventType.AUTH_USER_REGISTERED, test_handler)
        
        # Verify handler is registered
        handlers = handler.get_handlers(EventType.AUTH_USER_REGISTERED)
        assert len(handlers) > 0
        assert test_handler in handlers
    
    @pytest.mark.asyncio
    async def test_event_handling(self, sample_event):
        """Test event handling."""
        handler = get_event_handler()
        
        # Create a test handler
        test_results = []
        
        async def test_handler(event: Event):
            test_results.append(event.id)
        
        # Register and handle event
        handler.register(EventType.AUTH_USER_REGISTERED, test_handler)
        await handler.handle_event(sample_event)
        
        # Verify handler was called
        assert len(test_results) == 1
        assert test_results[0] == sample_event.id


class TestEventMonitoring:
    """Test event monitoring functionality."""
    
    @pytest.mark.asyncio
    async def test_event_monitoring_metrics(self, sample_event):
        """Test event monitoring metrics collection."""
        monitor = get_event_monitor()
        
        # Record some events
        monitor.record_event_processed(sample_event, 0.1)
        monitor.record_event_processed(sample_event, 0.2)
        
        # Get metrics
        metrics = monitor.get_metrics()
        
        assert metrics["event_counts"]["total"] == 2
        assert metrics["event_counts"][EventType.AUTH_USER_REGISTERED.value] == 2
        assert EventType.AUTH_USER_REGISTERED.value in metrics["average_processing_times"]
    
    @pytest.mark.asyncio
    async def test_event_monitoring_errors(self, sample_event):
        """Test event monitoring error tracking."""
        monitor = get_event_monitor()
        
        # Record an error
        monitor.record_event_error(sample_event, "Test error")
        
        # Get metrics
        metrics = monitor.get_metrics()
        
        assert metrics["error_counts"]["total"] == 1
        assert metrics["error_counts"][EventType.AUTH_USER_REGISTERED.value] == 1
    
    @pytest.mark.asyncio
    async def test_health_check(self, mock_redis):
        """Test health check functionality."""
        monitor = get_event_monitor()
        
        with patch('app.events.monitoring.get_event_bus') as mock_get_bus:
            mock_bus = MagicMock()
            mock_bus._redis = mock_redis
            mock_get_bus.return_value = mock_bus
            
            health = await monitor.health_check()
            
            assert "overall_status" in health
            assert "components" in health
            assert "redis" in health["components"]


class TestEventPersistence:
    """Test event persistence functionality."""
    
    @pytest.mark.asyncio
    async def test_get_events_by_type(self, mock_redis):
        """Test retrieving events by type."""
        store = get_event_store()
        
        # Mock Redis response
        mock_redis.xrange.return_value = [
            ("**********-0", {"event": json.dumps({
                "id": str(uuid.uuid4()),
                "type": EventType.AUTH_USER_REGISTERED.value,
                "data": {"user_id": "user-123"},
                "metadata": {
                    "source_service": "test",
                    "user_id": "user-123",
                    "organization_id": "org-456"
                },
                "timestamp": datetime.utcnow().isoformat(),
                "version": "1.0"
            })})
        ]
        
        with patch('app.events.persistence.get_event_bus') as mock_get_bus:
            mock_bus = MagicMock()
            mock_bus._redis = mock_redis
            mock_bus.stream_prefix = "events"
            mock_get_bus.return_value = mock_bus
            
            events = await store.get_events_by_type(EventType.AUTH_USER_REGISTERED)
            
            assert len(events) == 1
            assert events[0].type == EventType.AUTH_USER_REGISTERED
    
    @pytest.mark.asyncio
    async def test_event_statistics(self, mock_redis):
        """Test event statistics generation."""
        store = get_event_store()
        
        # Mock Redis responses for different event types
        mock_redis.xrange.return_value = []
        
        with patch('app.events.persistence.get_event_bus') as mock_get_bus:
            mock_bus = MagicMock()
            mock_bus._redis = mock_redis
            mock_bus.stream_prefix = "events"
            mock_get_bus.return_value = mock_bus
            
            stats = await store.get_event_statistics()
            
            assert "total_events" in stats
            assert "events_by_type" in stats
            assert "events_by_service" in stats


class TestEventIntegration:
    """Integration tests for the complete event system."""
    
    @pytest.mark.asyncio
    async def test_end_to_end_event_flow(self, mock_redis):
        """Test complete event flow from publishing to handling."""
        # Initialize event bus
        with patch('app.events.bus.redis.from_url', return_value=mock_redis):
            event_bus = init_event_bus("redis://localhost:6379", "test_service")
            await event_bus.start()
            
            # Track handled events
            handled_events = []
            
            async def test_handler(event: Event):
                handled_events.append(event.id)
            
            # Register handler
            handler = get_event_handler()
            handler.register(EventType.AUTH_USER_REGISTERED, test_handler)
            
            # Publish event
            event_id = await event_bus.publish(
                event_type=EventType.AUTH_USER_REGISTERED,
                data={
                    "user_id": "user-123",
                    "email": "<EMAIL>",
                    "organization_id": "org-456"
                },
                user_id="user-123",
                organization_id="org-456"
            )
            
            # Verify event was published
            assert event_id is not None
            mock_redis.xadd.assert_called()
            mock_redis.publish.assert_called()
    
    @pytest.mark.asyncio
    async def test_cross_service_communication(self, mock_redis):
        """Test cross-service event communication."""
        # This would test the HTTP client functionality
        # For now, we'll test the event client structure
        
        from app.events.client import EventPublishRequest, EventPublishResponse
        
        # Test request model
        request = EventPublishRequest(
            event_type="auth.user.registered",
            data={"user_id": "user-123"},
            user_id="user-123",
            organization_id="org-456"
        )
        
        assert request.event_type == "auth.user.registered"
        assert request.data["user_id"] == "user-123"
        
        # Test response model
        response = EventPublishResponse(event_id="event-123")
        assert response.event_id == "event-123"
        assert response.status == "published"


if __name__ == "__main__":
    pytest.main([__file__])
