import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.main import app
from app.database.session import get_db, Base
from app.models.model import UserProfile
from app.schemas.settings import UserProfileCreate, UserProfileUpdate
from app.services.crud import get_or_create_user_profile, update_user_profile


# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test_user_profile.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


app.dependency_overrides[get_db] = override_get_db


@pytest.fixture(scope="function")
def db_session():
    Base.metadata.create_all(bind=engine)
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()
        Base.metadata.drop_all(bind=engine)


@pytest.fixture
def client():
    return TestClient(app)


@pytest.fixture
def sample_user_details():
    return {
        "user_id": "test_user_123",
        "email": "<EMAIL>",
        "user_details": {
            "first_name": "John",
            "last_name": "Doe"
        }
    }


def test_get_or_create_user_profile_creates_new(db_session, sample_user_details):
    """Test that get_or_create_user_profile creates a new profile when none exists"""
    organization_id = "test_org_123"
    
    # Ensure no profile exists initially
    existing = db_session.query(UserProfile).filter(
        UserProfile.user_id == sample_user_details["user_id"],
        UserProfile.organization_id == organization_id
    ).first()
    assert existing is None
    
    # Create profile
    profile = get_or_create_user_profile(
        db=db_session,
        user_details=sample_user_details,
        organization_id=organization_id
    )
    
    # Verify profile was created
    assert profile is not None
    assert profile.user_id == sample_user_details["user_id"]
    assert profile.organization_id == organization_id
    assert profile.email_address == sample_user_details["email"]
    assert profile.full_name == "John Doe"


def test_get_or_create_user_profile_returns_existing(db_session, sample_user_details):
    """Test that get_or_create_user_profile returns existing profile when it exists"""
    organization_id = "test_org_123"
    
    # Create initial profile
    profile1 = get_or_create_user_profile(
        db=db_session,
        user_details=sample_user_details,
        organization_id=organization_id
    )
    
    # Try to create again - should return existing
    profile2 = get_or_create_user_profile(
        db=db_session,
        user_details=sample_user_details,
        organization_id=organization_id
    )
    
    # Should be the same profile
    assert profile1.id == profile2.id
    assert profile1.user_id == profile2.user_id


def test_update_user_profile(db_session, sample_user_details):
    """Test updating user profile"""
    organization_id = "test_org_123"
    
    # Create initial profile
    profile = get_or_create_user_profile(
        db=db_session,
        user_details=sample_user_details,
        organization_id=organization_id
    )
    
    # Update profile
    update_data = UserProfileUpdate(
        full_name="Jane Smith",
        phone_number="+1234567890",
        user_role="Admin"
    )
    
    updated_profile = update_user_profile(
        db=db_session,
        user_details=sample_user_details,
        new_profile=update_data,
        organization_id=organization_id
    )
    
    # Verify updates
    assert updated_profile.full_name == "Jane Smith"
    assert updated_profile.phone_number == "+1234567890"
    assert updated_profile.user_role == "Admin"
    assert updated_profile.email_address == sample_user_details["email"]  # Should remain unchanged


def test_update_nonexistent_user_profile(db_session, sample_user_details):
    """Test updating a profile that doesn't exist raises HTTPException"""
    organization_id = "test_org_123"
    
    update_data = UserProfileUpdate(full_name="Jane Smith")
    
    with pytest.raises(Exception):  # Should raise HTTPException
        update_user_profile(
            db=db_session,
            user_details=sample_user_details,
            new_profile=update_data,
            organization_id=organization_id
        )


def test_user_profile_unique_constraint(db_session):
    """Test that the unique constraint works for user_id + organization_id"""
    user_details = {
        "user_id": "test_user_123",
        "email": "<EMAIL>",
        "user_details": {"first_name": "John", "last_name": "Doe"}
    }
    
    organization_id = "test_org_123"
    
    # Create first profile
    profile1 = get_or_create_user_profile(
        db=db_session,
        user_details=user_details,
        organization_id=organization_id
    )
    
    # Try to create another profile for same user in same org - should return existing
    profile2 = get_or_create_user_profile(
        db=db_session,
        user_details=user_details,
        organization_id=organization_id
    )
    
    assert profile1.id == profile2.id
    
    # But should be able to create profile for same user in different org
    different_org_id = "different_org_456"
    profile3 = get_or_create_user_profile(
        db=db_session,
        user_details=user_details,
        organization_id=different_org_id
    )
    
    assert profile3.id != profile1.id
    assert profile3.organization_id == different_org_id
