from datetime import datetime

from app.models.base_model import BaseTableModel
from sqlalchemy import (
    JSON,
    Boolean,
    Column,
    DateTime,
    Enum,
    Float,
    Foreign<PERSON>ey,
    Integer,
    String,
    Time,
    UniqueConstraint,
)
from sqlalchemy.orm import relationship


class Plan(BaseTableModel):
    __tablename__ = "plans"

    name = Column(String, nullable=False, unique=True)
    interval = Column(String, nullable=False)
    description = Column(String, nullable=True)
    features = Column(String, nullable=True)  # Learn how to use JSONB
    currency = Column(String, nullable=True)
    amount = Column(Integer, nullable=False)
    invoice_limit = Column(Integer, nullable=True, default=None)
    plan_code = Column(String, nullable=True, index=True)
    subscriptions = relationship(
        "Subscription",
        back_populates="plan",
        uselist=False,
        cascade="all, delete-orphan",
    )

    def __str__(self):
        return f"{self.name} ** {self.plan_code}"


class Subscription(BaseTableModel):
    __tablename__ = "subscriptions"

    # organisation_id = Column(String, nullable=False)
    # the organisation for which the subscription is for
    user_id = Column(String, nullable=False)
    # the user making the subscription
    plan_id = Column(
        ForeignKey("plans.id", ondelete="CASCADE")
    )  # the plan that's been subscribed for
    status = Column(Boolean, default=False)  # status of the subscription
    reference_number = Column(
        String, nullable=False
    )  # the reference number generated from paystack
    access_code = Column(
        String, nullable=False
    )  # the access code generated from paystack
    plan = relationship("Plan", back_populates="subscriptions")


# -----------SETTINGS---------------#
# class GeneralSettings(BaseTableModel):
#     __tablename__ = "general_settings"
#     organization_id = Column(String)
#     full_name = Column(String, index=True)
#     email_address = Column(String, index=True)
#     phone_number = Column(String, nullable=True)
#     user_role = Column(String)
#     brand_voice = Column(String)
#     img_url = Column(String, nullable=True)


class ApplicationSettings(BaseTableModel):
    __tablename__ = "application_settings"
    organization_id = Column(String)
    user_email = Column(String)
    default_language = Column(String)
    content_auto_save_interval = Column(String)
    in_app_notifications = Column(Boolean, default=True)
    email_notifications = Column(Boolean, default=True)


class SocialSettings(BaseTableModel):
    __tablename__ = "social_settings"
    organization_id = Column(String, index=True)
    social_media = Column(JSON, default={})

    def __init__(self, organization_id, social_media=None):
        if social_media is None:
            social_media = {}
        self.organization_id = organization_id
        self.social_media = social_media


class LocalizationSettings(BaseTableModel):
    __tablename__ = "localization_settings"
    organization_id = Column(String, index=True)
    user_email = Column(String, index=True)
    language = Column(String, default="English")
    date_format = Column(String, default="MM/DD/YYYY")
    time_format = Column(String, default="12-hour")
    number_format = Column(String, default="1,000.00")
    country_region = Column(String, default="US")
    time_zone = Column(String, default="UTC")
    show_region_specific_content = Column(Boolean, default=False)
    block_content_based_on_locaction = Column(Boolean, default=False)


class DataStorageSettings(BaseTableModel):
    __tablename__ = "data_storage_settings"
    organization_id = Column(String, index=True)
    storage_limit = Column(Float, default=1000)  # Storage in GB
    storage_used = Column(Float, default=0)  # Used storage in GB
    automatic_backup = Column(Boolean, default=False)
    backup_frequency = Column(
        Enum("Daily", "Weekly", "Monthly", name="backup_frequency"), default="Weekly"
    )
    backup_location = Column(
        Enum("Cloud", "Local Storage", name="backup_location"), default="Cloud"
    )
    export_format = Column(String, default="PDF")  # Available formats: PDF, CSV, etc.
    data_retention_period = Column(
        String, default="1 year"
    )  # Example: 1 year, 6 months, etc.
    deletion_warning = Column(Boolean, default=False)


class NotificationSettings(BaseTableModel):
    __tablename__ = "notification_settings"
    organization_id = Column(String, index=True)
    user_id = Column(String, index=True)
    comments = Column(Boolean, default=True)
    new_message_alerts = Column(Boolean, default=True)
    account_activity = Column(Boolean, default=True)
    system_updates = Column(Boolean, default=True)
    marketing_updates = Column(Boolean, default=False)
    in_app_notifications = Column(Boolean, default=True)
    email_notifications = Column(Boolean, default=True)
    push_notifications = Column(Boolean, default=True)
    notification_frequency = Column(
        String, default="Real-time"
    )  # e.g., Real-time, Daily, Weekly
    do_not_disturb_start = Column(Time)
    do_not_disturb_end = Column(Time)


class SessionActivity(BaseTableModel):
    __tablename__ = "session_activity"
    organization_id = Column(String, index=True)
    session_id = Column(String, index=True)
    username = Column(String, index=True)
    device = Column(String)
    location = Column(String)
    start_time = Column(DateTime, default=datetime.now)
    end_time = Column(DateTime, nullable=True)


class UsageMetrics(BaseTableModel):
    __tablename__ = "usage_metrics"
    organization_id = Column(String, index=True)
    total_logins = Column(Integer, default=0)
    active_users_today = Column(Integer, default=0)
    total_sessions = Column(Integer, default=0)
    average_session_duration = Column(Float, default=0.0)
    peak_usage_start = Column(String)  # Example: '2:00 PM'
    peak_usage_end = Column(String)  # Example: '4:00 PM'


class Notification(BaseTableModel):
    __tablename__ = "notifications"
    user_id = Column(String, index=True)
    organization_id = Column(String, index=True)
    message = Column(String, nullable=False)
    is_read = Column(Boolean, default=False)


class DeviceToken(BaseTableModel):
    __tablename__ = "device_tokens"
    user_id = Column(String, nullable=False)
    device_token = Column(String, unique=True, nullable=False)
