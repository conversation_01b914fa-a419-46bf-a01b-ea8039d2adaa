"""
Event system exceptions.
"""


class EventError(Exception):
    """Base exception for event system errors."""
    pass


class EventPublishError(EventError):
    """Exception raised when event publishing fails."""
    pass


class EventSubscribeError(EventError):
    """Exception raised when event subscription fails."""
    pass


class EventValidationError(EventError):
    """Exception raised when event validation fails."""
    pass


class EventSerializationError(EventError):
    """Exception raised when event serialization/deserialization fails."""
    pass


class EventBusConnectionError(EventError):
    """Exception raised when event bus connection fails."""
    pass
