"""
Event subscribers setup for Settings and Payment service.
"""

from typing import List

from .bus import get_event_bus
from .models import EventType
from app.utils.logger import get_logger

logger = get_logger(__name__)


async def setup_event_subscribers() -> None:
    """Set up event subscribers for the Settings and Payment service."""
    try:
        event_bus = get_event_bus()

        # Subscribe to events that this service should handle
        event_types_to_subscribe = [et.value for et in EventType]
        
        await event_bus.subscribe(
            event_types=event_types_to_subscribe,
            use_streams=True,
            use_pubsub=True,
        )
        
        logger.info(f"Settings and Payment service subscribed to events: {event_types_to_subscribe}")
        
    except Exception as e:
        logger.error(f"Failed to set up event subscribers: {e}")
        raise


def get_subscribed_event_types() -> List[EventType]:
    """Get the list of event types this service subscribes to."""
    return [et for et in EventType]
