"""
Event models and schemas for the event-driven architecture.
"""

import uuid
from datetime import datetime
from enum import Enum
from typing import Any, Dict, Optional

from pydantic import BaseModel, Field


class EventType(str, Enum):
    """Enumeration of all event types in the system."""

    # Authentication Service Events
    AUTH_USER_REGISTERED = "auth.user.registered"
    AUTH_USER_LOGIN = "auth.user.login"
    AUTH_USER_LOGOUT = "auth.user.logout"
    AUTH_USER_ROLE_CHANGED = "auth.user.role_changed"
    AUTH_USER_INVITED = "auth.user.invited"
    AUTH_ORGANIZATION_CREATED = "auth.organization.created"
    AUTH_ORGANIZATION_UPDATED = "auth.organization.updated"

    # Settings & Payment Service Events
    SETTINGS_SUBSCRIPTION_CREATED = "settings.subscription.created"
    SETTINGS_SUBSCRIPTION_UPDATED = "settings.subscription.updated"
    SETTINGS_SUBSCRIPTION_CANCELLED = "settings.subscription.cancelled"
    SETTINGS_PAYMENT_COMPLETED = "settings.payment.completed"
    SETTINGS_PAYMENT_FAILED = "settings.payment.failed"
    SETTINGS_NOTIFICATION_PREFERENCES_UPDATED = "settings.notification_preferences.updated"
    
    # FastBot Service Events
    FASTBOT_FILE_UPLOADED = "fastbot.file.uploaded"
    FASTBOT_FILE_PROCESSED = "fastbot.file.processed"
    FASTBOT_CHAT_MESSAGE_SENT = "fastbot.chat.message_sent"
    FASTBOT_KNOWLEDGE_BASE_UPDATED = "fastbot.knowledge_base.updated"
    FASTBOT_TASK_COMPLETED = "fastbot.task.completed"
    FASTBOT_TASK_FAILED = "fastbot.task.failed"
    
    # Socials Service Events
    SOCIALS_POST_SCHEDULED = "socials.post.scheduled"
    SOCIALS_POST_PUBLISHED = "socials.post.published"
    SOCIALS_CONTENT_SCHEDULED = "socials.content.scheduled"
    SOCIALS_CONTENT_UPDATED = "socials.content.updated"
    SOCIALS_METRICS_UPDATED = "socials.metrics.updated"
    SOCIALS_PLATFORM_CONNECTED = "socials.platform.connected"
    SOCIALS_PLATFORM_DISCONNECTED = "socials.platform.disconnected"
    
    # Notification Events
    NOTIFICATION_SENT = "notification.sent"
    NOTIFICATION_FAILED = "notification.failed"


class EventMetadata(BaseModel):
    """Metadata associated with an event."""
    
    source_service: str = Field(..., description="Service that published the event")
    correlation_id: Optional[str] = Field(None, description="Request correlation ID")
    user_id: Optional[str] = Field(None, description="User associated with the event")
    organization_id: Optional[str] = Field(None, description="Organization associated with the event")
    trace_id: Optional[str] = Field(None, description="Distributed tracing ID")
    retry_count: int = Field(0, description="Number of retry attempts")
    
    class Config:
        extra = "allow"  # Allow additional metadata fields


class Event(BaseModel):
    """Base event model for all events in the system."""
    
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Unique event ID")
    type: EventType = Field(..., description="Type of the event")
    data: Dict[str, Any] = Field(..., description="Event payload data")
    metadata: EventMetadata = Field(..., description="Event metadata")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Event timestamp")
    version: Optional[str] = Field("1.0", description="Event schema version")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert event to dictionary for serialization."""
        data = self.dict()
        # Convert datetime objects to ISO format strings
        if 'timestamp' in data and isinstance(data['timestamp'], datetime):
            data['timestamp'] = data['timestamp'].isoformat()
        if 'metadata' in data and isinstance(data['metadata'], dict):
            if 'timestamp' in data['metadata'] and isinstance(data['metadata']['timestamp'], datetime):
                data['metadata']['timestamp'] = data['metadata']['timestamp'].isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Event":
        """Create event from dictionary."""
        # Parse datetime strings back to datetime objects
        if 'timestamp' in data and isinstance(data['timestamp'], str):
            data['timestamp'] = datetime.fromisoformat(data['timestamp'].replace('Z', '+00:00'))
        if 'metadata' in data and isinstance(data['metadata'], dict):
            if 'timestamp' in data['metadata'] and isinstance(data['metadata']['timestamp'], str):
                data['metadata']['timestamp'] = datetime.fromisoformat(data['metadata']['timestamp'].replace('Z', '+00:00'))
        return cls(**data)


# Event data schemas for specific event types

class UserRegisteredData(BaseModel):
    """Data schema for user registration events."""
    user_id: str
    email: str
    organization_id: str
    username: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None


class UserLoginData(BaseModel):
    """Data schema for user login events."""
    user_id: str
    organization_id: str
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    login_method: str = "password"


class SubscriptionCreatedData(BaseModel):
    """Data schema for subscription creation events."""
    subscription_id: str
    user_id: str
    organization_id: str
    plan_id: str
    plan_name: str
    amount: float
    currency: str = "USD"
    status: str


class FileUploadedData(BaseModel):
    """Data schema for file upload events."""
    file_id: str
    user_id: str
    organization_id: str
    filename: str
    file_size: int
    file_type: str
    upload_path: str


class ChatMessageData(BaseModel):
    """Data schema for chat message events."""
    message_id: str
    user_id: str
    organization_id: str
    conversation_id: str
    message_content: str
    message_type: str = "text"
    ai_response: Optional[str] = None


class NotificationData(BaseModel):
    """Data schema for notification events."""
    notification_id: str
    user_id: str
    organization_id: str
    title: str
    message: str
    notification_type: str
    channel: str  # email, push, in_app
    status: str  # sent, failed, pending
