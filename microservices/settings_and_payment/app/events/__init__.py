"""
Event integration for Settings and Payment service.
"""

from .publishers import (
    publish_notification_preferences_updated,
    publish_payment_completed,
    publish_payment_failed,
    publish_subscription_cancelled,
    publish_subscription_created,
    publish_subscription_updated,
)
from .subscribers import setup_event_subscribers
from .handlers import (
    handle_user_registered,
    handle_user_login,
    handle_file_uploaded,
    handle_chat_message,
)

__all__ = [
    "publish_notification_preferences_updated",
    "publish_payment_completed", 
    "publish_payment_failed",
    "publish_subscription_cancelled",
    "publish_subscription_created",
    "publish_subscription_updated",
    "setup_event_subscribers",
    "handle_user_registered",
    "handle_user_login",
    "handle_file_uploaded",
    "handle_chat_message",
]
