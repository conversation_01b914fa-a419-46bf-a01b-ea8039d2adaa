"""
Event monitoring and observability functionality.
"""

import asyncio
import json
import logging
import time
from collections import defaultdict, deque
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from .bus import get_event_bus
from .models import Event, EventType
from .persistence import get_event_store

logger = logging.getLogger(__name__)


class EventMonitor:
    """Event monitoring and metrics collection."""
    
    def __init__(self):
        self.event_bus = None
        self.event_store = None
        
        # Metrics storage
        self.event_counts = defaultdict(int)
        self.processing_times = defaultdict(list)
        self.error_counts = defaultdict(int)
        self.recent_events = deque(maxlen=1000)  # Keep last 1000 events
        
        # Performance tracking
        self.start_time = time.time()
        self.last_reset = datetime.utcnow()
        
        # Health status
        self.is_healthy = True
        self.last_health_check = datetime.utcnow()
    
    def _get_event_bus(self):
        """Get event bus instance."""
        if self.event_bus is None:
            self.event_bus = get_event_bus()
        return self.event_bus
    
    def _get_event_store(self):
        """Get event store instance."""
        if self.event_store is None:
            self.event_store = get_event_store()
        return self.event_store
    
    def record_event_processed(self, event: Event, processing_time: float):
        """Record that an event was processed."""
        try:
            # Update counters
            self.event_counts[event.type.value] += 1
            self.event_counts["total"] += 1
            
            # Record processing time
            self.processing_times[event.type.value].append(processing_time)
            
            # Keep only recent processing times (last 100 per type)
            if len(self.processing_times[event.type.value]) > 100:
                self.processing_times[event.type.value] = self.processing_times[event.type.value][-100:]
            
            # Add to recent events
            self.recent_events.append({
                "event_id": event.id,
                "event_type": event.type.value,
                "timestamp": event.timestamp.isoformat(),
                "source_service": event.metadata.source_service,
                "processing_time": processing_time,
                "organization_id": event.metadata.organization_id,
                "user_id": event.metadata.user_id,
            })
            
            logger.debug(f"Recorded processing of event {event.id} in {processing_time:.3f}s")
            
        except Exception as e:
            logger.error(f"Error recording event processing: {e}")
    
    def record_event_error(self, event: Event, error: str):
        """Record that an event processing failed."""
        try:
            self.error_counts[event.type.value] += 1
            self.error_counts["total"] += 1
            
            # Add to recent events with error
            self.recent_events.append({
                "event_id": event.id,
                "event_type": event.type.value,
                "timestamp": event.timestamp.isoformat(),
                "source_service": event.metadata.source_service,
                "error": error,
                "organization_id": event.metadata.organization_id,
                "user_id": event.metadata.user_id,
            })
            
            logger.warning(f"Recorded error for event {event.id}: {error}")
            
        except Exception as e:
            logger.error(f"Error recording event error: {e}")
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get current metrics."""
        try:
            uptime = time.time() - self.start_time
            
            # Calculate average processing times
            avg_processing_times = {}
            for event_type, times in self.processing_times.items():
                if times:
                    avg_processing_times[event_type] = {
                        "average": sum(times) / len(times),
                        "min": min(times),
                        "max": max(times),
                        "count": len(times)
                    }
            
            # Calculate error rates
            error_rates = {}
            for event_type in self.event_counts:
                if event_type != "total":
                    total_events = self.event_counts[event_type]
                    error_events = self.error_counts[event_type]
                    error_rates[event_type] = {
                        "total_events": total_events,
                        "error_events": error_events,
                        "error_rate": (error_events / total_events * 100) if total_events > 0 else 0
                    }
            
            return {
                "uptime_seconds": uptime,
                "last_reset": self.last_reset.isoformat(),
                "is_healthy": self.is_healthy,
                "last_health_check": self.last_health_check.isoformat(),
                "event_counts": dict(self.event_counts),
                "error_counts": dict(self.error_counts),
                "average_processing_times": avg_processing_times,
                "error_rates": error_rates,
                "recent_events_count": len(self.recent_events),
            }
            
        except Exception as e:
            logger.error(f"Error getting metrics: {e}")
            return {"error": str(e)}
    
    def get_recent_events(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent events."""
        try:
            recent = list(self.recent_events)
            return recent[-limit:] if len(recent) > limit else recent
        except Exception as e:
            logger.error(f"Error getting recent events: {e}")
            return []
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on the event system."""
        try:
            health_status = {
                "timestamp": datetime.utcnow().isoformat(),
                "overall_status": "healthy",
                "components": {}
            }
            
            # Check Redis connection
            try:
                event_bus = self._get_event_bus()
                await event_bus._redis.ping()
                health_status["components"]["redis"] = {
                    "status": "healthy",
                    "message": "Redis connection successful"
                }
            except Exception as e:
                health_status["components"]["redis"] = {
                    "status": "unhealthy",
                    "message": f"Redis connection failed: {e}"
                }
                health_status["overall_status"] = "unhealthy"
            
            # Check event processing rates
            total_events = self.event_counts.get("total", 0)
            total_errors = self.error_counts.get("total", 0)
            error_rate = (total_errors / total_events * 100) if total_events > 0 else 0
            
            if error_rate > 10:  # More than 10% error rate
                health_status["components"]["event_processing"] = {
                    "status": "degraded",
                    "message": f"High error rate: {error_rate:.2f}%"
                }
                if health_status["overall_status"] == "healthy":
                    health_status["overall_status"] = "degraded"
            else:
                health_status["components"]["event_processing"] = {
                    "status": "healthy",
                    "message": f"Error rate: {error_rate:.2f}%"
                }
            
            # Check recent activity
            recent_events = self.get_recent_events(10)
            if recent_events:
                last_event_time = datetime.fromisoformat(recent_events[-1]["timestamp"])
                time_since_last = datetime.utcnow() - last_event_time
                
                if time_since_last > timedelta(minutes=30):  # No events in 30 minutes
                    health_status["components"]["activity"] = {
                        "status": "warning",
                        "message": f"No events in {time_since_last.total_seconds() / 60:.1f} minutes"
                    }
                else:
                    health_status["components"]["activity"] = {
                        "status": "healthy",
                        "message": f"Last event {time_since_last.total_seconds():.0f} seconds ago"
                    }
            else:
                health_status["components"]["activity"] = {
                    "status": "warning",
                    "message": "No recent events recorded"
                }
            
            self.is_healthy = health_status["overall_status"] in ["healthy", "degraded"]
            self.last_health_check = datetime.utcnow()
            
            return health_status
            
        except Exception as e:
            logger.error(f"Error performing health check: {e}")
            return {
                "timestamp": datetime.utcnow().isoformat(),
                "overall_status": "unhealthy",
                "error": str(e)
            }
    
    async def generate_report(
        self,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
    ) -> Dict[str, Any]:
        """Generate a comprehensive event report."""
        try:
            if not start_time:
                start_time = datetime.utcnow() - timedelta(hours=24)  # Last 24 hours
            if not end_time:
                end_time = datetime.utcnow()
            
            event_store = self._get_event_store()
            
            # Get event statistics
            stats = await event_store.get_event_statistics(start_time, end_time)
            
            # Get current metrics
            metrics = self.get_metrics()
            
            # Get health status
            health = await self.health_check()
            
            report = {
                "report_generated": datetime.utcnow().isoformat(),
                "time_range": {
                    "start": start_time.isoformat(),
                    "end": end_time.isoformat(),
                    "duration_hours": (end_time - start_time).total_seconds() / 3600
                },
                "event_statistics": stats,
                "current_metrics": metrics,
                "health_status": health,
                "recommendations": []
            }
            
            # Add recommendations based on metrics
            if health["overall_status"] != "healthy":
                report["recommendations"].append("System health issues detected - investigate component status")
            
            error_rate = metrics.get("error_rates", {}).get("total", {}).get("error_rate", 0)
            if error_rate > 5:
                report["recommendations"].append(f"High error rate ({error_rate:.2f}%) - review error logs")
            
            if stats["total_events"] == 0:
                report["recommendations"].append("No events processed - verify event publishing is working")
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating report: {e}")
            return {
                "report_generated": datetime.utcnow().isoformat(),
                "error": str(e)
            }
    
    def reset_metrics(self):
        """Reset all metrics counters."""
        try:
            self.event_counts.clear()
            self.processing_times.clear()
            self.error_counts.clear()
            self.recent_events.clear()
            self.last_reset = datetime.utcnow()
            logger.info("Event metrics reset")
        except Exception as e:
            logger.error(f"Error resetting metrics: {e}")


# Global event monitor instance
_event_monitor: Optional[EventMonitor] = None


def get_event_monitor() -> EventMonitor:
    """Get the global event monitor instance."""
    global _event_monitor
    if _event_monitor is None:
        _event_monitor = EventMonitor()
    return _event_monitor
