"""
Event publishers for Settings and Payment service.
"""

import logging
from typing import Any, Dict, Optional

from .bus import get_event_bus
from .models import EventType

logger = logging.getLogger(__name__)


async def publish_subscription_created(
    subscription_id: str,
    user_id: str,
    organization_id: str,
    plan_id: str,
    plan_name: str,
    amount: float,
    currency: str = "USD",
    status: str = "active",
    correlation_id: Optional[str] = None,
) -> str:
    """Publish a subscription creation event."""
    try:
        event_bus = get_event_bus()

        data = {
            "subscription_id": subscription_id,
            "user_id": user_id,
            "organization_id": organization_id,
            "plan_id": plan_id,
            "plan_name": plan_name,
            "amount": amount,
            "currency": currency,
            "status": status,
        }

        return await event_bus.publish(
            event_type=EventType.SETTINGS_SUBSCRIPTION_CREATED,
            data=data,
            correlation_id=correlation_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    except Exception as e:
        logger.error(f"Failed to publish subscription created event: {e}")
        raise


async def publish_subscription_updated(
    subscription_id: str,
    user_id: str,
    organization_id: str,
    changes: Dict[str, Any],
    correlation_id: Optional[str] = None,
) -> str:
    """Publish a subscription update event."""
    try:
        event_bus = get_event_bus()
        
        data = {
            "subscription_id": subscription_id,
            "user_id": user_id,
            "organization_id": organization_id,
            "changes": changes,
        }
        
        return await event_bus.publish(
            event_type=EventType.SETTINGS_SUBSCRIPTION_UPDATED,
            data=data,
            correlation_id=correlation_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    except Exception as e:
        logger.error(f"Failed to publish subscription updated event: {e}")
        raise


async def publish_subscription_cancelled(
    subscription_id: str,
    user_id: str,
    organization_id: str,
    reason: Optional[str] = None,
    correlation_id: Optional[str] = None,
) -> str:
    """Publish a subscription cancellation event."""
    try:
        event_bus = get_event_bus()
        
        data = {
            "subscription_id": subscription_id,
            "user_id": user_id,
            "organization_id": organization_id,
            "reason": reason,
        }
        
        return await event_bus.publish(
            event_type=EventType.SETTINGS_SUBSCRIPTION_CANCELLED,
            data=data,
            correlation_id=correlation_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    except Exception as e:
        logger.error(f"Failed to publish subscription cancelled event: {e}")
        raise


async def publish_payment_completed(
    payment_id: str,
    subscription_id: str,
    user_id: str,
    organization_id: str,
    amount: float,
    currency: str = "USD",
    payment_method: str = "stripe",
    correlation_id: Optional[str] = None,
) -> str:
    """Publish a payment completion event."""
    try:
        event_bus = get_event_bus()
        
        data = {
            "payment_id": payment_id,
            "subscription_id": subscription_id,
            "user_id": user_id,
            "organization_id": organization_id,
            "amount": amount,
            "currency": currency,
            "payment_method": payment_method,
        }
        
        return await event_bus.publish(
            event_type=EventType.SETTINGS_PAYMENT_COMPLETED,
            data=data,
            correlation_id=correlation_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    except Exception as e:
        logger.error(f"Failed to publish payment completed event: {e}")
        raise


async def publish_payment_failed(
    payment_id: str,
    subscription_id: str,
    user_id: str,
    organization_id: str,
    amount: float,
    currency: str = "USD",
    error_message: str = "",
    payment_method: str = "stripe",
    correlation_id: Optional[str] = None,
) -> str:
    """Publish a payment failure event."""
    try:
        event_bus = get_event_bus()
        
        data = {
            "payment_id": payment_id,
            "subscription_id": subscription_id,
            "user_id": user_id,
            "organization_id": organization_id,
            "amount": amount,
            "currency": currency,
            "error_message": error_message,
            "payment_method": payment_method,
        }
        
        return await event_bus.publish(
            event_type=EventType.SETTINGS_PAYMENT_FAILED,
            data=data,
            correlation_id=correlation_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    except Exception as e:
        logger.error(f"Failed to publish payment failed event: {e}")
        raise


async def publish_notification_preferences_updated(
    user_id: str,
    organization_id: str,
    preferences: Dict[str, Any],
    correlation_id: Optional[str] = None,
) -> str:
    """Publish a notification preferences update event."""
    try:
        event_bus = get_event_bus()
        
        data = {
            "user_id": user_id,
            "organization_id": organization_id,
            "preferences": preferences,
        }
        
        return await event_bus.publish(
            event_type=EventType.SETTINGS_NOTIFICATION_PREFERENCES_UPDATED,
            data=data,
            correlation_id=correlation_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    except Exception as e:
        logger.error(f"Failed to publish notification preferences updated event: {e}")
        raise


async def publish_notification_event(
    notification_id: str,
    user_id: str,
    organization_id: str,
    title: str,
    message: str,
    notification_type: str,
    channel: str,
    status: str = "sent",
    correlation_id: Optional[str] = None,
) -> str:
    """Publish a notification event."""
    try:
        event_bus = get_event_bus()

        data = {
            "notification_id": notification_id,
            "user_id": user_id,
            "organization_id": organization_id,
            "title": title,
            "message": message,
            "notification_type": notification_type,
            "channel": channel,
            "status": status,
        }

        return await event_bus.publish(
            event_type=EventType.NOTIFICATION_SENT,
            data=data,
            correlation_id=correlation_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    except Exception as e:
        logger.error(f"Failed to publish notification event: {e}")
        raise
