# from app.schemas.subscription import StripeCreateProduct
import json

import stripe
from app.core.settings import settings
from app.utils.logger import get_logger
from fastapi import HTTPException, Request

stripe.api_key = settings.STRIPE_SECRET_KEY

logger = get_logger(__name__)
logger.info("redis client will be connected in stripe")


# async def initialize_redis():
#     global redis_client
#     redis_client = await redis_service.connect()

# redis_client = redis_service


async def get_product(product_id):
    """returns a product"""
    try:
        # check if the products are in the cache
        # cache_key = f"product:{product_id}"
        # # cached_product = await redis_client.redis.get(cache_key)
        # if cached_product:
        #     return json.loads(cached_product)
        # get the product
        res = stripe.Product.retrieve(product_id)
        # extract the price_id and get the price details
        price_id = res.default_price
        price = stripe.Price.retrieve(price_id)
        price_details = {
            "interval": price.recurring.interval,
            "currency": price.currency,
            "amount": price.unit_amount / 100,
            "price_id": price.id,
        }
        res["default_price"] = price_details
        # return the product
        result = {
            "id": res.id,
            "name": res.name,
            "description": res.description,
            "price": res.default_price,
            "features": res.marketing_features,
        }
        # cache the product
        # await redis_client.redis.set(cache_key, json.dumps(result), ex=3600)
        return result
    except stripe.error.StripeError as e:
        logger.error(f"Error fetching products: {str(e)}")
        raise HTTPException(503, "An upstream error occurred")
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        raise HTTPException(500, "An unexpected error occurred")


async def get_products():
    """returns a product"""
    try:
        # check if the product is in the cache
        # cache_key = "products"
        # cached_products = await redis_client.get(cache_key)
        # if cached_products:
        #     return json.loads(cached_products)
        # get the products
        results = stripe.Product.list(active=True)
        products = []
        for product in results.auto_paging_iter():
            # get the price
            price_id = product.default_price
            if price_id is None:
                continue
            # call stripe for price object
            price = stripe.Price.retrieve(price_id)
            # formulate the price data
            price_details = {
                "interval": price.recurring.interval,
                "currency": price.currency,
                "amount": price.unit_amount / 100,
                "id": price.id,
            }
            # formulate the product data
            product_data = {
                "id": product.id,
                "name": product.name,
                "description": product.description,
                "price": price_details,
                "features": product.marketing_features,
            }
            products.append(product_data)
            # cache the products
            # await redis_client.set(cache_key, json.dumps(products), ex=3600)
        return products
    except stripe.error.StripeError as e:
        logger.error(f"Error fetching products: {str(e)}")
        raise HTTPException(503, "An upstream error occurred")
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        raise HTTPException(500, "An unexpected error occurred")


async def create_subscription(price_id):
    try:
        checkout_session = stripe.checkout.Session.create(
            # customer=subscription.customer_id,
            payment_method_types=["card"],
            line_items=[
                {
                    "price": price_id,
                    "quantity": 1,
                }
            ],
            mode="subscription",
            success_url="http://localhost:8002/api/v1/stripe/success?session_id={CHECKOUT_SESSION_ID}",
            cancel_url="http://localhost:8002/api/v1/stripe/cancel",
        )
        return checkout_session.url
    except stripe.error.StipeError as e:
        raise HTTPException(status_code=400, detail=str(e))


def handle_successful_payment(session):
    logger.info(f"Payment successful for session: {session.id}")


async def stripe_webhook(request: Request):
    logger.info("making request to the webhook")
    payload = await request.body()
    event = None
    logger.info(f"payload: {json.loads(payload)}")
    sig_header = request.headers.get("stripe-signature")
    try:
        event = stripe.Webhook.construct_event(
            payload, sig_header, settings.STRIPE_WEBHOOK_SECRET
        )
    except ValueError as e:
        logger.error(f"A value error occurred: {str(e)}")
        raise HTTPException(status_code=400, detail=f"Invalid payload: {str(e)}")
    except stripe.error.SignatureVerificationError as e:
        logger.error(f"A signature verification error occurred: {str(e)}")
        raise HTTPException(status_code=400, detail=f"Invalid signature: {str(e)}")

    # handle specific events
    if event["type"] == "checkout.session.completed":
        session = event["data"]["object"]
        logger.info(f"session: {session}")
        handle_successful_payment(session)  # provision service
    elif event["type"] == "invoice.payment_failed":
        # send_notification_of_failed_payment()  send them to customer portal to update payment methods
        print("Your payment failed, kindly update that")
    elif event["type"] == "invoice.paid":
        # send_notification_of_successful_payment() continue to use service
        print("you can continue to use the service")
    else:
        # logger.error(f'Unknown event type: {event["type"]}')
        # raise HTTPException(status_code=400, detail=f'Unknown event type: {event["type"]}')
        pass

    return {"status": "success"}
