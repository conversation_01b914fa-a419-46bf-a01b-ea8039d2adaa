# get all users, organisations, all roles, all permissions, invitations

from fastapi import HTTPException, status
from app.models.model import Organisation, Permission, Role, User
from sqlalchemy.orm import Session

from app.schemas.pydantic_schema import (
    AdminGetOrganisation, AdminGetRole, CreateUpdateOrganisation, PermissionResponse, RoleResponse)
from app.schemas.subscription import UserData
from app.utils.logger import get_logger


logger = get_logger(__name__)


def get_users_admin(db_session: Session):
    '''Returns all the users registered on the platform'''
    logger.info("Fetching all the users in the database")
    try:
        db_users = db_session.query(User).all()
        logger.info(f"Users details retrieved: {len(db_users)} users")
        return [UserData.model_validate(user) for user in db_users]
    except Exception as e:
        logger.error(f"An error occurred during fetch: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'An unexpected error occurred: {str(e)}'
        )


def get_orgs_admin(db_session: Session):
    '''Returns all the Organisation registered on the platform'''
    try:
        logger.info("Fetching all the orgs in the database")
        db_orgs = db_session.query(Organisation).all()
        logger.info(f"Organisation details retrieved {len(db_orgs)}")
        return [AdminGetOrganisation.model_validate(org) for org in db_orgs]
    except Exception as e:
        logger.error(f"An error occurred during fetch: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'An unexpected error occurred: {str(e)}'
        )


def get_roles_admin(db_session: Session):
    '''Returns all the Roles registered on the platform for a particular organisation'''
    try:
        logger.info("Fetching all the roles in the database")
        db_roles = db_session.query(Role).all()
        logger.info(f"Roles details retrieved: {len(db_roles)}")
        return [AdminGetRole.model_validate(role) for role in db_roles]
    except Exception as e:
        logger.error(f"An error occurred during fetch: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'An unexpected error occurred: {str(e)}'
        )


def get_perms_admin(db_session: Session):
    '''Returns all the permissions registered on the platform'''
    try:
        logger.info("Fetching all the permissions in the database")
        db_perms = db_session.query(Permission).all()
        logger.info(f"Permissions details retrieved: {len(db_perms)}")
        return [PermissionResponse.model_validate(perm) for perm in db_perms]
    except Exception as e:
        logger.error(f"An error occurred during fetch: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'An unexpected error occurred: {str(e)}'
        )
