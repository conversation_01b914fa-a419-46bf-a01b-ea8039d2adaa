from datetime import datetime
from sqlalchemy.orm import Session
from fastapi import HTT<PERSON>Exception
from app.models.model import (
    DataStorageSettings,
    ApplicationSettings,
    LocalizationSettings,
    NotificationSettings,
    SessionActivity,
    SocialSettings,
    UsageMetrics
)
from app.schemas.settings import (
    DataStorageSettingsCreate, DataStorageSettingsUpdate,
    ApplicationSettingsPublic,
    ApplicationSettingsBase, ApplicationSettingsCreate,
    LocalizationSettingsCreate, NotificationSettingsCreate, 
    NotificationSettingsUpdate, SessionActivityCreate, SocialSettingsCreate, UsageMetricsCreate
)
from app.utils.logger import get_logger

logger = get_logger(__name__)


# def get_or_create_general_settings(
#     db: Session, organization_id: str,
#     user_details: dict
# ):
#     try:
#         logger.info("Get/Create user general settings details")
#         instance = db.query(GeneralSettings).filter(
#             GeneralSettings.organization_id == organization_id,
#             GeneralSettings.email_address == user_details.get('email')
#         ).first()
#         if not instance:
#             logger.info(f"No general settings found for user {user_details.get('email')} in organisation {organization_id}\ncreating a new one")
#             general_settings = {
#                 "organization_id": organization_id,
#                 "full_name": f"{user_details.get('user_details').get('first_name')}" + f" {user_details.get('user_details').get('last_name')}",
#                 "email_address": f"{user_details.get('email')}",
#                 "phone_number": None,
#                 "user_role": "",
#                 "brand_voice": "",
#                 "img_url": None
#             }
#             instance = GeneralSettings(**general_settings)
#             db.add(instance)
#             db.commit()
#             db.refresh(instance)
#         return instance
#     except HTTPException as e:
#         raise e
#     except Exception as e:
#         logger.error(f"An unexpected error occurred: {str(e)}")
#         raise HTTPException(
#             status_code=500,
#             detail="An unexpected error occurred"
#         )


# def update_general_settings(
#     db: Session,
#     organization_id: str,
#     new_settings: GeneralSettingsUpdate,
#     user_details: dict
# ):
#     try:
#         logger.info(f"Updating the user {user_details.get('email')} general settings")
#         # get the user settings first
#         instance = db.query(GeneralSettings).filter(
#             GeneralSettings.organization_id == organization_id,
#             GeneralSettings.email_address == user_details.get('email')
#         ).first()
#         if not instance:
#             logger.error(f"General settings for {user_details.get('email')} not found")
#             raise HTTPException(status_code=404, detail="Requested settings data not found")
#         for key, value in new_settings.model_dump(exclude_unset=True).items():
#             setattr(instance, key, value)
#         db.commit()
#         db.refresh(instance)
#         return instance
#     except HTTPException as e:
#         raise e
#     except Exception as e:
#         logger.error(f"An unexpected error occurred: {str(e)}")
#         raise HTTPException(status_code=500, detail="An unexpected error occurred")


def get_or_create_application_settings(
    db: Session, organization_id: str,
    user_details: dict
):
    try:
        logger.info(f"Get/create the user {user_details.get('email')} application settings")
        instance = db.query(ApplicationSettings).filter(
            ApplicationSettings.organization_id == organization_id,
            ApplicationSettings.user_email == user_details.get('email')
        ).first()
        if not instance:
            logger.info(f"No application settings found for user {user_details.get('email')} in organisation {organization_id}\ncreating a new one")
            default_data = {
                "organization_id": organization_id,
                "user_email": user_details.get('email'),
                "default_language": "English",
                "content_auto_save_interval": "5",
                "in_app_notifications": True,
                "email_notifications": True
            }
            instance = ApplicationSettings(**default_data)
            db.add(instance)
            db.commit()
            db.refresh(instance)
        return instance
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")


def update_application_settings(
    db: Session,
    settings: ApplicationSettingsCreate,
    organization_id: str,
    user_details: dict
):
    try:
        logger.info(f"Updating the user {user_details.get('email')} application settings")
        db_settings = db.query(ApplicationSettings).filter(
            ApplicationSettings.organization_id == organization_id,
            ApplicationSettings.user_email == user_details.get('email')
        ).first()
        if not db_settings:
            logger.error(f"Application settings not found for user: {user_details.get('email')} in organisation {organization_id}")
            raise HTTPException(status_code=404, detail="Requested application settings not found")
        for key, value in settings.model_dump(exclude_unset=True).items():
            setattr(db_settings, key, value)

        db.commit()
        db.refresh(db_settings)
        return db_settings
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")


def get_or_create_social_settings(db: Session, organization_id: str):
    try:
        instance = db.query(SocialSettings).filter(SocialSettings.organization_id == organization_id).first()
        if not instance:
            default_data = {
                "organization_id": organization_id,
                "social_media": {}
            }
            instance = SocialSettings(**default_data)
            db.add(instance)
            db.commit()
            db.refresh(instance)
        return instance
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")


def update_social_settings(db: Session, settings: SocialSettingsCreate, organization_id: str):
    try:
        db_settings = db.query(SocialSettings).filter(SocialSettings.organization_id == organization_id).first()
        if db_settings:
            db_settings.social_media = settings.social_media  # Replace the entire social media model_dump
            db.commit()
            db.refresh(db_settings)
        return db_settings
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")


def get_or_create_localization_settings(
    db: Session, organization_id: str, user_details: dict
):
    try:
        logger.info(f"Get/create the user {user_details.get('email')} localization settings")
        instance = (
            db.query(LocalizationSettings)
            .filter(
                LocalizationSettings.organization_id == organization_id,
                LocalizationSettings.user_email == user_details.get('email')
            ).first()
        )
        if not instance:
            logger.info(f"No localization settings found for user {user_details.get('email')} in organisation {organization_id}\ncreating a new one")
            default_data = {
                "organization_id": organization_id,
                "user_email": user_details.get("email"),
                "language": "English",
                "date_format": "MM/DD/YYYY",
                "time_format": "12-hour",
                "number_format": "1,000.00",
                "country_region": "US",
                "time_zone": "UTC",
                "show_region_specific_content": False,
                "block_content_based_on_locaction": False,
            }
            instance = LocalizationSettings(**default_data)
            db.add(instance)
            db.commit()
            db.refresh(instance)
        return instance
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")


def update_localization_settings(
    db: Session, organization_id: str,
    new_settings: LocalizationSettingsCreate,
    user_details: dict
):
    try:
        logger.info(f"Updating the user {user_details.get('email')} localization settings")
        db_settings = db.query(LocalizationSettings).filter(
            LocalizationSettings.organization_id == organization_id,
            LocalizationSettings.user_email == user_details.get("email")
        ).first()
        if not db_settings:
            logger.error(f"Localization settings for {user_details.get('email')} not found")
            raise HTTPException(status_code=404, detail="Requested localization settings not found")
        for key, value in new_settings.model_dump(exclude_unset=True).items():
            setattr(db_settings, key, value)
        db.commit()
        db.refresh(db_settings)
        return db_settings
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")


def get_or_create_data_storage_settings(db: Session, organization_id: str, user_details: dict):
    try:
        instance = (
            db.query(DataStorageSettings)
            .filter(DataStorageSettings.organization_id == organization_id)
            .first()
        )
        if not instance:
            default_data = {
                "organization_id": organization_id,
                "storage_limit": 1000,  # in GB
                "storage_used": 0,
                "automatic_backup": False,
                "backup_frequency": "Weekly",  # options: "Daily", "Weekly", "Monthly"
                "backup_location": "Cloud",    # options: "Cloud", "Local Storage"
                "export_format": "PDF",
                "data_retention_period": "1 year",
                "deletion_warning": False,
            }
            instance = DataStorageSettings(**default_data)
            db.add(instance)
            db.commit()
            db.refresh(instance)
        return instance
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")


def update_data_storage_settings(db: Session, organization_id: str, new_settings: DataStorageSettingsUpdate):
    try:
        db_settings = db.query(DataStorageSettings).filter(DataStorageSettings.organization_id == organization_id).first()
        if not db_settings:
            raise HTTPException(status_code=404, detail="Requested data settings not found")
        for key, value in new_settings.model_dump(exclude_unset=True).items():
            setattr(db_settings, key, value)
        db.commit()
        db.refresh(db_settings)
        return db_settings
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")


def get_or_create_notification_settings(db: Session, organization_id: str, user_id: str):
    try:
        instance = (
            db.query(NotificationSettings)
            .filter(
                NotificationSettings.organization_id == organization_id,
                NotificationSettings.user_id == user_id
            )
            .first()
        )
        if not instance:
            default_data = {
                "organization_id": organization_id,
                "user_id": user_id,
                "comments": True,
                "new_message_alerts": True,
                "account_activity": True,
                "system_updates": True,
                "marketing_updates": False,
                "in_app_notifications": True,
                "email_notifications": True,
                "push_notifications": True,
                "notification_frequency": "Real-time",
                "do_not_disturb_start": None,  # or set a default time if needed
                "do_not_disturb_end": None,
            }
            instance = NotificationSettings(**default_data)
            db.add(instance)
            db.commit()
            db.refresh(instance)
        return instance
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")


def update_notification_settings(db: Session, organization_id: str, user_id: str, new_settings: NotificationSettingsUpdate):
    try:
        instance = (
            db.query(NotificationSettings)
            .filter(
                NotificationSettings.organization_id == organization_id,
                NotificationSettings.user_id == user_id
            )
            .first()
        )
        if not instance:
            raise HTTPException(status_code=404, detail="Requested notification settings not found")
        for key, value in new_settings.model_dump(exclude_unset=True).items():
            setattr(instance, key, value)
        db.commit()
        db.refresh(instance)
        return instance
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")


def create_session_activity(db: Session, session_data: SessionActivityCreate):
    try:
        db_session = SessionActivity(**session_data.model_dump())
        db.add(db_session)
        db.commit()
        db.refresh(db_session)
        return db_session
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")

def end_session_activity(db: Session, session_id: str, organization_id: str):
    try:
        db_session = db.query(SessionActivity).filter(
            SessionActivity.session_id == session_id,
            SessionActivity.organization_id == organization_id
        ).first()
        if db_session:
            db_session.end_time = datetime.utcnow()
            db.commit()
            db.refresh(db_session)
        return db_session
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")

def get_active_sessions(db: Session, organization_id: str):
    try:
        return db.query(SessionActivity).filter(
            SessionActivity.end_time is None,
            SessionActivity.organization_id == organization_id
        ).all()
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")

def get_session_activity(db: Session, session_id: str, organization_id: str):
    try:
        return db.query(SessionActivity).filter(
            SessionActivity.session_id == session_id,
            SessionActivity.organization_id == organization_id
        ).first()
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")


def update_usage_metrics(db: Session, metrics_data: UsageMetricsCreate):
    try:
        db_metrics = db.query(UsageMetrics).filter(
            UsageMetrics.organization_id == metrics_data.organization_id
        ).first()
        if db_metrics:
            for key, value in metrics_data.dict().items():
                setattr(db_metrics, key, value)
        else:
            db_metrics = UsageMetrics(**metrics_data.dict())
            db.add(db_metrics)
        db.commit()
        db.refresh(db_metrics)
        return db_metrics
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")


def get_usage_metrics(db: Session, organization_id: str):
    try:
        return db.query(UsageMetrics).filter(
            UsageMetrics.organization_id == organization_id
        ).first()
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")
