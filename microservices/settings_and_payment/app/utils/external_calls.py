from typing import Annotated

import httpx
from app.core.settings import settings
from fastapi import Depends, HTTPException, status, WebSocket
from fastapi.security import OAuth2PasswordBearer
from jose import JWTError, jwt
from app.utils.logger import get_logger


logger = get_logger(__name__)


AUTH_SERVICE_URL = settings.AUTH_SERVICE_URL


# oauth2_scheme = OAuth2PasswordBearer(tokenUrl="login")
oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{AUTH_SERVICE_URL}/login")

ALGORITHM = settings.ALGORITHM
SECRET = settings.SECRET_KEY


def get_current_user(token: Annotated[str, Depends(oauth2_scheme)]):
    credentials_exception = HTTPException(
        status_code=401,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET, algorithms=[ALGORITHM])
        user_id: str = payload.get("user_id")
        if user_id is None:
            raise credentials_exception
        user = payload
    except JWTError:
        raise credentials_exception
    return user


async def get_current_user_from_websocket(websocket: WebSocket) -> dict:
    token = websocket.query_params.get("token")
    logger.info(token)

    if not token:
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
        raise HTTPException(status_code=401, detail="Token missing in query params")

    try:
        payload = jwt.decode(token, SECRET, algorithms=[ALGORITHM])
        logger.info(f'payload: {payload}')
        user_id: str | None = payload.get("user_id")
        if user_id is None:
            raise ValueError()
        return payload
    except JWTError as e:
        logger.error(f"Invalid token: {str(e)}")
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
        raise HTTPException(status_code=401, detail="Invalid token")


async def fetch_user_organisation(user_id: str) -> str:
    """
    Fetch the organization ID for the current user
    by making an HTTP request to the organization service.

    Args:
        user_id (str): The ID of the user whose organization ID is being fetched.

    Returns:
        str: The organization ID of the user.

    Raises:
        HTTPException: If fetching the organization ID fails.
    """

    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{AUTH_SERVICE_URL}/user/organisations", params={"user_id": user_id}
            )
            response.raise_for_status()
            data = response.json()
            if not data.get("success"):
                raise HTTPException(
                    status_code=data.get("status_code"),
                    detail="Failed to fetch organization ID for user.",
                )
            return data.get("data")[0]
    except httpx.HTTPStatusError as e:
        r = e.response.json()
        raise HTTPException(status_code=e.response.status_code, detail=r.get("message"))
    except httpx.RequestError as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error connecting to the organisation service: {str(e)}",
        )


async def verify_organization(organisation_id: str) -> str:
    """
    Verify the organization by making an HTTP request to the authentication service.

    Args:
        organisation_id (str): The ID of the organization to verify.

    Returns:
        str: The verified organization ID.

    Raises:
        HTTPException: If the organization verification fails.
    """
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{AUTH_SERVICE_URL}/verify-organisation/{organisation_id}"
            )
            response.raise_for_status()
            data = response.json()
            if not data.get("success"):
                raise HTTPException(
                    status_code=data.get("status_code"),
                    detail="Organization verification failed.",
                )
            return data.get("data")["id"]
    except httpx.HTTPStatusError as e:
        r = e.response.json()
        raise HTTPException(status_code=e.response.status_code, detail=r.get("message"))
    except httpx.RequestError as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error connecting to the authentication service: {str(e)}",
        )
