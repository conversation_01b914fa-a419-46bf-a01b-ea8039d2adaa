from inspect import iscoroutinefunction
from functools import wraps
from fastapi import Depends, HTTPException, status
import logging
from app.models.model import Permission, Role, user_permissions, User, user_organisation_roles, Organisation

logger = logging.getLogger(__name__)
from app.services.auth import get_current_user, get_db
from sqlalchemy.orm import Session


def check_permission(permission_titles: list):
    def decorator(func):
        @wraps(func)
        async def wrapper(
            current_user: User = Depends(get_current_user),
            db_session: Session = Depends(get_db),
            *args,
            **kwargs
        ):
            # Extract organisation_id from kwargs or schema
            organisation_id = kwargs.get('organisation_id')
            logger.debug(f"Checking permissions for user: {current_user.email}")
            if not organisation_id:
                organisation_id = next(
                    (getattr(value, 'organisation_id', None) for value in kwargs.values() if hasattr(value, 'organisation_id')),
                    None
                )

            if not organisation_id:
                logger.error("Organisation ID is required but not provided.")
                logger.error(f"Organisation with ID {organisation_id} not found.")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Organisation ID is required."
                )

            # Verify if the organisation_id is valid
            organisation = db_session.query(Organisation).filter_by(id=organisation_id).first()
            if not organisation:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Organisation not found."
                )
            logger.debug(f"Checking role for user {current_user.email} in organisation {organisation_id}")
            user_role = (
                db_session.query(Role)
                .join(user_organisation_roles)
                .filter(user_organisation_roles.c.user_id == current_user.id)
                .filter(user_organisation_roles.c.organisation_id == organisation_id)
                .first()
            )

            if user_role and user_role.name == "admin":
                # If the user is an admin in this organization, bypass the permission check
                # Check if the function is async and await it accordingly
                if iscoroutinefunction(func):
                    return await func(
                        current_user=current_user,
                        db_session=db_session, *args, **kwargs)
                else:
                    return func(
                        current_user=current_user,
                        db_session=db_session, *args, **kwargs)

            # Fetch user's permissions for this organization
            user_permissions_list = (
                db_session.query(Permission)
                .join(
                    user_permissions, user_permissions.c.permission_id == Permission.id
                )
                .filter(user_permissions.c.user_id == current_user.id)
                .filter(user_permissions.c.organisation_id == organisation_id)
                .all()
            )

            # Extract permission titles for easy comparison
            user_permission_titles = [permission.title for permission in user_permissions_list]

            # Check if the user has all required permissions
            if not all(title in user_permission_titles for title in permission_titles):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="You do not have permission to perform this action."
                )

            # Proceed with the actual function
            # Check if the function is async and await it accordingly
            if iscoroutinefunction(func):
                return await func(
                    current_user=current_user,
                    db_session=db_session, *args, **kwargs)
            else:
                return func(
                    current_user=current_user,
                    db_session=db_session, *args, **kwargs)

        return wrapper
    return decorator
