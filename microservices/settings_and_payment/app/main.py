from contextlib import asynccontextmanager

import redis
from app.core.settings import settings
from app.database.session import Base, engine
from app.routes.notification import router as notification_router
from app.routes.settings import router as settings_router
from app.routes.subscription import router as subscription_router
from app.events.client import router as events_router
from app.utils.logger import get_logger
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.utils import get_openapi

# Import event system
from app.events.bus import init_event_bus
from app.events.subscribers import setup_event_subscribers

# Import models to ensure they're registered with SQLAlchemy
from app.models.model import Notification, DeviceToken

# Set up logging configuration
logger = get_logger(__name__)

# Redis instance
redis_client = None
event_bus = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    global redis_client, event_bus
    try:
        logger.info("Connecting to Redis...")
        redis_client = redis.Redis(
            host=settings.REDIS_HOST, port=6379, decode_responses=True
        )

        if not redis_client.ping():
            raise ConnectionError("Unable to connect to Redis.")
        logger.info("Connected to Redis successfully.")

        # Initialize event bus
        logger.info("Initializing event bus...")
        event_bus = init_event_bus(
            redis_url=settings.REDIS_URL,
            service_name="settings_and_payment"
        )
        await event_bus.start()

        # Set up event subscribers
        await setup_event_subscribers()
        logger.info("Event system initialized successfully.")

        # Create database tables
        logger.info("Creating database tables...")
        try:
            with engine.begin() as conn:
                Base.metadata.create_all(bind=conn)
            logger.info("Database tables created successfully.")
        except Exception as e:
            logger.error(f"Error creating database tables: {e}")

    except Exception as e:
        logger.error(f"Error during startup: {e}")
        redis_client = None
        event_bus = None
        raise e

    yield

    # Cleanup
    if event_bus:
        await event_bus.stop()
        logger.info("Event bus stopped.")

    if redis_client:
        redis_client.close()
        logger.info("Redis connection closed.")


# Initialize the FastAPI app with lifespan
app = FastAPI(
    lifespan=lifespan,
    openapi_url="/api/v1/settings/openapi.json",
    docs_url="/api/v1/settings/docs",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Database tables are now created in the lifespan function above


app.include_router(settings_router, prefix="/api/v1/settings", tags=["Settings"])
app.include_router(
    subscription_router, prefix="/api/v1/subscription", tags=["Subscription"]
)
app.include_router(
    notification_router, prefix="/api/v1/notification", tags=["Notification"]
)
app.include_router(
    events_router, prefix="/api/v1/events", tags=["Events"]
)


# Custom OpenAPI schema to include bearer token
def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema
    openapi_schema = get_openapi(
        title="KIMEV",
        version="1.0.0",
        description="Do cool AI Stuffs",
        routes=app.routes,
    )
    openapi_schema["components"]["securitySchemes"] = {
        "Bearer": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}
    }
    for path in openapi_schema["paths"].values():
        for method in path.values():
            if "security" in method:
                method["security"].append({"Bearer": []})
            else:
                method["security"] = [{"Bearer": []}]
    app.openapi_schema = openapi_schema
    return app.openapi_schema


app.openapi = custom_openapi


@app.get("/settings_status")
def status():
    """
    This is just an introduction to this server
    requests to this endpoint is to confirm if the server is up and running
    """
    logger.info("Status endpoint accessed.")
    return {"message": "Welcome to the Settings Service"}


# Example route using Redis
@app.get("/redis-test")
async def redis_test():
    if not redis_client:
        return {"error": "Redis is not connected"}
    redis_client.set("test_key", "test_value")
    value = redis_client.get("test_key")
    return {"test_key": value}


# for route in app.routes:
#         print(f"Path: {route.path}, Name: {route.name}, Methods: {route.methods}")


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app)
