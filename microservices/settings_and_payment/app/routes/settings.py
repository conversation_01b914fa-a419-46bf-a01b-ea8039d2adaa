from typing import Annotated

from app.database.session import get_db
from app.schemas import settings
from app.services import crud
from app.utils.external_calls import get_current_user, verify_organization
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.utils.logger import get_logger
from app.utils.success_response import fail_response


router = APIRouter()
logger = get_logger(__name__)


# General Settings
# @r
# Application Settings
@router.get("/application", response_model=settings.ApplicationSettingsPublic)
def read_application_settings(
    user_details: Annotated[str, Depends(get_current_user)],
    organization_id: Annotated[str, Depends(verify_organization)],
    db: Session = Depends(get_db),
):
    try:
        return crud.get_or_create_application_settings(db, organization_id=organization_id, user_details=user_details)
    except HTTPException as e:
        logger.error(f"{e.detail}: {str(e)}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        return fail_response(500, "An unexpected error occuured")


@router.put("/application", response_model=settings.ApplicationSettingsBase)
def update_application_settings(
    user_details: Annotated[str, Depends(get_current_user)],
    organization_id: Annotated[str, Depends(verify_organization)],
    new_settings: settings.ApplicationSettingsCreate,
    db: Session = Depends(get_db),
):
    try:
        return crud.update_application_settings(db=db, settings=new_settings, organization_id=organization_id, user_details=user_details)
    except HTTPException as e:
        logger.error(f"{e.detail}: {str(e)}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        return fail_response(500, "An unexpected error occuured")


# Social Settings
@router.get("/social", response_model=settings.SocialSettingsPublic)
def read_social_settings(
    _: Annotated[str, Depends(get_current_user)],
    organization_id: Annotated[str, Depends(verify_organization)],
    db: Session = Depends(get_db),
):
    try:
        return crud.get_or_create_social_settings(db, organization_id=organization_id)
    except HTTPException as e:
        logger.error(f"{e.detail}: {str(e)}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        return fail_response(500, "An unexpected error occuured")


@router.put("/social", response_model=settings.SocialSettingsPublic)
def update_social_settings(
    _: Annotated[str, Depends(get_current_user)],
    organization_id: Annotated[str, Depends(verify_organization)],
    new_settings: settings.SocialSettingsCreate,
    db: Session = Depends(get_db),
):
    try:
        return crud.update_social_settings(db=db, settings=new_settings, organization_id=organization_id)
    except HTTPException as e:
        logger.error(f"{e.detail}: {str(e)}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        return fail_response(500, "An unexpected error occuured")


# Localization Settings
@router.get("/localization", response_model=settings.LocalizationSettings)
def read_localization(
    user_details: Annotated[str, Depends(get_current_user)],
    organization_id: Annotated[str, Depends(verify_organization)],
    db: Session = Depends(get_db),
):
    try:
        return crud.get_or_create_localization_settings(db=db, organization_id=organization_id, user_details=user_details)
    except HTTPException as e:
        logger.error(f"{e.detail}: {str(e)}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        return fail_response(500, "An unexpected error occuured")


@router.put("/localization", response_model=settings.LocalizationSettings)
def update_localization(
    user_details: Annotated[str, Depends(get_current_user)],
    organization_id: Annotated[str, Depends(verify_organization)],
    new_settings: settings.LocalizationSettingsCreate,
    db: Session = Depends(get_db),
):
    try:
        updated = crud.update_localization_settings(
            db=db, organization_id=organization_id,
            new_settings=new_settings, user_details=user_details
        )
        if updated is None:
            raise HTTPException(status_code=404, detail="Settings not found")
        return updated
    except HTTPException as e:
        logger.error(f"{e.detail}: {str(e)}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        return fail_response(500, "An unexpected error occuured")


# Data Storage Settings
@router.get("/data-storage", response_model=settings.DataStorageSettings)
def read_data_storage_settings(
    user_details: Annotated[str, Depends(get_current_user)],
    organization_id: Annotated[str, Depends(verify_organization)],
    db: Session = Depends(get_db),
):
    try:
        return crud.get_or_create_data_storage_settings(db=db, organization_id=organization_id, user_details=user_details)
    except HTTPException as e:
        logger.error(f"{e.detail}: {str(e)}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        return fail_response(500, "An unexpected error occuured")



@router.put("/data-storage", response_model=settings.DataStorageSettings)
def update_data_storage_settings(
    user_details: Annotated[str, Depends(get_current_user)],
    organization_id: Annotated[str, Depends(verify_organization)],
    new_settings: settings.DataStorageSettingsUpdate,
    db: Session = Depends(get_db),
):
    try:
        updated = crud.update_data_storage_settings(db=db, organization_id=organization_id, new_settings=new_settings)
        return updated
    except HTTPException as e:
        logger.error(f"{e.detail}: {str(e)}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        return fail_response(500, "An unexpected error occuured")


# Notification Settings
@router.get("/notifications", response_model=settings.NotificationSettings)
def read_notification_settings(
    current_user: Annotated[str, Depends(get_current_user)],
    organization_id: Annotated[str, Depends(verify_organization)],
    db: Session = Depends(get_db),
):
    try:
        user_id = current_user['user_id']
        return crud.get_or_create_notification_settings(db=db, organization_id=organization_id, user_id=user_id)
    except HTTPException as e:
        logger.error(f"{e.detail}: {str(e)}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        return fail_response(500, "An unexpected error occuured")


@router.put("/notifications", response_model=settings.NotificationSettings)
def update_notification_settings(
    current_user: Annotated[str, Depends(get_current_user)],
    organization_id: Annotated[str, Depends(verify_organization)],
    new_settings: settings.NotificationSettingsUpdate,
    db: Session = Depends(get_db),
):
    try:
        user_id = current_user['user_id']
        updated = crud.update_notification_settings(
            db=db,
            organization_id=organization_id,
            user_id=user_id,
            new_settings=new_settings
        )
        return updated
    except HTTPException as e:
        logger.error(f"{e.detail}: {str(e)}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        return fail_response(500, "An unexpected error occuured")


@router.post("/sessions", response_model=settings.SessionActivity)
def create_session(
    _: Annotated[str, Depends(get_current_user)],
    session_data: settings.SessionActivityCreate,
    db: Session = Depends(get_db),
):
    try:
        return crud.create_session_activity(db=db, session_data=session_data)
    except HTTPException as e:
        logger.error(f"{e.detail}: {str(e)}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        return fail_response(500, "An unexpected error occuured")


@router.put("/sessions/{session_id}/end", response_model=settings.SessionActivity)
def end_session(
    _: Annotated[str, Depends(get_current_user)],
    session_id: str,
    organization_id: Annotated[str, Depends(verify_organization)],
    db: Session = Depends(get_db),
):
    try:
        session = crud.end_session_activity(
            db=db, session_id=session_id, organization_id=organization_id
        )
        return session
    except HTTPException as e:
        logger.error(f"{e.detail}: {str(e)}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        return fail_response(500, "An unexpected error occuured")


@router.get("/sessions", response_model=list[settings.SessionActivity])
def get_active_sessions(
    _: Annotated[str, Depends(get_current_user)],
    organization_id: Annotated[str, Depends(verify_organization)],
    db: Session = Depends(get_db),
):
    try:
        return crud.get_active_sessions(db=db, organization_id=organization_id)
    except HTTPException as e:
        logger.error(f"{e.detail}: {str(e)}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        return fail_response(500, "An unexpected error occuured")


@router.get("/sessions/{session_id}", response_model=settings.SessionActivity)
def get_session(
    _: Annotated[str, Depends(get_current_user)],
    session_id: str,
    organization_id: Annotated[str, Depends(verify_organization)],
    db: Session = Depends(get_db),
):
    try:
        session = crud.get_session_activity(
            db=db, session_id=session_id, organization_id=organization_id
        )
        return session
    except HTTPException as e:
        logger.error(f"{e.detail}: {str(e)}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        return fail_response(500, "An unexpected error occuured")


@router.put("/metrics", response_model=settings.UsageMetrics)
def update_metrics(
    _: Annotated[str, Depends(get_current_user)],
    metrics_data: settings.UsageMetricsCreate,
    db: Session = Depends(get_db),
):
    try:
        return crud.update_usage_metrics(db=db, metrics_data=metrics_data)
    except HTTPException as e:
        logger.error(f"{e.detail}: {str(e)}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        return fail_response(500, "An unexpected error occuured")


@router.get("/metrics", response_model=settings.UsageMetrics)
def get_metrics(
    _: Annotated[str, Depends(get_current_user)],
    organization_id: Annotated[str, Depends(verify_organization)],
    db: Session = Depends(get_db),
):
    try:
        metrics = crud.get_usage_metrics(db=db, organization_id=organization_id)
        return metrics
    except HTTPException as e:
        logger.error(f"{e.detail}: {str(e)}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        return fail_response(500, "An unexpected error occuured")
