from datetime import datetime
import os
from typing import Annotated

from app.database.session import get_db
from app.models.model import Device<PERSON>oken, Notification, NotificationSettings
from app.services.push_service import push_service
from app.services.redis_service import redis_service
from app.utils.email_service import email_service
from app.utils.external_calls import (
    get_current_user, get_current_user_from_websocket)
from app.utils.logger import get_logger
from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException, WebSocket

from app.schemas.notification import NotificationRequest
from app.events.publishers import publish_notification_event

router = APIRouter()

logger = get_logger(__name__)


@router.post("/notify")
async def send_notification(
    request: NotificationRequest,
    token: Annotated[str, Depends(get_current_user)],
    background_tasks: BackgroundTasks,
    db=Depends(get_db),
):
    try:
        # Retrieve user notification settings
        user_id = token.get("user_id")
        organization_id = request.organization_id
        message = request.message
        user_settings = (
            db.query(NotificationSettings)
            .filter(
                NotificationSettings.organization_id == organization_id,
                NotificationSettings.user_id == user_id,
            )
            .first()
        )
        if not user_settings:
            user_settings = NotificationSettings(
                organization_id=organization_id,
                user_id=user_id,
                comments=True,
                new_message_alerts=True,
                account_activity=True,
                system_updates=True,
                marketing_updates=False,
                in_app_notifications=True,
                email_notifications=True,
                push_notifications=False,
                notification_frequency="Real-time",
                do_not_disturb_start=None,  # Default to 12:00 AM
                do_not_disturb_end=None,  # Default to 11:59 PM
            )
            db.add(user_settings)
            db.commit()
            db.refresh(user_settings)
        # Check Do Not Disturb settings
        current_time = datetime.now().time()
        if (
            user_settings.do_not_disturb_start
            and user_settings.do_not_disturb_end
            and user_settings.do_not_disturb_start
            <= current_time
            <= user_settings.do_not_disturb_end
        ):
            return {"status": "ignored", "reason": "Do Not Disturb active"}

        # Create notification record
        new_notification = Notification(
            user_id=user_id, organization_id=organization_id, message=message
        )
        db.add(new_notification)
        db.commit()
        db.refresh(new_notification)

        notification_id = str(new_notification.id)

        # In-app Notification
        if user_settings.in_app_notifications:
            logger.info("Sending in-app notification")
            await redis_service.publish(
                f"user:{user_id}:notifications:{organization_id}", message
            )

            # Publish in-app notification event
            await publish_notification_event(
                notification_id=notification_id,
                user_id=user_id,
                organization_id=organization_id,
                title="New Notification",
                message=message,
                notification_type="general",
                channel="in_app",
                status="sent"
            )

        # Email Notification
        if user_settings.email_notifications:
            logger.info("Sending email notification")
            email = token.get("email")
            BASE_DIR = os.path.dirname(os.path.abspath(__file__))
            template_path = os.path.join(BASE_DIR, "../templates/notification.html")
            context = {"message": message}
            background_tasks.add_task(
                email_service.send_email, email, "Notification", template_path, context
            )

            # Publish email notification event
            await publish_notification_event(
                notification_id=f"{notification_id}_email",
                user_id=user_id,
                organization_id=organization_id,
                title="New Notification",
                message=message,
                notification_type="general",
                channel="email",
                status="sent"
            )

        # Push Notification
        if user_settings.push_notifications:
            device_tokens = (
                db.query(DeviceToken)
                .filter(
                    DeviceToken.user_id == user_id,
                    DeviceToken.organization_id == organization_id,
                )
                .all()
            )

            tokens = [device.device_token for device in device_tokens]
            logger.info(f"Device tokens: {tokens}")

            if tokens:
                logger.info("Sending push notification")
                push_service.send_push(
                    device_tokens=tokens,
                    title="New Notification",
                    body=message,
                )

                # Publish push notification event
                await publish_notification_event(
                    notification_id=f"{notification_id}_push",
                    user_id=user_id,
                    organization_id=organization_id,
                    title="New Notification",
                    message=message,
                    notification_type="general",
                    channel="push",
                    status="sent"
                )

        return {"status": "success"}

    except HTTPException as e:
        raise e
    except Exception as e:
        return {"status": "error", "message": str(e)}


@router.websocket("/ws/notifications")
async def websocket_endpoint(
    websocket: WebSocket,
    token: Annotated[dict, Depends(get_current_user_from_websocket)],
    organization_id: str,
):
    """
    WebSocket endpoint for real-time notifications.
    """
    await websocket.accept()
    try:
        user_id = token.get("user_id")
        await redis_service.connect()
        pubsub = await redis_service.subscribe(
            f"user:{user_id}:notifications:{organization_id}"
        )
        async for message in pubsub.listen():
            if message.get("type") == "message":
                await websocket.send_json({"notification": message["data"]})
    except Exception as e:
        print(f"WebSocket error: {e}")
    finally:
        await websocket.close()


@router.get("/notifications")
async def get_notifications(
    token: Annotated[dict, Depends(get_current_user)],
    organization_id: str,
    limit: int = 50,
    offset: int = 0,
    db=Depends(get_db),
):
    """
    Get notifications for a user, ordered by most recent first.

    Args:
        organization_id: The organization ID
        limit: Maximum number of notifications to return (default: 50, max: 100)
        offset: Number of notifications to skip (default: 0)
    """
    user_id = token.get("user_id")

    # Limit the maximum number of notifications that can be requested at once
    limit = min(limit, 100)

    notifications = (
        db.query(Notification)
        .filter(
            Notification.user_id == user_id,
            Notification.organization_id == organization_id,
        )
        .order_by(Notification.created_at.desc())
        .offset(offset)
        .limit(limit)
        .all()
    )
    return notifications


@router.put("/notifications/{notification_id}/read")
async def mark_as_read(notification_id: str, db=Depends(get_db)):
    notification = (
        db.query(Notification).filter(Notification.id == notification_id).first()
    )
    if not notification:
        return {"error": "Notification not found"}
    notification.is_read = True
    db.commit()
    return {"status": "success"}


@router.get("/notifications/unread-count")
async def get_unread_count(
    token: Annotated[dict, Depends(get_current_user)],
    organization_id: str,
    db=Depends(get_db),
):
    user_id = token.get("user_id")
    count = (
        db.query(Notification)
        .filter(
            Notification.user_id == user_id,
            Notification.organization_id == organization_id,
            Notification.is_read == False,
        )
        .count()
    )
    return {"unread_count": count}


@router.post("/save-device-token")
async def save_device_token(
    token: Annotated[str, Depends(get_current_user)],
    device_token: str,
    organization_id: str,
    db=Depends(get_db),
):
    """
    Save a device token for a particular user.
    """
    user_id = token.get("user_id")
    # Check if the device token already exists
    existing_token = (
        db.query(DeviceToken).filter(DeviceToken.device_token == device_token).first()
    )
    if existing_token:
        raise HTTPException(status_code=400, detail="Device token already exists")

    # Save the new device token
    new_token = DeviceToken(
        user_id=user_id, device_token=device_token, organization_id=organization_id
    )
    try:
        db.add(new_token)
        db.commit()
        db.refresh(new_token)
    except Exception:
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to save device token")

    return {
        "message": "Device token saved successfully",
        "device_token": new_token.device_token,
    }


@router.get("/device-tokens")
async def get_device_tokens(
    token: Annotated[str, Depends(get_current_user)],
    organization_id: str,
    db=Depends(get_db),
):
    """
    Retrieve all device tokens for a user.
    """
    user_id = token.get("user_id")
    tokens = (
        db.query(DeviceToken)
        .filter(
            DeviceToken.user_id == user_id,
            DeviceToken.organization_id == organization_id,
        )
        .all()
    )
    return {
        "user_id": user_id,
        "device_tokens": [token.device_token for token in tokens],
    }


@router.delete("/remove-device-token")
async def remove_device_token(device_token: str, db=Depends(get_db)):
    """
    Remove a device token from the database.
    """
    token = (
        db.query(DeviceToken).filter(DeviceToken.device_token == device_token).first()
    )
    if not token:
        raise HTTPException(status_code=404, detail="Device token not found")

    db.delete(token)
    db.commit()
    return {"message": "Device token removed successfully"}
