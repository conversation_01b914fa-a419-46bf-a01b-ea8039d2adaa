from typing import List

from app.schemas.subscription import PlanPublic, SubscriptionResponse
from app.services import stripe
from app.utils.logger import get_logger
from app.utils.success_response import fail_response, success_response
from fastapi import APIRouter, HTTPException, Request

logger = get_logger(__name__)

router = APIRouter()


@router.get("/plans", response_model=List[PlanPublic], tags=["Subscription"])
async def get_plans():
    """Get's all plans"""
    try:
        plans = await stripe.get_products()
        return success_response(200, "Plans retrieved", plans)
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(500, f"An unexpected error occurred: {str(e)}")


@router.get("/plans/{plan_code}", response_model=PlanPublic, tags=["Subscription"])
async def get_plan(plan_code: str):
    """Get's a plan"""
    try:
        plan = await stripe.get_product(plan_code)
        return success_response(200, "Plan Retrieved", plan)
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(500, f"An unexpected error occurred: {str(e)}")


@router.post("/subscribe", response_model=SubscriptionResponse, tags=["Subscription"])
async def initialize_Transaction(price_code: str):
    """Subscribes to a plan"""
    try:
        response = await stripe.create_subscription(price_code)
        return success_response(200, "checkout string generated", response)
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(e.status_code, f"An unexpected error occurred: {str(e)}")


@router.get("/success")
async def success_callback(request: Request):
    try:
        checkout_id = request.query_params.get("session_id")
        return success_response(
            200,
            "checkout initiated successfully, this needs to redirect to success page",
            checkout_id,
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(e.status_code, f"An unexpected error occurred: {str(e)}")


@router.get("/cancel")
async def cancel_callback(request: Request):
    try:
        error_response = request.query_params.get("error")
        return success_response(
            400,
            "checkout initiated successfully, this needs to redirect to cancel page",
            error_response,
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        return fail_response(e.status_code, f"An unexpected error occurred: {str(e)}")


# @router.get("/verify", response_model=SubscriptionPublic, tags=["Subscription"])
# async def verify_Transaction(reference: str, db_session: Session = Depends(get_db)):
#     """Subscribes to a plan"""
#     try:
#         response = await subscription.verifyTransaction(reference, db_session)
#         return success_response(200, response["message"], response["data"])
#     except HTTPException as e:
#         return fail_response(e.status_code, e.detail)
#     except Exception as e:
#         return fail_response(e.status_code, f"An unexpected error occurred: {str(e)}")


@router.post("/stripe-webhook")
async def stripe_webhook(request: Request):
    return await stripe.stripe_webhook(request)
