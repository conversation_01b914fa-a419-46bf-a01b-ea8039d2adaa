import enum
from datetime import datetime
from typing import List, Optional

# from pydantic.json import pydantic_encoder
from app.utils.logger import get_logger
from pydantic import BaseModel, ConfigDict, conint, field_validator

# Initialize the logger for this module
logger = get_logger(__name__)


class INTERVALS(enum.Enum):
    HOURLY = "hourly"
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    YEARLY = "annually"
    BIANNUALLY = "biannually"
    QUARTERLY = "quarterly"


class CURRENCY(enum.Enum):
    USD = "USD"
    NGN = "NGN"
    GHS = "GHS"
    ZAR = "ZAR"
    KES = "KES"


class PlanBase(BaseModel):
    name: str
    amount: conint(ge=100)  # type: ignore
    interval: INTERVALS
    description: Optional[str] = ""
    currency: CURRENCY
    features: List[str]
    model_config = ConfigDict(
        json_encoders={INTERVALS: lambda v: v.value, CURRENCY: lambda v: v.value}
    )

    @field_validator("currency", mode="before")
    @classmethod
    def validate_currency(cls, value):
        if isinstance(value, list):
            value = value[0]
        if value not in CURRENCY.__members__:
            raise ValueError(
                f"Currency must be one of: {', '.join(CURRENCY.__members__.keys())}"
            )
        return value

    @field_validator("name", mode="before")
    @classmethod
    def lowercase(cls, value):
        return value.lower()

    @field_validator("amount", mode="before")
    @classmethod
    def multiply_amount(cls, value):
        return value * 100


class PlanPublic(PlanBase):
    plan_code: str
    model_config = ConfigDict(from_attributes=True)

    @field_validator("name", mode="after")
    @classmethod
    def lowercase(cls, value):
        return value.lower()

    @field_validator("amount", mode="after")
    @classmethod
    def divide_amount(cls, value):
        return value / 100


class PlanUpdate(PlanBase):
    name: Optional[str] = None
    amount: Optional[conint(ge=100)] = None  # type: ignore
    interval: Optional[INTERVALS] = None
    description: Optional[str] = None
    currency: Optional[CURRENCY] = None
    features: Optional[List[str]] = None
    update_existing_subscriptions: Optional[bool] = True

    model_config = ConfigDict(
        validate_all_optional=True,
        json_encoders={INTERVALS: lambda v: v.value, CURRENCY: lambda v: v.value},
    )

    # Redefining validators to make them work for optional fields
    @field_validator("currency", mode="before")
    @classmethod
    def validate_currency(cls, value):
        if value is not None:
            if isinstance(value, list):
                value = value[0]
            if value not in CURRENCY.__members__:
                raise ValueError(
                    f"Currency must be one of: {', '.join(CURRENCY.__members__.keys())}"
                )
            return value

    @field_validator("name", mode="after")
    @classmethod
    def lowercase(cls, value):
        if value is not None:
            return value.lower()
        return value

    @field_validator("amount", mode="after")
    @classmethod
    def multiply_amount(cls, value):
        if value is not None:
            return value * 100
        return value

    def dict_without_none(self, *args, **kwargs):
        """Override the dict method to exclude fields with None values."""
        return self.dict(*args, exclude_none=True, **kwargs)


class User(BaseModel):
    email: str


class SubscriptionResponse(BaseModel):
    authorization_url: str
    access_code: str
    reference: str


class SubscriptionPublic(BaseModel):
    reference: str
    amount: int
    gateway_response: str
    status: str
    paid_at: datetime
    created_at: datetime
    currency: str
    channel: str
    ip_address: str
    customer: dict
