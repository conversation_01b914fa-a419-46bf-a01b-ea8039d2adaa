#!/usr/bin/env python3
"""
Test script to verify individual notification system components.
This tests the notification system components without requiring all services to be running.
"""

import asyncio
import sys
import os
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.events.models import Event, EventMetadata, EventType
from app.events.handlers import handle_user_registered, handle_file_uploaded, handle_chat_message
from app.events.publishers import publish_notification_event
from app.models.model import NotificationSettings


class NotificationComponentTester:
    """Test individual notification system components."""
    
    def __init__(self):
        self.test_results = []
    
    def log_test(self, test_name: str, success: bool, message: str = ""):
        """Log test results."""
        status = "✅ PASS" if success else "❌ FAIL"
        timestamp = datetime.now().strftime("%H:%M:%S")
        result = f"[{timestamp}] {status} {test_name}"
        if message:
            result += f" - {message}"
        print(result)
        self.test_results.append({
            "test": test_name,
            "success": success,
            "message": message,
            "timestamp": timestamp
        })
    
    def test_notification_settings_model(self):
        """Test NotificationSettings model creation."""
        try:
            settings = NotificationSettings(
                user_id="test-user-123",
                organization_id="test-org-456",
                comments=True,
                new_message_alerts=True,
                account_activity=True,
                system_updates=True,
                marketing_updates=False,
                in_app_notifications=True,
                email_notifications=True,
                push_notifications=True,
                notification_frequency="Real-time"
            )
            
            # Verify attributes
            assert settings.user_id == "test-user-123"
            assert settings.organization_id == "test-org-456"
            assert settings.in_app_notifications is True
            assert settings.email_notifications is True
            assert settings.push_notifications is True
            
            self.log_test("NotificationSettings Model", True, "Model created and validated successfully")
            return True
            
        except Exception as e:
            self.log_test("NotificationSettings Model", False, str(e))
            return False
    
    def test_event_model_creation(self):
        """Test Event model creation and serialization."""
        try:
            # Create event metadata
            metadata = EventMetadata(
                source_service="test_service",
                correlation_id="test-correlation-123",
                user_id="user-123",
                organization_id="org-456"
            )
            
            # Create event
            event = Event(
                type=EventType.AUTH_USER_REGISTERED,
                data={
                    "user_id": "user-123",
                    "email": "<EMAIL>",
                    "organization_id": "org-456",
                    "username": "testuser"
                },
                metadata=metadata
            )
            
            # Test serialization
            event_dict = event.to_dict()
            assert isinstance(event_dict, dict)
            assert event_dict["type"] == EventType.AUTH_USER_REGISTERED
            assert event_dict["data"]["user_id"] == "user-123"
            
            # Test deserialization
            reconstructed_event = Event.from_dict(event_dict)
            assert reconstructed_event.id == event.id
            assert reconstructed_event.type == event.type
            assert reconstructed_event.data == event.data
            
            self.log_test("Event Model Creation", True, "Event created and serialized successfully")
            return True
            
        except Exception as e:
            self.log_test("Event Model Creation", False, str(e))
            return False
    
    async def test_user_registration_handler(self):
        """Test user registration event handler."""
        try:
            with patch('app.events.handlers.get_db') as mock_get_db, \
                 patch('app.events.publishers.get_event_bus') as mock_get_bus:
                
                # Mock database session
                mock_db = MagicMock()
                mock_get_db.return_value.__next__.return_value = mock_db
                mock_db.query.return_value.filter.return_value.first.return_value = None
                mock_db.add.return_value = None
                mock_db.commit.return_value = None
                
                # Mock event bus
                mock_bus = MagicMock()
                mock_bus.publish = AsyncMock(return_value="event-123")
                mock_get_bus.return_value = mock_bus
                
                # Create test event
                event = Event(
                    type=EventType.AUTH_USER_REGISTERED,
                    data={
                        "user_id": "test-user-123",
                        "organization_id": "test-org-456",
                        "email": "<EMAIL>"
                    },
                    metadata=EventMetadata(
                        source_service="auth_service",
                        correlation_id="test-correlation-123"
                    )
                )
                
                # Handle the event
                await handle_user_registered(event)
                
                # Verify database operations
                mock_db.add.assert_called_once()
                mock_db.commit.assert_called_once()
                
                self.log_test("User Registration Handler", True, "Handler executed successfully")
                return True
                
        except Exception as e:
            self.log_test("User Registration Handler", False, str(e))
            return False
    
    async def test_file_upload_handler(self):
        """Test file upload event handler."""
        try:
            with patch('app.events.handlers.get_db') as mock_get_db, \
                 patch('app.events.publishers.get_event_bus') as mock_get_bus:
                
                # Mock notification settings
                mock_settings = MagicMock()
                mock_settings.account_activity = True
                
                # Mock database session
                mock_db = MagicMock()
                mock_get_db.return_value.__next__.return_value = mock_db
                mock_db.query.return_value.filter.return_value.first.return_value = mock_settings
                
                # Mock event bus
                mock_bus = MagicMock()
                mock_bus.publish = AsyncMock(return_value="event-123")
                mock_get_bus.return_value = mock_bus
                
                # Create test event
                event = Event(
                    type=EventType.FASTBOT_FILE_UPLOADED,
                    data={
                        "user_id": "test-user-123",
                        "organization_id": "test-org-456",
                        "filename": "test-document.pdf",
                        "file_size": 1024,
                        "file_id": "file-123"
                    },
                    metadata=EventMetadata(
                        source_service="fastbot",
                        correlation_id="test-correlation-123"
                    )
                )
                
                # Handle the event
                await handle_file_uploaded(event)
                
                # Verify event was published
                mock_bus.publish.assert_called_once()
                
                self.log_test("File Upload Handler", True, "Handler executed successfully")
                return True
                
        except Exception as e:
            self.log_test("File Upload Handler", False, str(e))
            return False
    
    async def test_chat_message_handler(self):
        """Test chat message event handler."""
        try:
            with patch('app.events.handlers.get_db') as mock_get_db, \
                 patch('app.events.handlers.redis_service') as mock_redis:
                
                # Mock notification settings
                mock_settings = MagicMock()
                mock_settings.new_message_alerts = True
                
                # Mock database session
                mock_db = MagicMock()
                mock_get_db.return_value.__next__.return_value = mock_db
                mock_db.query.return_value.filter.return_value.first.return_value = mock_settings
                
                # Mock Redis service
                mock_redis.publish = AsyncMock(return_value=1)
                
                # Create test event
                event = Event(
                    type=EventType.FASTBOT_CHAT_MESSAGE_SENT,
                    data={
                        "user_id": "test-user-123",
                        "organization_id": "test-org-456",
                        "conversation_id": "conv-123",
                        "ai_response": True
                    },
                    metadata=EventMetadata(
                        source_service="fastbot",
                        correlation_id="test-correlation-123"
                    )
                )
                
                # Handle the event
                await handle_chat_message(event)
                
                # Verify Redis publish was called
                mock_redis.publish.assert_called_once()
                
                self.log_test("Chat Message Handler", True, "Handler executed successfully")
                return True
                
        except Exception as e:
            self.log_test("Chat Message Handler", False, str(e))
            return False
    
    async def test_notification_publisher(self):
        """Test notification event publisher."""
        try:
            with patch('app.events.publishers.get_event_bus') as mock_get_bus:
                # Mock event bus
                mock_bus = MagicMock()
                mock_bus.publish = AsyncMock(return_value="event-123")
                mock_get_bus.return_value = mock_bus
                
                # Test publishing notification event
                event_id = await publish_notification_event(
                    notification_id="notif-123",
                    user_id="test-user-123",
                    organization_id="test-org-456",
                    title="Test Notification",
                    message="This is a test notification",
                    notification_type="test",
                    channel="in_app",
                    status="sent"
                )
                
                # Verify event was published
                assert event_id == "event-123"
                mock_bus.publish.assert_called_once()
                
                self.log_test("Notification Publisher", True, "Publisher executed successfully")
                return True
                
        except Exception as e:
            self.log_test("Notification Publisher", False, str(e))
            return False
    
    async def run_all_tests(self):
        """Run all component tests."""
        print("🚀 Starting Notification Component Tests")
        print("=" * 50)
        
        # Run synchronous tests
        self.test_notification_settings_model()
        self.test_event_model_creation()
        
        # Run asynchronous tests
        await self.test_user_registration_handler()
        await self.test_file_upload_handler()
        await self.test_chat_message_handler()
        await self.test_notification_publisher()
        
        # Print summary
        self.print_summary()
    
    def print_summary(self):
        """Print test summary."""
        print("\n" + "=" * 50)
        print("📊 Component Test Summary")
        print("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ Failed Tests:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test']}: {result['message']}")
        
        print("\n🎯 Notification Components Status:", "HEALTHY ✅" if failed_tests == 0 else "NEEDS ATTENTION ⚠️")


if __name__ == "__main__":
    tester = NotificationComponentTester()
    asyncio.run(tester.run_all_tests())
