#!/usr/bin/env python3
"""
Comprehensive test of notification endpoints - WebSocket and HTTP.
Tests actual functionality of receiving notifications.
"""

import asyncio
import requests
import json
import time
from datetime import datetime
from unittest.mock import patch, MagicMock, AsyncMock


class NotificationEndpointTester:
    """Test notification endpoints thoroughly."""
    
    def __init__(self):
        self.base_url = "http://localhost:8005"
        self.test_results = []
    
    def log_test(self, test_name: str, success: bool, message: str = ""):
        """Log test results."""
        status = "✅ PASS" if success else "❌ FAIL"
        timestamp = datetime.now().strftime("%H:%M:%S")
        result = f"[{timestamp}] {status} {test_name}"
        if message:
            result += f" - {message}"
        print(result)
        self.test_results.append({
            "test": test_name,
            "success": success,
            "message": message,
            "timestamp": timestamp
        })
    
    def test_service_health(self):
        """Test if the settings service is running."""
        try:
            response = requests.get(f"{self.base_url}/settings_status", timeout=5)
            if response.status_code == 200:
                self.log_test("Service Health", True, "Settings service is running")
                return True
            else:
                self.log_test("Service Health", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_test("Service Health", False, str(e))
            return False
    
    def check_notification_http_endpoints(self):
        """Check what notification-related HTTP endpoints exist."""
        try:
            # Get OpenAPI schema to see all endpoints
            response = requests.get(f"{self.base_url}/api/v1/settings/openapi.json", timeout=5)
            if response.status_code == 200:
                schema = response.json()
                paths = schema.get("paths", {})
                
                notification_endpoints = []
                for path, methods in paths.items():
                    if "notification" in path.lower():
                        for method in methods.keys():
                            notification_endpoints.append(f"{method.upper()} {path}")
                
                if notification_endpoints:
                    self.log_test("HTTP Notification Endpoints", True, f"Found: {', '.join(notification_endpoints)}")
                    return notification_endpoints
                else:
                    self.log_test("HTTP Notification Endpoints", False, "No notification endpoints found")
                    return []
            else:
                self.log_test("HTTP Notification Endpoints", False, f"Cannot get schema: HTTP {response.status_code}")
                return []
        except Exception as e:
            self.log_test("HTTP Notification Endpoints", False, str(e))
            return []
    
    def test_notification_retrieval_endpoints(self):
        """Test if there are endpoints to retrieve notifications."""
        endpoints_to_test = [
            "/api/v1/notification/list",
            "/api/v1/notification/history", 
            "/api/v1/notifications",
            "/api/v1/notification/get",
            "/api/v1/settings/notifications"
        ]
        
        found_endpoints = []
        
        for endpoint in endpoints_to_test:
            try:
                response = requests.get(f"{self.base_url}{endpoint}", timeout=3)
                # 401 means endpoint exists but requires auth, 404 means doesn't exist
                if response.status_code in [200, 401, 403]:
                    found_endpoints.append(f"{endpoint} (HTTP {response.status_code})")
                    self.log_test(f"Endpoint {endpoint}", True, f"Exists - HTTP {response.status_code}")
                elif response.status_code == 404:
                    self.log_test(f"Endpoint {endpoint}", False, "Not found")
                else:
                    self.log_test(f"Endpoint {endpoint}", False, f"HTTP {response.status_code}")
            except Exception as e:
                self.log_test(f"Endpoint {endpoint}", False, str(e))
        
        if found_endpoints:
            print(f"\n📋 Found notification endpoints: {', '.join(found_endpoints)}")
        else:
            print("\n❌ No notification retrieval endpoints found")
        
        return found_endpoints
    
    async def test_websocket_connection_detailed(self):
        """Test WebSocket connection in detail."""
        try:
            import websockets
            
            # Test different WebSocket URLs
            ws_urls = [
                f"ws://localhost:8005/api/v1/notification/ws/test-org",
                f"ws://localhost:8005/api/v1/notification/ws/test-org-456"
            ]
            
            for ws_url in ws_urls:
                try:
                    print(f"\n🔌 Testing WebSocket: {ws_url}")
                    
                    # Try to connect
                    async with websockets.connect(ws_url, timeout=3) as websocket:
                        self.log_test(f"WebSocket Connect {ws_url}", True, "Connection established")
                        
                        # Try to send a test message
                        await websocket.send(json.dumps({"test": "message"}))
                        
                        # Wait for response (with timeout)
                        try:
                            response = await asyncio.wait_for(websocket.recv(), timeout=2)
                            self.log_test(f"WebSocket Response {ws_url}", True, f"Received: {response}")
                        except asyncio.TimeoutError:
                            self.log_test(f"WebSocket Response {ws_url}", False, "No response received")
                        
                        return True
                        
                except websockets.exceptions.ConnectionClosedError as e:
                    self.log_test(f"WebSocket {ws_url}", False, f"Connection closed: {e}")
                except websockets.exceptions.InvalidStatusCode as e:
                    self.log_test(f"WebSocket {ws_url}", False, f"Invalid status: {e}")
                except Exception as e:
                    self.log_test(f"WebSocket {ws_url}", False, f"Error: {e}")
            
            return False
            
        except ImportError:
            self.log_test("WebSocket Test", False, "websockets library not available")
            return False
        except Exception as e:
            self.log_test("WebSocket Test", False, str(e))
            return False
    
    def test_notification_database_structure(self):
        """Check if we can infer notification storage from the codebase."""
        try:
            # Check if we can access the models to understand notification structure
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))
            
            from app.models.model import Notification
            
            # Get the table structure
            notification_columns = []
            for column in Notification.__table__.columns:
                notification_columns.append(f"{column.name}: {column.type}")
            
            self.log_test("Notification Model", True, f"Columns: {', '.join(notification_columns)}")
            
            # Check if there are any endpoints that might return notifications
            return True
            
        except Exception as e:
            self.log_test("Notification Model", False, str(e))
            return False
    
    async def test_notification_flow_simulation(self):
        """Simulate a notification flow to test the system."""
        try:
            # Import the notification system components
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))
            
            from app.events.models import Event, EventMetadata, EventType
            from app.routes.notification import send_notification
            from app.schemas.notification import NotificationRequest
            
            # Mock a user token
            mock_token = {
                "user_id": "test-user-123",
                "email": "<EMAIL>", 
                "organization_id": "test-org-456"
            }
            
            # Test the notification sending function
            with patch('app.routes.notification.get_current_user', return_value=mock_token), \
                 patch('app.routes.notification.redis_service') as mock_redis, \
                 patch('app.routes.notification.email_service') as mock_email, \
                 patch('app.routes.notification.push_service') as mock_push, \
                 patch('app.routes.notification.publish_notification_event') as mock_publish, \
                 patch('app.routes.notification.get_db') as mock_get_db:
                
                # Setup mocks
                mock_redis.publish = AsyncMock(return_value=1)
                mock_email.send_email.return_value = True
                mock_push.send_push.return_value = []
                mock_publish.return_value = "event-123"
                
                # Mock database
                mock_db = MagicMock()
                mock_get_db.return_value = mock_db
                mock_db.query.return_value.filter.return_value.first.return_value = None
                mock_db.add.return_value = None
                mock_db.commit.return_value = None
                mock_db.refresh.return_value = None
                
                # Create a mock notification object
                mock_notification = MagicMock()
                mock_notification.id = 123
                mock_db.add.return_value = mock_notification
                mock_db.refresh.return_value = mock_notification
                
                # Create notification request
                request = NotificationRequest(
                    organization_id="test-org-456",
                    message="Test notification message"
                )
                
                # Test the notification function
                from fastapi import BackgroundTasks
                background_tasks = BackgroundTasks()
                
                result = await send_notification(
                    request=request,
                    token=mock_token,
                    background_tasks=background_tasks,
                    db=mock_db
                )
                
                if result.get("status") == "success":
                    self.log_test("Notification Flow Simulation", True, "Notification sent successfully")
                    
                    # Check if Redis publish was called (for WebSocket delivery)
                    if mock_redis.publish.called:
                        self.log_test("WebSocket Delivery", True, "Redis publish called for real-time delivery")
                    else:
                        self.log_test("WebSocket Delivery", False, "Redis publish not called")
                    
                    return True
                else:
                    self.log_test("Notification Flow Simulation", False, f"Unexpected result: {result}")
                    return False
                    
        except Exception as e:
            self.log_test("Notification Flow Simulation", False, str(e))
            return False
    
    async def run_all_tests(self):
        """Run all notification endpoint tests."""
        print("🔍 TESTING NOTIFICATION ENDPOINTS IN DETAIL")
        print("=" * 60)
        
        # Basic health check
        if not self.test_service_health():
            print("❌ Service not running. Cannot proceed with tests.")
            return
        
        # Check what endpoints exist
        print("\n📋 CHECKING AVAILABLE ENDPOINTS")
        print("-" * 40)
        self.check_notification_http_endpoints()
        self.test_notification_retrieval_endpoints()
        
        # Test WebSocket functionality
        print("\n🔌 TESTING WEBSOCKET FUNCTIONALITY")
        print("-" * 40)
        await self.test_websocket_connection_detailed()
        
        # Check notification data structure
        print("\n🗄️ CHECKING NOTIFICATION DATA STRUCTURE")
        print("-" * 40)
        self.test_notification_database_structure()
        
        # Test notification flow
        print("\n🔄 TESTING NOTIFICATION FLOW")
        print("-" * 40)
        await self.test_notification_flow_simulation()
        
        # Print summary
        self.print_summary()
    
    def print_summary(self):
        """Print test summary."""
        print("\n" + "=" * 60)
        print("📊 NOTIFICATION ENDPOINT TEST SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ Failed Tests:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test']}: {result['message']}")
        
        print("\n🎯 CONCLUSIONS:")
        print("-" * 20)
        
        # Analyze results
        websocket_tests = [r for r in self.test_results if "websocket" in r["test"].lower()]
        http_tests = [r for r in self.test_results if "endpoint" in r["test"].lower()]
        
        websocket_working = any(r["success"] for r in websocket_tests)
        http_endpoints_exist = any(r["success"] for r in http_tests)
        
        if websocket_working:
            print("✅ WebSocket notifications: WORKING")
        else:
            print("❌ WebSocket notifications: NOT WORKING")
        
        if http_endpoints_exist:
            print("✅ HTTP notification endpoints: AVAILABLE")
        else:
            print("❌ HTTP notification endpoints: NOT FOUND")


if __name__ == "__main__":
    tester = NotificationEndpointTester()
    asyncio.run(tester.run_all_tests())
