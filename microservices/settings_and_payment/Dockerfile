# Use the official Python image from the Docker Hub
FROM python:3.12-alpine

# Set the working directory
WORKDIR /app

# Copy the requirements file into the container
COPY . /app

# Install the dependencies and curl
RUN pip install --no-cache-dir -r /app/requirements.txt

# create a directory for server logs
RUN mkdir -p /app/logs

EXPOSE 8005

# Command to run the application
CMD ["uvicorn", "--workers", "5", "--host", "0.0.0.0", "--port", "8005", "app.main:app"]
