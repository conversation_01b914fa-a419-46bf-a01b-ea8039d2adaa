#!/usr/bin/env python3
"""
Test script to verify the notification API endpoints are working.
This tests the notification API with the running settings service.
"""

import requests
import json
from datetime import datetime


class NotificationAPITester:
    """Test the notification API endpoints."""
    
    def __init__(self):
        self.base_url = "http://localhost:8005"
        self.test_results = []
    
    def log_test(self, test_name: str, success: bool, message: str = ""):
        """Log test results."""
        status = "✅ PASS" if success else "❌ FAIL"
        timestamp = datetime.now().strftime("%H:%M:%S")
        result = f"[{timestamp}] {status} {test_name}"
        if message:
            result += f" - {message}"
        print(result)
        self.test_results.append({
            "test": test_name,
            "success": success,
            "message": message,
            "timestamp": timestamp
        })
    
    def test_service_health(self):
        """Test if the settings service is running."""
        try:
            response = requests.get(f"{self.base_url}/settings_status", timeout=5)
            if response.status_code == 200:
                self.log_test("Service Health Check", True, "Settings service is running")
                return True
            else:
                self.log_test("Service Health Check", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_test("Service Health Check", False, str(e))
            return False
    
    def test_notification_endpoint_without_auth(self):
        """Test notification endpoint without authentication (should fail)."""
        try:
            notification_data = {
                "organization_id": "test-org-456",
                "message": "Test notification without auth"
            }
            
            response = requests.post(
                f"{self.base_url}/api/v1/notification/notify",
                json=notification_data
            )
            
            if response.status_code == 401:
                self.log_test("Notification Endpoint Security", True, "Properly requires authentication")
                return True
            else:
                self.log_test("Notification Endpoint Security", False, f"Expected 401, got {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("Notification Endpoint Security", False, str(e))
            return False
    
    def test_api_documentation(self):
        """Test if API documentation is accessible."""
        try:
            response = requests.get(f"{self.base_url}/api/v1/settings/docs", timeout=5)
            if response.status_code == 200:
                self.log_test("API Documentation", True, "Documentation is accessible")
                return True
            else:
                self.log_test("API Documentation", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_test("API Documentation", False, str(e))
            return False
    
    def test_openapi_schema(self):
        """Test if OpenAPI schema is accessible."""
        try:
            response = requests.get(f"{self.base_url}/api/v1/settings/openapi.json", timeout=5)
            if response.status_code == 200:
                schema = response.json()
                # Check if notification endpoints are documented
                paths = schema.get("paths", {})
                notification_paths = [path for path in paths.keys() if "notification" in path]
                
                if notification_paths:
                    self.log_test("OpenAPI Schema", True, f"Found {len(notification_paths)} notification endpoints")
                    return True
                else:
                    self.log_test("OpenAPI Schema", False, "No notification endpoints found in schema")
                    return False
            else:
                self.log_test("OpenAPI Schema", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_test("OpenAPI Schema", False, str(e))
            return False
    
    def test_websocket_endpoint_availability(self):
        """Test if WebSocket endpoint is available (connection test only)."""
        try:
            import websockets
            import asyncio
            
            async def test_ws_connection():
                try:
                    # Try to connect to WebSocket endpoint
                    ws_url = f"ws://localhost:8005/api/v1/notification/ws/test-org"
                    async with websockets.connect(ws_url, timeout=2) as websocket:
                        # If we get here, the endpoint exists but will likely close due to auth
                        return True
                except websockets.exceptions.ConnectionClosedError:
                    # Expected - endpoint exists but closes due to authentication
                    return True
                except Exception:
                    return False
            
            # Run the async test
            result = asyncio.run(test_ws_connection())
            
            if result:
                self.log_test("WebSocket Endpoint", True, "WebSocket endpoint is available")
                return True
            else:
                self.log_test("WebSocket Endpoint", False, "WebSocket endpoint not accessible")
                return False
                
        except ImportError:
            self.log_test("WebSocket Endpoint", False, "websockets library not available")
            return False
        except Exception as e:
            self.log_test("WebSocket Endpoint", False, str(e))
            return False
    
    def test_redis_connection(self):
        """Test Redis connection through the service."""
        try:
            response = requests.get(f"{self.base_url}/redis-test", timeout=5)
            if response.status_code == 200:
                result = response.json()
                if "test_key" in result and result["test_key"] == "test_value":
                    self.log_test("Redis Connection", True, "Redis is connected and working")
                    return True
                else:
                    self.log_test("Redis Connection", False, "Redis test failed")
                    return False
            else:
                self.log_test("Redis Connection", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_test("Redis Connection", False, str(e))
            return False
    
    def test_notification_settings_endpoints(self):
        """Test notification settings endpoints (without auth)."""
        try:
            # Test GET notification settings (should require auth)
            response = requests.get(f"{self.base_url}/api/v1/settings/notification-settings")
            
            if response.status_code == 401:
                self.log_test("Notification Settings Security", True, "Settings endpoints require authentication")
                return True
            else:
                self.log_test("Notification Settings Security", False, f"Expected 401, got {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("Notification Settings Security", False, str(e))
            return False
    
    def run_all_tests(self):
        """Run all API tests."""
        print("🚀 Starting Notification API Tests")
        print("=" * 50)
        
        # Check if service is running first
        if not self.test_service_health():
            print("❌ Service is not running. Cannot proceed with API tests.")
            return
        
        # Run API tests
        self.test_notification_endpoint_without_auth()
        self.test_api_documentation()
        self.test_openapi_schema()
        self.test_websocket_endpoint_availability()
        self.test_redis_connection()
        self.test_notification_settings_endpoints()
        
        # Print summary
        self.print_summary()
    
    def print_summary(self):
        """Print test summary."""
        print("\n" + "=" * 50)
        print("📊 API Test Summary")
        print("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ Failed Tests:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test']}: {result['message']}")
        
        print("\n🎯 Notification API Status:", "HEALTHY ✅" if failed_tests == 0 else "NEEDS ATTENTION ⚠️")
        
        # Provide recommendations
        print("\n💡 Recommendations:")
        if passed_tests > 0:
            print("✅ Notification system components are working correctly")
            print("✅ API endpoints are properly secured with authentication")
            print("✅ Service is running and accessible")
        
        if failed_tests > 0:
            print("⚠️  Some tests failed - check the details above")
        
        print("\n📝 Next Steps:")
        print("1. To test with authentication, create a valid user and get an auth token")
        print("2. Test notification delivery by sending authenticated requests")
        print("3. Test WebSocket connections with proper authentication")
        print("4. Verify cross-service event handling when other services are running")


if __name__ == "__main__":
    tester = NotificationAPITester()
    tester.run_all_tests()
