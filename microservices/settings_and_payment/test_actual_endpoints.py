#!/usr/bin/env python3
"""
Test the actual notification endpoints that exist.
"""

import requests
import asyncio
import json
from datetime import datetime


class ActualEndpointTester:
    """Test the actual notification endpoints."""
    
    def __init__(self):
        self.base_url = "http://localhost:8005"
        self.test_results = []
    
    def log_test(self, test_name: str, success: bool, message: str = ""):
        """Log test results."""
        status = "✅ PASS" if success else "❌ FAIL"
        timestamp = datetime.now().strftime("%H:%M:%S")
        result = f"[{timestamp}] {status} {test_name}"
        if message:
            result += f" - {message}"
        print(result)
        self.test_results.append({
            "test": test_name,
            "success": success,
            "message": message,
            "timestamp": timestamp
        })
    
    def test_notification_endpoints_without_auth(self):
        """Test notification endpoints without authentication."""
        endpoints = [
            ("GET", "/api/v1/notification/notifications?organization_id=test-org"),
            ("GET", "/api/v1/notification/notifications/unread-count?organization_id=test-org"),
            ("GET", "/api/v1/notification/device-tokens?organization_id=test-org"),
            ("PUT", "/api/v1/notification/notifications/test-id/read"),
            ("POST", "/api/v1/notification/save-device-token"),
            ("DELETE", "/api/v1/notification/remove-device-token?device_token=test")
        ]
        
        print("🔍 Testing notification endpoints (expecting 401 - auth required):")
        print("-" * 60)
        
        for method, endpoint in endpoints:
            try:
                if method == "GET":
                    response = requests.get(f"{self.base_url}{endpoint}", timeout=5)
                elif method == "POST":
                    response = requests.post(f"{self.base_url}{endpoint}", 
                                           json={"device_token": "test", "organization_id": "test"}, 
                                           timeout=5)
                elif method == "PUT":
                    response = requests.put(f"{self.base_url}{endpoint}", timeout=5)
                elif method == "DELETE":
                    response = requests.delete(f"{self.base_url}{endpoint}", timeout=5)
                
                if response.status_code == 401:
                    self.log_test(f"{method} {endpoint}", True, "Requires authentication (expected)")
                elif response.status_code == 422:
                    self.log_test(f"{method} {endpoint}", True, "Validation error (endpoint exists)")
                elif response.status_code == 404:
                    self.log_test(f"{method} {endpoint}", False, "Endpoint not found")
                else:
                    self.log_test(f"{method} {endpoint}", True, f"HTTP {response.status_code} (endpoint exists)")
                    
            except Exception as e:
                self.log_test(f"{method} {endpoint}", False, str(e))
    
    def test_websocket_with_different_approaches(self):
        """Test WebSocket with different approaches."""
        print("\n🔌 Testing WebSocket connections:")
        print("-" * 40)
        
        try:
            import websockets
            
            async def test_websocket_approaches():
                # Test different WebSocket connection approaches
                approaches = [
                    {
                        "url": "ws://localhost:8005/api/v1/notification/ws/test-org",
                        "headers": {},
                        "description": "Basic connection"
                    },
                    {
                        "url": "ws://localhost:8005/api/v1/notification/ws/test-org",
                        "headers": {"Authorization": "Bearer fake-token"},
                        "description": "With fake auth token"
                    },
                    {
                        "url": "ws://localhost:8005/api/v1/notification/ws/test-org",
                        "headers": {"Origin": "http://localhost:3000"},
                        "description": "With Origin header"
                    }
                ]
                
                for approach in approaches:
                    try:
                        print(f"  Testing: {approach['description']}")
                        async with websockets.connect(
                            approach["url"], 
                            extra_headers=approach["headers"],
                            timeout=3
                        ) as websocket:
                            self.log_test(f"WebSocket {approach['description']}", True, "Connected successfully")
                            
                            # Try to receive a message
                            try:
                                message = await asyncio.wait_for(websocket.recv(), timeout=1)
                                self.log_test(f"WebSocket Message {approach['description']}", True, f"Received: {message}")
                            except asyncio.TimeoutError:
                                self.log_test(f"WebSocket Message {approach['description']}", True, "No immediate message (normal)")
                            
                            return True
                            
                    except websockets.exceptions.ConnectionClosedError as e:
                        self.log_test(f"WebSocket {approach['description']}", False, f"Connection closed: {e.code} {e.reason}")
                    except websockets.exceptions.InvalidStatusCode as e:
                        self.log_test(f"WebSocket {approach['description']}", False, f"HTTP {e.status_code}")
                    except Exception as e:
                        self.log_test(f"WebSocket {approach['description']}", False, str(e))
                
                return False
            
            return asyncio.run(test_websocket_approaches())
            
        except ImportError:
            self.log_test("WebSocket Test", False, "websockets library not available")
            return False
    
    def check_websocket_endpoint_in_code(self):
        """Check the WebSocket endpoint implementation."""
        try:
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))
            
            # Check if the WebSocket endpoint function exists
            from app.routes.notification import websocket_endpoint
            
            # Check the function signature
            import inspect
            sig = inspect.signature(websocket_endpoint)
            params = list(sig.parameters.keys())
            
            self.log_test("WebSocket Endpoint Code", True, f"Function exists with params: {params}")
            
            # The issue might be in the authentication dependency
            if 'token' in params:
                self.log_test("WebSocket Auth Dependency", True, "Uses authentication dependency")
            else:
                self.log_test("WebSocket Auth Dependency", False, "No authentication dependency found")
            
            return True
            
        except Exception as e:
            self.log_test("WebSocket Endpoint Code", False, str(e))
            return False
    
    def test_redis_for_websocket(self):
        """Test if Redis is working for WebSocket functionality."""
        try:
            response = requests.get(f"{self.base_url}/redis-test", timeout=5)
            if response.status_code == 200:
                result = response.json()
                if "test_key" in result:
                    self.log_test("Redis for WebSocket", True, "Redis is working")
                    return True
            
            self.log_test("Redis for WebSocket", False, "Redis test failed")
            return False
            
        except Exception as e:
            self.log_test("Redis for WebSocket", False, str(e))
            return False
    
    def create_test_notification_and_check(self):
        """Try to create a test notification and see if we can retrieve it."""
        print("\n📝 Testing notification creation and retrieval:")
        print("-" * 50)
        
        # This would require authentication, so we'll just test the structure
        try:
            # Test the notification creation endpoint structure
            test_data = {
                "organization_id": "test-org",
                "message": "Test notification"
            }
            
            response = requests.post(
                f"{self.base_url}/api/v1/notification/notify",
                json=test_data,
                timeout=5
            )
            
            if response.status_code == 401:
                self.log_test("Notification Creation Endpoint", True, "Exists and requires auth")
            else:
                self.log_test("Notification Creation Endpoint", False, f"Unexpected response: {response.status_code}")
            
            # Test notification retrieval endpoint
            response = requests.get(
                f"{self.base_url}/api/v1/notification/notifications?organization_id=test-org",
                timeout=5
            )
            
            if response.status_code == 401:
                self.log_test("Notification Retrieval Endpoint", True, "Exists and requires auth")
                return True
            else:
                self.log_test("Notification Retrieval Endpoint", False, f"Unexpected response: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("Notification Endpoints Test", False, str(e))
            return False
    
    def run_all_tests(self):
        """Run all tests."""
        print("🔍 TESTING ACTUAL NOTIFICATION ENDPOINTS")
        print("=" * 60)
        
        # Test service health
        try:
            response = requests.get(f"{self.base_url}/settings_status", timeout=5)
            if response.status_code == 200:
                self.log_test("Service Health", True, "Service is running")
            else:
                self.log_test("Service Health", False, "Service not responding")
                return
        except Exception as e:
            self.log_test("Service Health", False, str(e))
            return
        
        # Test HTTP endpoints
        self.test_notification_endpoints_without_auth()
        
        # Test notification flow
        self.create_test_notification_and_check()
        
        # Test WebSocket
        self.test_websocket_with_different_approaches()
        
        # Check WebSocket implementation
        self.check_websocket_endpoint_in_code()
        
        # Test Redis
        self.test_redis_for_websocket()
        
        # Print summary
        self.print_summary()
    
    def print_summary(self):
        """Print test summary."""
        print("\n" + "=" * 60)
        print("📊 ACTUAL ENDPOINT TEST SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        print("\n🎯 KEY FINDINGS:")
        print("-" * 20)
        
        # Analyze HTTP endpoints
        http_tests = [r for r in self.test_results if "api/v1/notification" in r["test"]]
        http_working = sum(1 for r in http_tests if r["success"])
        
        if http_working > 0:
            print("✅ HTTP notification endpoints: EXIST and require authentication")
        else:
            print("❌ HTTP notification endpoints: NOT WORKING")
        
        # Analyze WebSocket
        ws_tests = [r for r in self.test_results if "websocket" in r["test"].lower()]
        ws_working = any(r["success"] for r in ws_tests)
        
        if ws_working:
            print("✅ WebSocket endpoint: WORKING")
        else:
            print("❌ WebSocket endpoint: HAS ISSUES (likely authentication)")
        
        # Redis status
        redis_tests = [r for r in self.test_results if "redis" in r["test"].lower()]
        redis_working = any(r["success"] for r in redis_tests)
        
        if redis_working:
            print("✅ Redis backend: WORKING")
        else:
            print("❌ Redis backend: ISSUES")
        
        print("\n📋 FRONTEND INTEGRATION RECOMMENDATIONS:")
        print("-" * 45)
        print("1. ✅ Use HTTP endpoints for notification history:")
        print("   GET /api/v1/notification/notifications?organization_id={org}")
        print("   GET /api/v1/notification/notifications/unread-count?organization_id={org}")
        print("   PUT /api/v1/notification/notifications/{id}/read")
        
        print("\n2. ⚠️  WebSocket for real-time (needs proper auth):")
        print("   WS /api/v1/notification/ws/{organization_id}")
        print("   - Requires proper JWT token in connection")
        
        print("\n3. ✅ Device token management:")
        print("   POST /api/v1/notification/save-device-token")
        print("   GET /api/v1/notification/device-tokens")
        print("   DELETE /api/v1/notification/remove-device-token")


if __name__ == "__main__":
    tester = ActualEndpointTester()
    tester.run_all_tests()
