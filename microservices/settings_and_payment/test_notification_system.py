#!/usr/bin/env python3
"""
Test script to verify the notification system is working properly.
This script tests the notification system with live services.
"""

import asyncio
import json
import requests
import time
import websockets
from datetime import datetime
from typing import Dict, Any


class NotificationSystemTester:
    """Test the notification system with live services."""
    
    def __init__(self):
        self.base_url = "http://localhost:8005"  # Settings and Payment service
        self.auth_url = "http://localhost:7777"   # Auth service
        self.fastbot_url = "http://localhost:8002"  # FastBot service
        self.social_url = "http://localhost:8003"   # Social service
        
        self.test_user = {
            "email": "<EMAIL>",
            "password": "testpassword123",
            "organization_id": "test-org-456"
        }
        self.auth_token = None
        self.test_results = []
    
    def log_test(self, test_name: str, success: bool, message: str = ""):
        """Log test results."""
        status = "✅ PASS" if success else "❌ FAIL"
        timestamp = datetime.now().strftime("%H:%M:%S")
        result = f"[{timestamp}] {status} {test_name}"
        if message:
            result += f" - {message}"
        print(result)
        self.test_results.append({
            "test": test_name,
            "success": success,
            "message": message,
            "timestamp": timestamp
        })
    
    def check_service_health(self, service_name: str, url: str) -> bool:
        """Check if a service is running."""
        try:
            response = requests.get(f"{url}/settings_status" if "8005" in url else f"{url}/health", timeout=5)
            return response.status_code == 200
        except Exception as e:
            self.log_test(f"{service_name} Health Check", False, str(e))
            return False
    
    def authenticate_user(self) -> bool:
        """Authenticate test user and get token."""
        try:
            # Try to login first
            login_data = {
                "email": self.test_user["email"],
                "password": self.test_user["password"]
            }
            
            response = requests.post(f"{self.auth_url}/api/v1/auth/login", json=login_data)
            
            if response.status_code == 200:
                self.auth_token = response.json().get("access_token")
                self.log_test("User Authentication", True, "Login successful")
                return True
            else:
                self.log_test("User Authentication", False, f"Login failed: {response.text}")
                return False
                
        except Exception as e:
            self.log_test("User Authentication", False, str(e))
            return False
    
    def test_notification_api(self) -> bool:
        """Test the notification API endpoint."""
        try:
            headers = {"Authorization": f"Bearer {self.auth_token}"}
            notification_data = {
                "organization_id": self.test_user["organization_id"],
                "message": "Test notification from automated test"
            }
            
            response = requests.post(
                f"{self.base_url}/api/v1/notification/notify",
                json=notification_data,
                headers=headers
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("status") == "success":
                    self.log_test("Notification API", True, "Notification sent successfully")
                    return True
                else:
                    self.log_test("Notification API", False, f"Unexpected response: {result}")
                    return False
            else:
                self.log_test("Notification API", False, f"HTTP {response.status_code}: {response.text}")
                return False
                
        except Exception as e:
            self.log_test("Notification API", False, str(e))
            return False
    
    def test_notification_settings(self) -> bool:
        """Test notification settings management."""
        try:
            headers = {"Authorization": f"Bearer {self.auth_token}"}
            
            # Get current settings
            response = requests.get(
                f"{self.base_url}/api/v1/settings/notification-settings",
                headers=headers
            )
            
            if response.status_code == 200:
                settings = response.json()
                self.log_test("Get Notification Settings", True, f"Retrieved settings: {len(settings)} items")
                
                # Test updating settings
                update_data = {
                    "in_app_notifications": True,
                    "email_notifications": True,
                    "push_notifications": False,
                    "notification_frequency": "Real-time"
                }
                
                update_response = requests.put(
                    f"{self.base_url}/api/v1/settings/notification-settings",
                    json=update_data,
                    headers=headers
                )
                
                if update_response.status_code == 200:
                    self.log_test("Update Notification Settings", True, "Settings updated successfully")
                    return True
                else:
                    self.log_test("Update Notification Settings", False, f"Update failed: {update_response.text}")
                    return False
            else:
                self.log_test("Get Notification Settings", False, f"HTTP {response.status_code}: {response.text}")
                return False
                
        except Exception as e:
            self.log_test("Notification Settings", False, str(e))
            return False
    
    async def test_websocket_notifications(self) -> bool:
        """Test WebSocket notification delivery."""
        try:
            ws_url = f"ws://localhost:8005/api/v1/notification/ws/{self.test_user['organization_id']}"
            headers = {"Authorization": f"Bearer {self.auth_token}"}
            
            # Note: This is a simplified test. In practice, WebSocket authentication 
            # might need to be handled differently
            async with websockets.connect(ws_url, extra_headers=headers) as websocket:
                self.log_test("WebSocket Connection", True, "Connected successfully")
                
                # Send a test notification via API to trigger WebSocket message
                await asyncio.sleep(1)  # Give connection time to establish
                
                # In a real test, we would trigger a notification and listen for it
                # For now, we just verify the connection works
                return True
                
        except Exception as e:
            self.log_test("WebSocket Notifications", False, str(e))
            return False
    
    def test_cross_service_events(self) -> bool:
        """Test that events from other services trigger notifications."""
        try:
            headers = {"Authorization": f"Bearer {self.auth_token}"}
            
            # Test file upload event (if FastBot is available)
            if self.check_service_health("FastBot", self.fastbot_url):
                # This would typically involve uploading a file to FastBot
                # and checking if a notification is generated
                self.log_test("Cross-Service Events (FastBot)", True, "FastBot service is available")
            else:
                self.log_test("Cross-Service Events (FastBot)", False, "FastBot service not available")
            
            # Test social media event (if Social service is available)
            if self.check_service_health("Social", self.social_url):
                self.log_test("Cross-Service Events (Social)", True, "Social service is available")
            else:
                self.log_test("Cross-Service Events (Social)", False, "Social service not available")
            
            return True
            
        except Exception as e:
            self.log_test("Cross-Service Events", False, str(e))
            return False
    
    def run_all_tests(self):
        """Run all notification system tests."""
        print("🚀 Starting Notification System Tests")
        print("=" * 50)
        
        # Check service health
        services = [
            ("Auth Service", self.auth_url),
            ("Settings & Payment Service", self.base_url),
            ("FastBot Service", self.fastbot_url),
            ("Social Service", self.social_url)
        ]
        
        for service_name, url in services:
            is_healthy = self.check_service_health(service_name, url)
            self.log_test(f"{service_name} Health", is_healthy)
        
        # Authenticate user
        if not self.authenticate_user():
            print("❌ Cannot proceed without authentication")
            return
        
        # Run notification tests
        self.test_notification_api()
        self.test_notification_settings()
        
        # Run async tests
        try:
            asyncio.run(self.test_websocket_notifications())
        except Exception as e:
            self.log_test("WebSocket Test", False, str(e))
        
        self.test_cross_service_events()
        
        # Print summary
        self.print_summary()
    
    def print_summary(self):
        """Print test summary."""
        print("\n" + "=" * 50)
        print("📊 Test Summary")
        print("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ Failed Tests:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test']}: {result['message']}")
        
        print("\n🎯 Notification System Status:", "HEALTHY ✅" if failed_tests == 0 else "NEEDS ATTENTION ⚠️")


if __name__ == "__main__":
    tester = NotificationSystemTester()
    tester.run_all_tests()
