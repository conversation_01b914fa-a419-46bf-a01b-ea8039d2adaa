# Socials Service

The Socials Service handles social media integration, content scheduling, and metrics tracking in the EllumAI backend system.

## Features

- Social media platform integration
- Content scheduling and automated posting
- Social media metrics tracking and analytics
- Scheduled tasks using APScheduler
- Redis integration for caching

## API Endpoints

The service exposes the following endpoints:

- `/api/v1/socials/platforms` - Manage social media platform connections
- `/api/v1/socials/content` - Create and schedule social media content
- `/api/v1/socials/metrics` - Retrieve social media metrics and analytics
- `/socials_status` - Check if the service is running
- `/redis-test` - Test Redis connectivity

Full API documentation is available at `/api/v1/socials/docs` when the service is running.

## Environment Variables

Create a `.env` file in the service directory with the following variables:

```
DATABASE_URL=postgresql://username:password@host:port/database
SECRET_KEY=your_secret_key
REDIS_HOST=redis
REDIS_PORT=6379
SCHEDULE_PERIOD=60
TWITTER_API_KEY=your_twitter_api_key
TWITTER_API_SECRET=your_twitter_api_secret
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret
LINKEDIN_CLIENT_ID=your_linkedin_client_id
LINKEDIN_CLIENT_SECRET=your_linkedin_client_secret
```

## Running the Service

### Without Docker

1. Navigate to the socials service directory:
   ```bash
   cd microservices/socials_service
   ```

2. Create a virtual environment:
   ```bash
   python -m venv venv
   ```

3. Activate the virtual environment:
   - On Windows: `venv\Scripts\activate`
   - On macOS/Linux: `source venv/bin/activate`

4. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

5. Initialize and run migrations:
   ```bash
   alembic revision --autogenerate -m "initial migrations"
   alembic upgrade head
   ```

6. Start the service:
   ```bash
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8006
   ```

### With Docker

1. Build and run the Docker container:
   ```bash
   docker build -t socials_service .
   docker run -p 8006:8006 socials_service
   ```

### Using Docker Compose

From the root of the project:
```bash
docker-compose up socials_service
```
