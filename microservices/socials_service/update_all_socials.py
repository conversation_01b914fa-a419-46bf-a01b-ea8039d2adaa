#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to immediately update all social media metrics for testing purposes.
This script will:
1. Update all Facebook metrics
2. Update all Twitter metrics
3. Clear and update Redis cache
4. Print status information

Usage:
    python update_all_socials.py [--organisation_id ORGANISATION_ID]

If organisation_id is provided, only updates metrics for that organisation.
Otherwise, updates metrics for all organisations.
"""

import asyncio
import argparse
import sys
import logging
from datetime import datetime
import json

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger("socials_updater")

# Add the parent directory to the path so we can import the app modules
sys.path.append(".")

# Import necessary modules
try:
    from app.database.session import SessionLocal
    from app.models.model import SocialMediaAccount, FacebookPageMetrics, TwitterMetrics, InstagramAccountMetrics
    from app.utils.redis_cache import redis_client
    from app.tasks.facebook_metrics import fetch_and_store_facebook_metrics_for_account
    from app.tasks.twitter_metrics import fetch_and_store_twitter_metrics_for_account
    from sqlalchemy.future import select
except ImportError as e:
    logger.error(f"Failed to import required modules: {e}")
    logger.error("Make sure you're running this script from the socials_service directory")
    sys.exit(1)

async def clear_cache_for_account(account):
    """Clear all Redis cache for a specific account"""
    platform = account.platform
    organisation_id = account.organisation_id

    # Get all keys matching the pattern for this platform and organisation
    pattern = f"{platform}_*_{organisation_id}*"
    all_keys = redis_client.keys(pattern)

    if all_keys:
        # Delete all matching keys
        redis_client.delete(*all_keys)
        logger.info(f"Cleared {len(all_keys)} cache keys for {platform} account of organisation {organisation_id}")
    else:
        logger.info(f"No cache keys found for {platform} account of organisation {organisation_id}")

    # Also clear any platform-specific keys that might not follow the standard pattern
    if platform == "facebook":
        fb_patterns = [
            f"fb_*_{organisation_id}*",
            f"facebook_*_{organisation_id}*"
        ]
        for pattern in fb_patterns:
            keys = redis_client.keys(pattern)
            if keys:
                redis_client.delete(*keys)
                logger.info(f"Cleared {len(keys)} additional Facebook cache keys")

    elif platform == "twitter":
        twitter_patterns = [
            f"x_*_{organisation_id}*",
            f"twitter_*_{organisation_id}*"
        ]
        for pattern in twitter_patterns:
            keys = redis_client.keys(pattern)
            if keys:
                redis_client.delete(*keys)
                logger.info(f"Cleared {len(keys)} additional Twitter cache keys")

    elif platform == "instagram":
        ig_patterns = [
            f"ig_*_{organisation_id}*",
            f"instagram_*_{organisation_id}*"
        ]
        for pattern in ig_patterns:
            keys = redis_client.keys(pattern)
            if keys:
                redis_client.delete(*keys)
                logger.info(f"Cleared {len(keys)} additional Instagram cache keys")

async def update_account_metrics(account):
    """Update metrics for a specific account by fetching real data from APIs"""
    try:
        platform = account.platform
        account_id = account.id
        organisation_id = account.organisation_id

        logger.info(f"Updating metrics for {platform} account {account_id} (Organisation: {organisation_id})")

        # Clear cache first to ensure fresh data
        await clear_cache_for_account(account)

        # Update metrics based on platform
        if platform == "facebook":
            logger.info(f"Fetching real data from Facebook API for account {account_id}")
            await fetch_and_store_facebook_metrics_for_account(account_id)

            # Verify update by checking the database
            db = SessionLocal()
            try:
                # Get the latest metrics from the database
                result = await db.execute(
                    select(FacebookPageMetrics)
                    .filter(FacebookPageMetrics.organisation_id == organisation_id)
                    .order_by(FacebookPageMetrics.collected_at.desc())
                    .limit(1)
                )
                metrics = result.scalars().first()

                if metrics:
                    logger.info(f"Facebook metrics updated successfully. Latest data from: {metrics.collected_at}")
                    # Access the actual values, not the column objects
                    followers = getattr(metrics, 'total_followers', 0)
                    impressions = getattr(metrics, 'total_impressions', 0)
                    engagements = getattr(metrics, 'total_engagements', 0)
                    engagement_rate = getattr(metrics, 'engagement_rate', 0)
                    logger.info(f"Total Followers: {followers}, Total Impressions: {impressions}, Total Engagements: {engagements}, Engagement Rate: {engagement_rate}%")

                    # Get the actual values directly from the model
                    try:
                        # Access the attributes directly
                        followers = metrics.total_followers
                        impressions = metrics.total_impressions
                        reach = metrics.total_reach
                        engagements = metrics.total_engagements
                        engagement_rate = metrics.engagement_rate

                        logger.info(f"Extracted values - Followers: {followers}, Impressions: {impressions}, Reach: {reach}, Engagements: {engagements}, Rate: {engagement_rate}%")
                    except Exception as e:
                        logger.error(f"Error extracting values from metrics: {str(e)}")

                    # Force update the cache with the latest database values - using the new field names
                    cache_key = f"fb_overview_{organisation_id}"
                    cache_data = {
                        "total_followers": followers,
                        "total_impressions": impressions,
                        "total_reach": reach,
                        "total_engagements": engagements,
                        "engagement_rate": engagement_rate,
                        "updated_at": datetime.now().isoformat()
                    }
                    # Use a 5-minute cache expiry for API endpoints
                    redis_client.setex(cache_key, 300, json.dumps(cache_data))
                    logger.info(f"Manually updated Redis cache for Facebook overview")
                else:
                    logger.warning(f"No Facebook metrics found after update for organisation {organisation_id}")
            finally:
                await db.close()

        elif platform == "twitter":
            logger.info(f"Fetching real data from Twitter API for account {account_id}")
            await fetch_and_store_twitter_metrics_for_account(account_id)

            # Verify update by checking the database
            db = SessionLocal()
            try:
                # Get the latest metrics from the database
                result = await db.execute(
                    select(TwitterMetrics)
                    .filter(TwitterMetrics.organisation_id == organisation_id)
                    .order_by(TwitterMetrics.collected_at.desc())
                    .limit(1)
                )
                metrics = result.scalars().first()

                if metrics:
                    logger.info(f"Twitter metrics updated successfully. Latest data from: {metrics.collected_at}")
                    logger.info(f"Followers: {metrics.followers_count}, Impressions: {metrics.impressions}, Engagements: {metrics.engagements}")

                    # Force update the cache with the latest database values
                    cache_key = f"x_overview_{organisation_id}"
                    cache_data = {
                        "followers": metrics.followers_count,
                        "impressions": metrics.impressions,
                        "reach": metrics.reach,
                        "engagements": metrics.engagements,
                        "updated_at": datetime.now().isoformat()
                    }
                    # Use a 5-minute cache expiry for API endpoints
                    redis_client.setex(cache_key, 300, json.dumps(cache_data))
                    logger.info(f"Manually updated Redis cache for Twitter overview")
                else:
                    logger.warning(f"No Twitter metrics found after update for organisation {organisation_id}")
            finally:
                await db.close()

        elif platform == "instagram":
            logger.info(f"Fetching real data from Instagram API for account {account_id}")
            # Import here to avoid circular imports
            from app.tasks.instagram_metrics import fetch_and_store_instagram_metrics_for_account
            await fetch_and_store_instagram_metrics_for_account(account_id)

            # Verify update by checking the database
            db = SessionLocal()
            try:
                # Get the latest metrics from the database
                result = await db.execute(
                    select(InstagramAccountMetrics)
                    .filter(InstagramAccountMetrics.organisation_id == organisation_id)
                    .order_by(InstagramAccountMetrics.collected_at.desc())
                    .limit(1)
                )
                metrics = result.scalars().first()

                if metrics:
                    logger.info(f"Instagram metrics updated successfully. Latest data from: {metrics.collected_at}")
                    logger.info(f"Followers: {metrics.followers_count}, Following: {metrics.follows_count}, Media: {metrics.media_count}")

                    # Force update the cache with the latest database values
                    cache_key = f"ig_overview_{organisation_id}"
                    cache_data = {
                        "followers_count": metrics.followers_count,
                        "follows_count": metrics.follows_count,
                        "media_count": metrics.media_count,
                        "updated_at": datetime.now().isoformat()
                    }
                    # Use a 5-minute cache expiry for API endpoints
                    redis_client.setex(cache_key, 300, json.dumps(cache_data))
                    logger.info(f"Manually updated Redis cache for Instagram overview")
                else:
                    logger.warning(f"No Instagram metrics found after update for organisation {organisation_id}")
            finally:
                await db.close()
        else:
            logger.info(f"Platform {platform} not supported for updates")

        # Verify cache update for all platforms
        overview_key = f"{platform}_overview_{organisation_id}"
        cache_data = redis_client.get(overview_key)

        if cache_data:
            cache_json = json.loads(cache_data)
            logger.info(f"Cache verified and updated successfully. Updated at: {cache_json.get('updated_at')}")
        else:
            logger.warning(f"Cache not updated for {platform} account of organisation {organisation_id}")

        return True
    except Exception as e:
        logger.error(f"Error updating metrics for account {account.id}: {str(e)}")
        return False

async def update_all_accounts(organisation_id=None):
    """Update metrics for all accounts or a specific organisation"""
    try:
        logger.info(f"Starting metrics update at {datetime.now().isoformat()}")

        # Create a new database session
        db = SessionLocal()

        try:
            # Get all active social media accounts
            query = select(SocialMediaAccount).filter(SocialMediaAccount.login_status == True)

            if organisation_id:
                query = query.filter(SocialMediaAccount.organisation_id == organisation_id)

            result = await db.execute(query)
            accounts = result.scalars().all()

            if not accounts:
                logger.warning(f"No active social media accounts found{' for organisation ' + organisation_id if organisation_id else ''}")
                return

            logger.info(f"Found {len(accounts)} active social media accounts to update")

            # Update each account
            success_count = 0
            for account in accounts:
                success = await update_account_metrics(account)
                if success:
                    success_count += 1

            logger.info(f"Completed metrics update. Successfully updated {success_count}/{len(accounts)} accounts")
        finally:
            await db.close()
    except Exception as e:
        logger.error(f"Error in update_all_accounts: {str(e)}")

async def main():
    """Main function to parse arguments and run the update"""
    parser = argparse.ArgumentParser(description="Update social media metrics for testing")
    parser.add_argument("--organisation_id", help="Organisation ID to update (updates all if not specified)")
    args = parser.parse_args()

    # Check if Redis is available
    if not redis_client.ping():
        logger.error("Redis is not available. Make sure Redis server is running.")
        return

    logger.info("Starting social media metrics update")
    await update_all_accounts(args.organisation_id)
    logger.info("Update process completed")

if __name__ == "__main__":
    asyncio.run(main())
