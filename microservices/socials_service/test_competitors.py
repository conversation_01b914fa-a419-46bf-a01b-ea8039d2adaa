import asyncio
from app.database.session import get_db
from app.models import model
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload

async def test_get_competitors():
    async for db in get_db():
        try:
            # Test the query with selectinload
            result = await db.execute(
                select(model.TwitterCompetitor)
                .options(selectinload(model.TwitterCompetitor.metrics))
            )
            competitors = result.scalars().all()
            
            print(f"Found {len(competitors)} competitors")
            for comp in competitors:
                print(f"Competitor: {comp.username}, ID: {comp.id}")
                print(f"Metrics count: {len(comp.metrics)}")
                
            return competitors
        except Exception as e:
            print(f"Error: {str(e)}")
            raise

if __name__ == "__main__":
    asyncio.run(test_get_competitors())
