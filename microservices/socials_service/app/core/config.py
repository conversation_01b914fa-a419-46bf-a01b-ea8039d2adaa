from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Class to hold application's config values."""

    SECRET_KEY: str
    ALGORITHM: str
    # DATABASE CONFIGURATIONS
    DATABASE: str
    LOCAL_DATABASE: str

    # TWITTER CREDENTIALS
    TWITTER_API_KEY: str
    TWITTER_API_SECRET_KEY: str
    TWITTER_ACCESS_TOKEN: str
    TWITTER_ACCESS_TOKEN_SECRET: str
    TWITTER_CLIENT_SECRET: str
    TWITTER_CLIENT_ID: str
    TWITTER_BEARER_TOKEN: str
    TWITTER_CALLBACK_URL: str
    TWITTER_IMAGE_URL: str
    TWITTER_BASE_URL: str
    RATE_LIMIT_REQUESTS: int = 1000
    RATE_LIMIT_PERIOD_MINUTES: int = 15

    # FACEBOOK CREDENTIALS
    FACEBOOK_CLIENT_ID: str
    FACEBOOK_CLIENT_SECRET: str
    FACEBOOK_REDIRECT_URI: str
    FACEBOOK_OAUTH_URL: str
    FACEBOOK_TOKEN_URL: str
    FACEBOOK_USER_INFO_URL: str
    FACEBOOK_GRAPH_API_URL: str
    FACEBOOK_AD_ID: str
    FACEBOOK_GRAPH_VIDEO_API_URL: str
    FACEBOOK_CONFIGURATION_ID: str
    FACEBOOK_BUSINESS_ID: str

    # INSTAGRAM CREDENTIALLS
    INSTAGRAM_GRAPH_API_URL: str
    INSTAGRAM_REDIRECT_URL: str
    INSTAGRAM_APP_ID: str
    INSTAGRAM_APP_SECRET: str
    INSTAGRAM_EMBED_URL: str
    INSTAGRAM_API_BASE_URL: str

    # LINKEDIN CREDENTIALS
    LINKEDIN_CLIENT_ID: str
    LINKEDIN_CLIENT_SECRET: str
    LINKEDIN_REDIRECT_URI: str

    AUTH_SERVICE_URL: str
    SETTINGS_SERVICE_URL: str
    CHAT_SERVICE_URL: str
    SCHEDULE_PERIOD: int
    REDIS_HOST: str
    FRONTEND_SOCIALS_ENDPOINT: str

    # CACHE SETTINGS
    REDIS_CACHE_EXPIRY_SECONDS: int = 300  # Default 5 minutes (300 seconds)
    REDIS_LONG_CACHE_EXPIRY_SECONDS: int = 43200
    ALLOWED_EXT: list

    # Email Configurations
    MAIL_USERNAME: str
    MAIL_PASSWORD: str
    MAIL_FROM: str
    MAIL_FROM_NAME: str
    MAIL_PORT: int
    MAIL_SERVER: str
    TEMPLATE_FOLDER: str = "app/templates"

    # Environment
    ENV: str = "DEVELOPMENT"

    # Frontend URL for content links
    FRONTEND_BASE_URL: str = "http://localhost:3000"

    ENV: str

    class Config:
        env_file = ".env"
        extra = "ignore"


settings = Settings()
