from app.core.config import settings
from app.utils.logger import get_logger
from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import declarative_base, sessionmaker

logger = get_logger(__name__)

DATABASE_URI = (
    settings.DATABASE if settings.ENV == "PRODUCTION" else settings.LOCAL_DATABASE
)

logger.info(f"Running in {settings.ENV} mode with db uri {DATABASE_URI}")
engine = create_async_engine(
    DATABASE_URI,
    echo=False,
    pool_pre_ping=True,
    pool_recycle=1800,
    pool_timeout=30,
    pool_size=10,
    max_overflow=20,
)
SessionLocal = sessionmaker(
    bind=engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autocommit=False, autoflush=False
)
Base = declarative_base()


async def create_db_and_tables():
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)


async def get_db() -> AsyncSession:
    async with SessionLocal() as db:
        try:
            yield db
        finally:
            await db.close()
