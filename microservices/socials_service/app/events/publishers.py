"""
Event publishers for Socials service.
"""

import logging
from typing import Any, Dict, Optional

from .client import get_event_client

logger = logging.getLogger(__name__)


async def publish_post_scheduled(
    post_id: str,
    user_id: str,
    organization_id: str,
    platform: str,
    content: str,
    scheduled_time: str,
    correlation_id: Optional[str] = None,
) -> str:
    """Publish a social media post scheduled event."""
    try:
        event_client = get_event_client()
        
        data = {
            "post_id": post_id,
            "user_id": user_id,
            "organization_id": organization_id,
            "platform": platform,
            "content": content,
            "scheduled_time": scheduled_time,
        }
        
        return await event_client.publish_event(
            event_type="socials.post.scheduled",
            data=data,
            correlation_id=correlation_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    except Exception as e:
        logger.error(f"Failed to publish post scheduled event: {e}")
        return ""


async def publish_post_published(
    post_id: str,
    user_id: str,
    organization_id: str,
    platform: str,
    content: str,
    post_url: Optional[str] = None,
    correlation_id: Optional[str] = None,
) -> str:
    """Publish a social media post published event."""
    try:
        event_client = get_event_client()
        
        data = {
            "post_id": post_id,
            "user_id": user_id,
            "organization_id": organization_id,
            "platform": platform,
            "content": content,
            "post_url": post_url,
        }
        
        return await event_client.publish_event(
            event_type="socials.post.published",
            data=data,
            correlation_id=correlation_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    except Exception as e:
        logger.error(f"Failed to publish post published event: {e}")
        return ""


async def publish_metrics_updated(
    user_id: str,
    organization_id: str,
    platform: str,
    metrics_type: str,
    metrics_data: Dict[str, Any],
    correlation_id: Optional[str] = None,
) -> str:
    """Publish a social media metrics update event."""
    try:
        event_client = get_event_client()
        
        data = {
            "user_id": user_id,
            "organization_id": organization_id,
            "platform": platform,
            "metrics_type": metrics_type,
            "metrics_data": metrics_data,
        }
        
        return await event_client.publish_event(
            event_type="socials.metrics.updated",
            data=data,
            correlation_id=correlation_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    except Exception as e:
        logger.error(f"Failed to publish metrics updated event: {e}")
        return ""


async def publish_platform_connected(
    user_id: str,
    organization_id: str,
    platform: str,
    account_info: Dict[str, Any],
    correlation_id: Optional[str] = None,
) -> str:
    """Publish a social media platform connection event."""
    try:
        event_client = get_event_client()
        
        data = {
            "user_id": user_id,
            "organization_id": organization_id,
            "platform": platform,
            "account_info": account_info,
        }
        
        return await event_client.publish_event(
            event_type="socials.platform.connected",
            data=data,
            correlation_id=correlation_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    except Exception as e:
        logger.error(f"Failed to publish platform connected event: {e}")
        return ""


async def publish_platform_disconnected(
    user_id: str,
    organization_id: str,
    platform: str,
    reason: Optional[str] = None,
    correlation_id: Optional[str] = None,
) -> str:
    """Publish a social media platform disconnection event."""
    try:
        event_client = get_event_client()
        
        data = {
            "user_id": user_id,
            "organization_id": organization_id,
            "platform": platform,
            "reason": reason,
        }
        
        return await event_client.publish_event(
            event_type="socials.platform.disconnected",
            data=data,
            correlation_id=correlation_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    except Exception as e:
        logger.error(f"Failed to publish platform disconnected event: {e}")
        return ""


async def publish_content_scheduled_with_reviewers(
    content_id: str,
    organization_id: str,
    user_id: str,
    scheduled_by: dict,
    content: str,
    platform: str,
    scheduled_time: str,
    reviewers: list,
    correlation_id: Optional[str] = None,
) -> str:
    """
    Publish an event when content is scheduled, notifying all reviewers.
    reviewers: list of user IDs or emails to notify.
    """
    try:
        event_client = get_event_client()

        data = {
            "content_id": content_id,
            "organization_id": organization_id,
            "user_id": user_id,
            "scheduled_by": scheduled_by,
            "content": content,
            "platform": platform,
            "scheduled_time": scheduled_time,
            "reviewers": reviewers,
        }

        return await event_client.publish_event(
            event_type="socials.content.scheduled",
            data=data,
            correlation_id=correlation_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    except Exception as e:
        logger.error(f"Failed to publish content scheduled with reviewers event: {e}")
        return ""


async def publish_content_updated_with_reviewers(
    content_id: str,
    organization_id: str,
    user_id: str,
    updated_by: dict,
    content: str,
    platform: str,
    scheduled_time: str,
    scheduled_status: str,
    reviewers: list,
    correlation_id: Optional[str] = None,
) -> str:
    """
    Publish an event when scheduled content is updated, including reviewer updates.
    reviewers: list of user IDs or emails to notify.
    """
    try:
        event_client = get_event_client()

        data = {
            "content_id": content_id,
            "organization_id": organization_id,
            "user_id": user_id,
            "updated_by": updated_by,
            "content": content,
            "platform": platform,
            "scheduled_time": scheduled_time,
            "scheduled_status": scheduled_status,
            "reviewers": reviewers,
        }

        return await event_client.publish_event(
            event_type="socials.content.updated",
            data=data,
            correlation_id=correlation_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    except Exception as e:
        logger.error(f"Failed to publish content updated with reviewers event: {e}")
        return ""
