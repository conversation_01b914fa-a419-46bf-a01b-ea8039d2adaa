import redis
from app.core.config import settings
from app.utils.logger import get_logger

logger = get_logger(__name__)


class RedisClient:
    logger.info("Connecting to Redis...")
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(RedisClient, cls).__new__(cls)
            cls._instance.client = redis.StrictRedis(
                host=settings.REDIS_HOST, port=6379, db=2, decode_responses=True
            )
        return cls._instance

    def get_client(self):
        logger.info("Connected to Redis successfully.")
        return self.client


redis_client = RedisClient().get_client()
