import matplotlib.pyplot as plt
import io
import base64
import pandas as pd
import numpy as np
from typing import List, Dict, Any
import logging

logger = logging.getLogger(__name__)

class AnalysisService:
    @staticmethod
    def generate_follower_growth_chart(dates, followers, username):
        """Generate a chart showing follower growth over time"""
        plt.figure(figsize=(10, 6))
        plt.plot(dates, followers, marker='o', linestyle='-', color='#1DA1F2')  # X blue color
        plt.title(f"Follower Growth for @{username}")
        plt.xlabel("Date")
        plt.ylabel("Followers")
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.tight_layout()
        
        # Save chart to memory
        buffer = io.BytesIO()
        plt.savefig(buffer, format='png', dpi=100)
        plt.close()
        buffer.seek(0)
        
        # Encode chart to base64
        chart_data = base64.b64encode(buffer.getvalue()).decode('utf-8')
        
        return chart_data
    
    @staticmethod
    def generate_engagement_comparison_chart(engagement_data):
        """Generate a chart comparing engagement rates between users"""
        usernames = [data["username"] for data in engagement_data]
        engagement_rates = [data["engagement_rate"] for data in engagement_data]
        
        plt.figure(figsize=(12, 8))
        colors = ['#1DA1F2', '#657786', '#AAB8C2', '#E1E8ED', '#F5F8FA']  # X color palette
        bars = plt.bar(
            usernames, 
            engagement_rates, 
            color=colors[:len(usernames)] if len(usernames) <= len(colors) else colors * (len(usernames) // len(colors) + 1)
        )
        
        # Add value labels above bars
        for bar in bars:
            height = bar.get_height()
            plt.text(
                bar.get_x() + bar.get_width()/2., 
                height + 0.1,
                f'{height:.2f}%',
                ha='center', 
                va='bottom',
                fontweight='bold'
            )
        
        plt.title("Engagement Rate Comparison", fontsize=16, fontweight='bold')
        plt.xlabel("Username", fontsize=12)
        plt.ylabel("Engagement Rate (%)", fontsize=12)
        plt.grid(True, axis='y', linestyle='--', alpha=0.7)
        plt.tight_layout()
        
        # Save chart to memory
        buffer = io.BytesIO()
        plt.savefig(buffer, format='png', dpi=100)
        plt.close()
        buffer.seek(0)
        
        # Encode chart to base64
        chart_data = base64.b64encode(buffer.getvalue()).decode('utf-8')
        
        return chart_data
    
    @staticmethod
    def generate_content_analysis_chart(content_data):
        """Generate a chart showing content type distribution"""
        usernames = list(content_data.keys())
        
        media_percentages = [content_data[u]["media_percentage"] for u in usernames]
        url_percentages = [content_data[u]["url_percentage"] for u in usernames]
        hashtag_percentages = [content_data[u]["hashtag_percentage"] for u in usernames]
        
        x = np.arange(len(usernames))  # Label locations
        width = 0.25  # Width of the bars
        
        fig, ax = plt.subplots(figsize=(12, 8))
        rects1 = ax.bar(x - width, media_percentages, width, label='Media', color='#1DA1F2')
        rects2 = ax.bar(x, url_percentages, width, label='URLs', color='#657786')
        rects3 = ax.bar(x + width, hashtag_percentages, width, label='Hashtags', color='#AAB8C2')
        
        ax.set_title('Content Type Distribution', fontsize=16, fontweight='bold')
        ax.set_xlabel('Username', fontsize=12)
        ax.set_ylabel('Percentage of Tweets (%)', fontsize=12)
        ax.set_xticks(x)
        ax.set_xticklabels(usernames)
        ax.legend()
        ax.grid(True, axis='y', linestyle='--', alpha=0.7)
        
        # Add value labels
        def add_labels(rects):
            for rect in rects:
                height = rect.get_height()
                ax.annotate(f'{height:.1f}%',
                            xy=(rect.get_x() + rect.get_width() / 2, height),
                            xytext=(0, 3),  # 3 points vertical offset
                            textcoords="offset points",
                            ha='center', va='bottom')
        
        add_labels(rects1)
        add_labels(rects2)
        add_labels(rects3)
        
        fig.tight_layout()
        
        # Save chart to memory
        buffer = io.BytesIO()
        plt.savefig(buffer, format='png', dpi=100)
        plt.close()
        buffer.seek(0)
        
        # Encode chart to base64
        chart_data = base64.b64encode(buffer.getvalue()).decode('utf-8')
        
        return chart_data
    
    @staticmethod
    def generate_posting_schedule_chart(schedule_data, username):
        """Generate a chart showing when a user typically posts"""
        # Create a figure with two subplots
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
        
        # Days of the week
        days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        day_percentages = [schedule_data["by_day"][i] for i in range(7)]
        
        # Hours of the day
        hours = [f'{h:02d}:00' for h in range(24)]
        hour_percentages = [schedule_data["by_hour"][i] for i in range(24)]
        
        # Plot days
        bars1 = ax1.bar(days, day_percentages, color='#1DA1F2')
        ax1.set_title(f'Posting Schedule by Day for @{username}', fontsize=14, fontweight='bold')
        ax1.set_ylabel('Percentage of Posts (%)', fontsize=12)
        ax1.grid(True, axis='y', linestyle='--', alpha=0.7)
        
        # Add value labels for days
        for bar in bars1:
            height = bar.get_height()
            ax1.annotate(f'{height:.1f}%',
                        xy=(bar.get_x() + bar.get_width() / 2, height),
                        xytext=(0, 3),  # 3 points vertical offset
                        textcoords="offset points",
                        ha='center', va='bottom')
        
        # Plot hours
        bars2 = ax2.bar(hours, hour_percentages, color='#657786')
        ax2.set_title(f'Posting Schedule by Hour for @{username}', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Hour of Day (UTC)', fontsize=12)
        ax2.set_ylabel('Percentage of Posts (%)', fontsize=12)
        ax2.set_xticks(range(0, 24, 2))  # Show every other hour to avoid crowding
        ax2.set_xticklabels([hours[i] for i in range(0, 24, 2)])
        ax2.grid(True, axis='y', linestyle='--', alpha=0.7)
        
        # Add value labels for hours with significant percentages
        for bar in bars2:
            height = bar.get_height()
            if height > 2.0:  # Only label bars with significant percentages
                ax2.annotate(f'{height:.1f}%',
                            xy=(bar.get_x() + bar.get_width() / 2, height),
                            xytext=(0, 3),  # 3 points vertical offset
                            textcoords="offset points",
                            ha='center', va='bottom')
        
        plt.tight_layout()
        
        # Save chart to memory
        buffer = io.BytesIO()
        plt.savefig(buffer, format='png', dpi=100)
        plt.close()
        buffer.seek(0)
        
        # Encode chart to base64
        chart_data = base64.b64encode(buffer.getvalue()).decode('utf-8')
        
        return chart_data