import json
from datetime import datetime
from typing import List

import httpx
from sqlalchemy.ext.asyncio import AsyncSession

from sqlalchemy import select
from app.models import model
from app.services.facebook import (
    composite_score,
    extract_images,
)
from app.utils.redis_cache import redis_client
from app.core.config import settings

FACEBOOK_GRAPH_API_URL = settings.FACEBOOK_GRAPH_API_URL
redis_long_expiry_time = settings.REDIS_LONG_CACHE_EXPIRY_SECONDS
timeout = httpx.Timeout(30.0, connect=30.0, read=30.0, write=30.0)


async def fetch_all_facebook_posts(db_session, organisation_id):
    """
    Fetch all posts for a Facebook page, following pagination.
    Returns a list of all posts.
    """
    # fetch only posts made from the app for the correct social account
    result = await db_session.execute(
        select(model.SocialMediaAccount)
        .where(model.SocialMediaAccount.organisation_id == organisation_id)
        .where(model.SocialMediaAccount.platform == "facebook")
    )
    social_account = result.scalars().first()

    if not social_account:
        return []

    results = await db_session.execute(
        select(model.Post)
        .where(model.Post.social_media_account_id == social_account.id)
        .order_by(model.Post.created_at.desc())
    )
    db_posts = results.scalars().all()

    if not db_posts:
        return []

    async with httpx.AsyncClient(timeout=timeout) as client:

        params = {
            "access_token": social_account.page_access_token,
            "fields": "message,id,created_time,full_picture,attachments{subattachments,media,type,title,url},insights.metric(page_fans,post_impressions,post_clicks,post_reactions_like_total,post_impressions_unique),reactions.limit(0).summary(true),comments.limit(0).summary(true)",
        }
        # all_posts = []
        # while url:
        #     response = await client.get(url, params=params if url.endswith('/posts') else None)
        #     response.raise_for_status()
        #     data = response.json()
        #     posts = data.get("data", [])
        #     all_posts.extend(posts)
        #     # Prepare for next page
        #     paging = data.get("paging", {})
        #     url = paging.get("next")
        #     params = None  # Only use params on the first request

        # if not all_posts:
        #     return []

        formatted_post = []
        for posts in db_posts:
            url = f"{FACEBOOK_GRAPH_API_URL}/{posts.post_id}"

            response = await client.get(url, params=params)
            response.raise_for_status()
            post = response.json()

            # Gather all paginated insights for this post
            insights_data = post.get("insights", {}).get("data", [])
            insights_paging = post.get("insights", {}).get("paging", {})
            insights_next_url = insights_paging.get("next")
            # Use the same client for efficiency
            while insights_next_url:
                insights_response = await client.get(insights_next_url)
                insights_response.raise_for_status()
                insights_json = insights_response.json()
                next_data = insights_json.get("data", [])
                insights_data.extend(next_data)
                insights_next_url = insights_json.get("paging", {}).get("next")
            # Deduplicate insights by (name, period)
            insights_dict = {}
            for item in insights_data:
                name = item["name"]
                period = item["period"]
                value = item["values"][0]["value"]
                insights_dict[(name, period)] = {"value": value, "period": period}
            insights = [
                {name: data} for (name, period), data in insights_dict.items()
            ]
            reactions = post.get("reactions", {}).get("summary", {}).get("total_count", 0)
            comments = post.get("comments", {}).get("summary", {}).get("total_count", 0)
            images = extract_images(post)
            response_post = {
                "id": post["id"],
                "message": post.get("message", ""),
                "full_picture": post.get("full_picture", ""),
                "created_at": post["created_time"],
                "insights": insights,
                "reactions": reactions,
                "comments": comments,
                "images": images
            }
            formatted_post.append(response_post)

            # Save to database
            try:
                created_time = None
                if post.get("created_time"):
                    try:
                        created_time = datetime.strptime(post["created_time"], "%Y-%m-%dT%H:%M:%S%z")
                        created_time = created_time.replace(tzinfo=None)
                    except ValueError:
                        try:
                            created_time = datetime.strptime(post["created_time"], "%Y-%m-%dT%H:%M:%S+0000")
                        except ValueError:
                            created_time = None

                impressions = next((list(metric.values())[0]["value"] for metric in insights if "post_impressions" in list(metric.keys())[0]), 0)
                clicks = next((list(metric.values())[0]["value"] for metric in insights if "post_clicks" in list(metric.keys())[0]), 0)
                reach = next((list(metric.values())[0]["value"] for metric in insights if "post_impressions_unique" in list(metric.keys())[0]), 0)
                engagement_rate = 0
                if reach > 0:
                    engagement_rate = ((reactions + comments + clicks) / reach) * 100

                top_post = model.FacebookTopPerformingPost(
                    organisation_id=organisation_id,
                    page_id=social_account.page_id,
                    post_id=post["id"],
                    message=post.get("message"),
                    created_time=created_time,
                    permalink_url=f"https://www.facebook.com/{post['id']}",
                    full_picture=post.get("full_picture"),
                    impressions=impressions,
                    reach=reach,
                    likes=reactions,
                    comments=comments,
                    clicks=clicks,
                    engagement_rate=engagement_rate
                )
                top_post.set_attachments(post.get("attachments", {}).get("data", []))
                top_post.set_insights(insights)
                top_post.set_images(images)
                db_session.add(top_post)
            except Exception:
                pass

    # Commit DB changes, ignore errors
    try:
        await db_session.commit()
    except Exception:
        await db_session.rollback()
    return


class FacebookTopPostsService:
    @staticmethod
    async def get_top_posts(
        organisation_id: str,
        db_session: AsyncSession,
        limit: int = 5
    ) -> List[dict]:
        """
        Retrieve the top performing posts for a Facebook page
        based on impressions or composite score.
        Checks cache, then DB, then Facebook API.
        """

        result = await db_session.execute(
            select(model.FacebookTopPerformingPost)
            .filter(model.FacebookTopPerformingPost.organisation_id == organisation_id)
            .order_by(model.FacebookTopPerformingPost.collected_at.desc())
        )
        db_top_posts = result.scalars().all()

        top_posts = []
        if not db_top_posts:
            return top_posts

        for post in db_top_posts:
            post_data = {
                "id": post.post_id,
                "message": post.message,
                "created_at": post.created_time.isoformat() if post.created_time else None,
                "full_picture": post.full_picture,
                "attachments": post.get_attachments(),
                "insights": post.get_insights(),
                "reactions": post.likes,
                "comments": post.comments,
                "images": post.get_images(),
            }
            top_posts.append(post_data)
        # Sort by composite score
        top_posts = sorted(
            top_posts,
            key=lambda x: composite_score(x.get("insights", [])),
            reverse=True
        )[:limit]

        return top_posts
