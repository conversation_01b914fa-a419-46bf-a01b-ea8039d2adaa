from typing import Dict
from fastapi import WebSocket

# Mapping of user_id to WebSocket connection, confirm if i can use something else
active_connections: Dict[str, WebSocket] = {}


# Register a new WebSocket connection for a user
def register_connection(user_id: str, websocket: WebSocket):
    active_connections[user_id] = websocket


# Unregister a WebSocket connection for a user
def unregister_connection(user_id: str):
    active_connections.pop(user_id, None)

# Publish a new message event to Redis for a user
# async def publish_new_message(user_id: str, message_data: dict):
#     redis = aioredis.from_url(REDIS_URL, decode_responses=True)
#     channel = f"{MESSAGE_CHANNEL_PREFIX}{user_id}"
#     await redis.publish(channel, message_data)
#     await redis.close()

# # Background task: subscribe to all message channels and push to WebSocket
# async def redis_subscriber():
#     redis = aioredis.from_url(REDIS_URL, decode_responses=True)
#     pubsub = redis.pubsub()
#     # Subscribe to pattern for all user message channels
#     await pubsub.psubscribe(f"{MESSAGE_CHANNEL_PREFIX}*")
#     print("Subscribed to Redis message channels.")

#     async for message in pubsub.listen():
#         if message["type"] == "pmessage":
#             channel = message["channel"]
#             data = message["data"]
#             # Extract user_id from channel name
#             if channel.startswith(MESSAGE_CHANNEL_PREFIX):
#                 user_id = channel[len(MESSAGE_CHANNEL_PREFIX):]
#                 websocket = active_connections.get(user_id)
#                 if websocket:
#                     try:
#                         await websocket.send_text(data)
#                     except Exception:
#                         unregister_connection(user_id)
#     await redis.close()


async def publish_new_message(user_id: str, message_data: dict):
    # check if user has active connections
    if user_id in active_connections:
        websocket = active_connections[user_id]
        await websocket.send_json({
            "event": "new_message",
            "data": message_data
        })
    return {"status": "delivered" if websocket else "user_not_connected"}
