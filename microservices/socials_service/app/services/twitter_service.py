import tweepy
from fastapi import HTT<PERSON>Exception
from app.core.config import settings
import pandas as pd
from datetime import datetime, timedelta
import time
import logging
from typing import List, Dict, Any, Optional
import re

logger = logging.getLogger(__name__)

class TwitterService:
    def __init__(self):
        self.client = self._get_twitter_client()
        self.rate_limit_remaining = settings.RATE_LIMIT_REQUESTS
        self.rate_limit_reset = datetime.now() + timedelta(minutes=settings.RATE_LIMIT_PERIOD_MINUTES)
        
    def _get_twitter_client(self):
        """Initialize the Twitter API v2 client with appropriate credentials"""
        try:
            client = tweepy.Client(
                bearer_token=settings.TWITTER_BEARER_TOKEN,
                consumer_key=settings.TWITTER_API_KEY,
                consumer_secret=settings.TWITTER_API_SECRET_KEY,
                access_token=settings.TWITTER_ACCESS_TOKEN,
                access_token_secret=settings.TWITTER_ACCESS_TOKEN_SECRET,
                wait_on_rate_limit=True  # Automatically wait when rate limited
            )
            return client
        except Exception as e:
            logger.error(f"Failed to connect to X API: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to connect to X API: {str(e)}")
    
    def _handle_rate_limit(self):
        """Handle API rate limiting based on configured limits"""
        now = datetime.now()
        if now > self.rate_limit_reset:
            # Reset rate limit after the time period
            self.rate_limit_remaining = settings.RATE_LIMIT_REQUESTS
            self.rate_limit_reset = now + timedelta(minutes=settings.RATE_LIMIT_PERIOD_MINUTES)
        
        if self.rate_limit_remaining <= 0:
            # Wait until rate limit resets
            wait_time = (self.rate_limit_reset - now).total_seconds()
            if wait_time > 0:
                logger.warning(f"Rate limit exceeded. Waiting for {wait_time} seconds.")
                time.sleep(wait_time)
            
            self.rate_limit_remaining = settings.RATE_LIMIT_REQUESTS
            self.rate_limit_reset = datetime.now() + timedelta(minutes=settings.RATE_LIMIT_PERIOD_MINUTES)
        
        self.rate_limit_remaining -= 1
    
    async def get_user_profile(self, username: str):
        """Get user profile information using X API v2"""
        self._handle_rate_limit()
        try:
            # Get user data with appropriate fields for v2 API
            user = self.client.get_user(
                username=username, 
                user_fields=["id", "name", "username", "created_at", 
                             "description", "profile_image_url", 
                             "public_metrics"]
            )
            
            if not user or not user.data:
                raise HTTPException(status_code=404, detail=f"User '{username}' not found")
                
            return user.data
        except tweepy.errors.TweepyException as e:
            error_msg = str(e)
            if "453" in error_msg:
                raise HTTPException(status_code=403, 
                                    detail="API access restricted. You need a different access level for this endpoint.")
            elif "401" in error_msg:
                raise HTTPException(status_code=401, 
                                    detail="Authentication error. Check your API credentials.")
            else:
                logger.error(f"Error fetching user profile: {error_msg}")
                raise HTTPException(status_code=500, detail=f"Error fetching user profile: {error_msg}")
    
    async def get_user_tweets(self, username: str, count: int = 100, days_limit: Optional[int] = None):
        """Get recent tweets for a user with X API v2"""
        self._handle_rate_limit()
        try:
            # First get the user ID (required for v2 endpoints)
            user = await self.get_user_profile(username)
            
            # Define start_time if days_limit is provided
            start_time = None
            if days_limit:
                start_time = datetime.now() - timedelta(days=days_limit)
            
            # Use Paginator to handle retrieving multiple pages of tweets if needed
            tweets = []
            # Setting max_results to a maximum of 100 per request (API limit)
            # Available fields: https://developer.twitter.com/en/docs/twitter-api/data-dictionary/object-model/tweet
            for tweet in tweepy.Paginator(
                self.client.get_users_tweets,
                id=user.id,
                max_results=min(100, count),
                tweet_fields=["created_at", "public_metrics", "text", "entities", "attachments"],
                start_time=start_time,
                # Calculate number of pages needed to get desired count
                limit=max(1, (count + 99) // 100)
            ).flatten(limit=count):
                tweets.append(tweet)
                
            return tweets
        except tweepy.errors.TweepyException as e:
            error_msg = str(e)
            if "453" in error_msg:
                raise HTTPException(status_code=403, 
                                    detail="API access restricted. You need a different access level for this endpoint.")
            else:
                logger.error(f"Failed to get tweets: {error_msg}")
                raise HTTPException(status_code=500, detail=f"Failed to get tweets: {error_msg}")
    
    async def get_engagement_metrics(self, username: str, days: int = 30):
        """Calculate engagement metrics for a user over a specific time period"""
        try:
            # Get tweets for the specified time period directly
            tweets = await self.get_user_tweets(username, count=100, days_limit=days)
            
            if not tweets:
                return {
                    "username": username,
                    "period_days": days,
                    "total_tweets": 0,
                    "avg_likes": 0,
                    "avg_retweets": 0,
                    "avg_replies": 0,
                    "avg_quotes": 0,
                    "engagement_rate": 0,
                    "content_metrics": {
                        "avg_tweet_length": 0,
                        "media_percentage": 0,
                        "url_percentage": 0,
                        "hashtag_percentage": 0
                    }
                }
            
            # Calculate engagement metrics
            total_tweets = len(tweets)
            total_likes = sum(t.public_metrics.get('like_count', 0) for t in tweets)
            total_retweets = sum(t.public_metrics.get('retweet_count', 0) for t in tweets)
            total_replies = sum(t.public_metrics.get('reply_count', 0) for t in tweets)
            total_quotes = sum(t.public_metrics.get('quote_count', 0) for t in tweets)
            
            avg_likes = total_likes / total_tweets if total_tweets > 0 else 0
            avg_retweets = total_retweets / total_tweets if total_tweets > 0 else 0
            avg_replies = total_replies / total_tweets if total_tweets > 0 else 0
            avg_quotes = total_quotes / total_tweets if total_tweets > 0 else 0
            
            # Calculate content metrics
            total_length = sum(len(t.text) for t in tweets)
            avg_tweet_length = total_length / total_tweets if total_tweets > 0 else 0
            
            # Count tweets with media, URLs, and hashtags
            media_count = sum(1 for t in tweets if hasattr(t, 'attachments') and t.attachments is not None)
            
            url_count = 0
            hashtag_count = 0
            for tweet in tweets:
                if hasattr(tweet, 'entities') and tweet.entities is not None:
                    if 'urls' in tweet.entities:
                        url_count += 1
                    if 'hashtags' in tweet.entities:
                        hashtag_count += 1
            
            media_percentage = (media_count / total_tweets) * 100 if total_tweets > 0 else 0
            url_percentage = (url_count / total_tweets) * 100 if total_tweets > 0 else 0
            hashtag_percentage = (hashtag_count / total_tweets) * 100 if total_tweets > 0 else 0
            
            # Get follower count for engagement rate calculation
            user = await self.get_user_profile(username)
            follower_count = user.public_metrics.get('followers_count', 0)
            
            # Calculate engagement rate
            if follower_count > 0 and total_tweets > 0:
                engagement_rate = ((total_likes + total_retweets + total_replies + total_quotes) / 
                                  (follower_count * total_tweets)) * 100
            else:
                engagement_rate = 0
            
            return {
                "username": username,
                "period_days": days,
                "total_tweets": total_tweets,
                "avg_likes": avg_likes,
                "avg_retweets": avg_retweets,
                "avg_replies": avg_replies,
                "avg_quotes": avg_quotes,
                "engagement_rate": engagement_rate,
                "content_metrics": {
                    "avg_tweet_length": avg_tweet_length,
                    "media_percentage": media_percentage,
                    "url_percentage": url_percentage,
                    "hashtag_percentage": hashtag_percentage
                }
            }
        except Exception as e:
            logger.error(f"Error calculating engagement metrics: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error calculating engagement metrics: {str(e)}")
    
    async def compare_profiles(self, usernames: List[str]):
        """Compare basic profile metrics for multiple users"""
        results = []
        
        for username in usernames:
            user = await self.get_user_profile(username)
            metrics = user.public_metrics
            
            results.append({
                "username": username,
                "followers": metrics.get('followers_count', 0),
                "following": metrics.get('following_count', 0),
                "tweet_count": metrics.get('tweet_count', 0),
                "listed_count": metrics.get('listed_count', 0)
            })
            
        return results
    
    async def get_top_hashtags(self, username: str, count: int = 100, top_n: int = 10):
        """Get the most frequently used hashtags by a user"""
        tweets = await self.get_user_tweets(username, count=count)
        
        hashtags = []
        for tweet in tweets:
            if hasattr(tweet, 'entities') and tweet.entities and 'hashtags' in tweet.entities:
                for hashtag in tweet.entities['hashtags']:
                    hashtags.append(hashtag['tag'].lower())
        
        # Count frequency of each hashtag
        hashtag_counts = {}
        for hashtag in hashtags:
            if hashtag in hashtag_counts:
                hashtag_counts[hashtag] += 1
            else:
                hashtag_counts[hashtag] = 1
        
        # Sort by frequency and return top N
        top_hashtags = sorted(hashtag_counts.items(), key=lambda x: x[1], reverse=True)[:top_n]
        
        return [{"hashtag": h[0], "count": h[1]} for h in top_hashtags]
    
    async def get_posting_schedule(self, username: str, days: int = 30):
        """Analyze when a user typically posts"""
        tweets = await self.get_user_tweets(username, count=200, days_limit=days)
        
        # Initialize counters for days and hours
        days_count = {i: 0 for i in range(7)}  # 0=Monday, 6=Sunday
        hours_count = {i: 0 for i in range(24)}
        
        for tweet in tweets:
            if tweet.created_at:
                # Count by day of week
                day = tweet.created_at.weekday()
                days_count[day] += 1
                
                # Count by hour
                hour = tweet.created_at.hour
                hours_count[hour] += 1
        
        # Convert to percentages
        total_tweets = len(tweets)
        if total_tweets > 0:
            for day in days_count:
                days_count[day] = (days_count[day] / total_tweets) * 100
                
            for hour in hours_count:
                hours_count[hour] = (hours_count[hour] / total_tweets) * 100
        
        return {
            "by_day": days_count,
            "by_hour": hours_count,
            "total_tweets_analyzed": total_tweets
        }
    