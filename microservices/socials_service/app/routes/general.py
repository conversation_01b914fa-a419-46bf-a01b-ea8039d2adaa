import json
from datetime import datetime, timezone
from typing import Annotated, List, Optional

from dateutil.relativedelta import relativedelta
from fastapi import APIRouter, Depends, HTTPException, Query, Request, status, BackgroundTasks
import httpx
from psycopg2 import OperationalError
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app.database.session import get_db
from app.models.model import MediaMetrics, Post, ScheduledContent, SocialMediaAccount
from app.routes.facebook import (
    get_convo,
    get_single_posts_facebook,
    get_top_performing_post,
    get_overview as facebook_get_overview,
    get_comments as facebook_get_comments
)
from app.routes.instagram import get_instagram_overview_metrics_route, get_media_insights
from app.routes.twitter import (
    get_direct_messages,
    get_top_performing_contents,
    get_tweet,
    get_comments as twitter_get_comments,
    get_overview as twitter_get_overview
)
from app.schemas.schema import (
    ApprovalStatus,
    CalendarViewResponse,
    ContentView,
    ContentViewResponse,
    MetricsResponse,
    Platform,
    ResponsePayload,
    ScheduleContentRequest,
    ScheduleContentStatus,
    SocialAccountDisconnect,
    SocialAccountPublic,
    UpdateScheduleContentRequest,
)
from app.utils.dependency import (
    check_permissions,
    decode_JWT_Response,
    get_current_user,
    verify_organization,
)
from app.utils.external_calls import send_notification_request, get_user_details
from app.utils.logger import get_logger
from app.utils.success_response import fail_response, success_response
from app.utils.twitter_engagement_calculator import calculate_twitter_engagement
from app.events.publishers import publish_content_scheduled_with_reviewers, publish_content_updated_with_reviewers
from app.core.config import settings
from app.utils.email_service import EmailService

logger = get_logger(__name__)

router = APIRouter()


@router.get("/socials_decode")
async def decode_JWT_token_to_get_details(token: str):
    """Decode a JWT token to get the details"""
    try:
        response = await decode_JWT_Response(token)
        return success_response(200, "Decoded successfully", response)
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        return fail_response(500, "An unexpected error occurred")


@router.get("/org/socials", response_model=List[SocialAccountPublic])
async def get_org_socials(
    _: Annotated[str, Depends(get_current_user)],
    organisation_id: Annotated[str, Depends(verify_organization)],
    db: AsyncSession = Depends(get_db),
):
    """
    Retrieves the social media handles configured for a given organization.
    """
    logger.info(f"Fetching social media handles for organisation_id: {organisation_id}")

    try:
        result = await db.execute(
            select(SocialMediaAccount).filter_by(organisation_id=organisation_id)
        )
        socials = result.scalars().all()

        logger.info("getting the registered user socials")

        social_dicts = [social.to_dict() for social in socials]

        logger.info(
            f"Successfully retrieved social media handles for organisation_id: {organisation_id}"
        )
        return success_response(200, "social accounts returned", social_dicts)
    except OperationalError as e:
        logger.error(f"OperationalError occurred: {str(e)}")
        raise HTTPException(
            status_code=503,
            detail="SSL connection has been closed unexpectedly, please try again",
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        return fail_response(500, "An unexpected error occurred")


@router.get("/top-contents")
async def get_top_products(
    _: Annotated[str, Depends(get_current_user)],
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db),
):
    """
    Retrieve the top-performing contents from both Twitter and Facebook.
    """
    try:
        combined_top = []
        logger.info(
            f"Fetching top-performing contents for organisation_id: {organisation_id}"
        )
        # Get top-performing tweets
        try:
            twitter_response = await get_top_performing_contents(
                organisation_id=organisation_id, db_session=db_session
            )
            if twitter_response.status_code != 200:
                logger.error(
                    f"Error fetching Twitter contents: {twitter_response.status_code} - {twitter_response.body}"
                )
                twitter_top = []
            else:
                twitter_data = json.loads(twitter_response.body)
                twitter_top = twitter_data.get("data", [])

            if isinstance(twitter_top, list) and all(
                isinstance(tweet, dict) for tweet in twitter_top
            ):
                for tweet in twitter_top:
                    tweet["platform"] = "Twitter"
                combined_top.extend(twitter_top)
            else:
                logger.warning("Invalid data format received from twitter")
        except Exception as e:
            logger.error(f"Twitter top-performing contents failed: {str(e)}")
            raise e

        # Get top-performing Facebook posts
        try:
            facebook_response = await get_top_performing_post(
                organisation_id=organisation_id, db_session=db_session
            )
            if facebook_response.status_code != 200:
                logger.error(
                    f"Error fetching Facebook contents: {facebook_response.status_code} - {facebook_response.body}"
                )
                facebook_top = []
            else:
                facebook_data = json.loads(facebook_response.body)
                facebook_top = facebook_data.get("data", [])
            if isinstance(facebook_top, list) and all(
                isinstance(post, dict) for post in facebook_top
            ):
                for post in facebook_top:
                    post["platform"] = "Facebook"
                combined_top.extend(facebook_top)
            else:
                logger.warning("Invalid data format received from Facebook")
        except Exception as e:
            logger.error(f"Facebook top-performing posts failed: {str(e)}")
            raise e

        if not combined_top:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Failed to retrieve top-performing contents from both platforms.",
            )

        logger.info(
            f"Successfully retrieved top-performing contents for organisation_id: {organisation_id}"
        )
        return success_response(
            200, "Top-performing products retrieved successfully.", combined_top
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(
            f"An error occurred while fetching top-performing contents: {str(e)}"
        )
        return fail_response(500, "An unexpected error occurred")


@router.get("/combined-overview")
async def get_combined_overview(
    _: Annotated[str, Depends(get_current_user)],
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db),
):
    """
    Retrieve a combined overview of followers, engagements, impressions, and reach from all platforms.
    """
    try:
        logger.info(
            f"Fetching combined overview for organisation_id: {organisation_id}"
        )

        combined_overview = []

        # Get overview from Twitter
        try:
            twitter_response = await twitter_get_overview(
                organisation_id=organisation_id, db_session=db_session
            )
            if twitter_response.status_code != 200:
                logger.error(
                    f"Error fetching Twitter overview: {twitter_response.status_code} - {twitter_response.body}"
                )
                twitter_data = {}
            else:
                twitter_data = json.loads(twitter_response.body)
            if isinstance(twitter_data, dict):
                twitter_data = twitter_data.get("data", {})
                twitter_data["platform"] = "Twitter"
                combined_overview.append(twitter_data)
            else:
                logger.warning("Invalid data format from twitter overview")
        except Exception as e:
            logger.error(f"Twitter overview failed: {str(e)}")

        # Get overview from Facebook
        try:
            facebook_response = await facebook_get_overview(organisation_id, db_session)
            if facebook_response.status_code != 200:
                logger.error(
                    f"Error fetching Facebook overview: {facebook_response.status_code} - {facebook_response.body}"
                )
                facebook_data = {}
            else:
                facebook_data = json.loads(facebook_response.body)
            if isinstance(facebook_data, dict):
                facebook_data = facebook_data.get("data", {})
                facebook_data["platform"] = "Facebook"
                combined_overview.append(facebook_data)
            else:
                logger.warning("Invalid data format from Facebook overview")
        except Exception as e:
            logger.error(f"Facebook overview failed: {str(e)}")

        # Get overview from Instagram
        try:
            instagram_response = await get_instagram_overview_metrics_route(organisation_id, db_session)
            if instagram_response.status_code != 200:
                logger.error(
                    f"Error fetching Instagram overview: {instagram_response.status_code} - {instagram_response.body}"
                )
                instagram_data = {}
            else:
                instagram_data = json.loads(instagram_response.body)
            if isinstance(instagram_data, dict):
                instagram_data = instagram_data.get("data", {})
                instagram_data["platform"] = "Instagram"
                combined_overview.append(instagram_data)
            else:
                logger.warning("Invalid data format from Instagram overview")
        except Exception as e:
            logger.error(f"Instagram overview failed: {str(e)}")

        if not combined_overview:
            logger.error("Failed to retrieve overview from all platforms.")
            return fail_response(
                404, "Failed to retrieve overview from all platforms."
            )

        logger.info(
            f"Successfully retrieved combined overview for organisation_id: {organisation_id}"
        )
        return success_response(
            200, "Combined overview retrieved successfully.", combined_overview
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"An error occurred while fetching combined overview: {str(e)}")
        return fail_response(500, "An unexpected error occurred")


@router.delete("/org/socials")
async def disconnect_social(
    social_details: SocialAccountDisconnect,
    token: Annotated[str, Depends(get_current_user)],
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db),
):
    """disconnects an organisation connected socials"""
    try:
        # confirm user has rights to delete socials
        user_role = await check_permissions(
            token.get("user_id"), organisation_id, "can delete socials"
        )
        # check the socials is connected
        result = await db_session.execute(
            select(SocialMediaAccount).filter_by(
                organisation_id=organisation_id,
                platform=social_details.platform_name,
            )
        )
        db_social = result.scalars().first()
        if not db_social:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="social media credentials not found",
            )

        logger.info(
            f"{user_role} is attempting to disconnect {db_social.username} from the platform"
        )

        await db_session.delete(db_social)
        await db_session.commit()
        logger.info(f"Successfully disconnected {db_social.username} from the platform")
        return success_response(200, "Disconnected successfully")
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"an exception occurred: {str(e)}")
        return fail_response(500, "An unexpected error has occurred")


# ############## SCHEDULING CONTENTS ###################


@router.post(
    "/schedule-content", tags=["Schedule"], response_model=ResponsePayload)
async def schedule_content(
    token: Annotated[dict, Depends(get_current_user)],
    organisation_id: Annotated[str, Depends(verify_organization)],
    request: ScheduleContentRequest,
    http_request: Request,
    background_tasks: BackgroundTasks,
    db_session: AsyncSession = Depends(get_db),
):
    user_id = token.get("user_id")

    try:
        logger.info(
            f"Scheduling content for organisation_id: {organisation_id} by user_email: {token.get('email')}"
        )
        user_role = await check_permissions(
            user_id, organisation_id, "can schedule content"
        )
        now = (
            datetime.now(request.post_time.tzinfo)
            if request.post_time.tzinfo
            else datetime.now()
        )
        if request.status != 'draft':
            if request.post_time < now:
                logger.error(
                    f"Attempted to schedule content in the past for organisation_id: {organisation_id} by user_email: {token.get('email')}"
                )
                raise HTTPException(400, "Post time cannot be in the past.")
        scheduled_content = ScheduledContent(
            organization_id=organisation_id,
            user_id=user_id,
            user_role=user_role,
            platforms=request.platforms,
            post_time=request.post_time,
            content=request.content,
            reviewer_ids=request.reviewer_ids,
            media_links=request.media_links,
            media_type=request.media_type,
            status=request.status,
        )
        if user_role == "owner" or user_role == "admin":
            scheduled_content.approval_status = ApprovalStatus.approved
            scheduled_content.approved_by = user_id

        db_session.add(scheduled_content)
        try:
            await db_session.commit()
        except Exception as e:
            await db_session.rollback()
            logger.error(f"Database commit error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An error occurred during saving to database"
            )

        # Alert reviewers of the request
        if request.status == "scheduled":
            try:
                await publish_content_scheduled_with_reviewers(
                    content_id=str(scheduled_content.id),
                    organization_id=organisation_id,
                    user_id=user_id,
                    scheduled_by={"id": user_id, "email": token.get('email')},
                    content=request.content,
                    platform=",".join(request.platforms) if request.platforms else "",
                    scheduled_time=str(request.post_time),
                    reviewers=request.reviewer_ids,
                )
            except Exception as e:
                logger.error(
                    f"Failed to notify reviewer {reviewer_id} for content {scheduled_content.id}: {str(e)}"
                )

            if hasattr(token, "get"):
                data = token.get("user_details", {})
                first_name = data.get("first_name", '')
                last_name = data.get("last_name", '')
                scheduler_name = f"{first_name.strip()} {last_name.strip()}"

            content_link = f"{settings.FRONTEND_BASE_URL}/content/scheduled/{scheduled_content.id}"

            for reviewer_id in request.reviewer_ids:
                # Send personalized email notification to reviewers
                try:
                    reviewer = await get_user_details(
                        user_id=reviewer_id,
                        org_id=organisation_id
                    )

                    reviewer_email = reviewer["email"]
                    reviewer_name = f"{reviewer.get('first_name', '')} {reviewer.get('last_name', '')}".strip() or reviewer_email
                    await EmailService.send(
                        title="Content Scheduled for Review",
                        template_name="content_scheduled_notification.html",
                        recipients=[reviewer_email],
                        background_task=background_tasks,
                        template_data={
                            "scheduler_name": "You" if reviewer_id == user_id else scheduler_name,
                            "reviewer_name": reviewer_name,
                            "platform": request.platforms[0] if request.platforms else "",
                            "content_title": request.content[:10] + ("..." if len(request.content) > 50 else ""),
                            "content_link": content_link,
                            "is_self": reviewer_id == user_id,
                        },
                    )
                except httpx.HTTPStatusError as e:
                    logger.error(f'error: {e}')
                    r = e.response.json()
                    raise HTTPException(status_code=e.response.status_code, detail=r.get("detail"))
                except Exception as e:
                    logger.error(f"Failed to send email to reviewer {reviewer_name}: {str(e)}")

            logger.info(
                f"Scheduled content created with ID {scheduled_content.id} by user {token.get('email')} with role {user_role}"
            )

        return success_response(201, "Content scheduled successfully")

    except httpx.HTTPStatusError as e:
        logger.error(f'error: {e}')
        r = e.response.json()
        raise HTTPException(status_code=e.response.status_code, detail=r.get("detail"))
    except HTTPException as e:
        await db_session.rollback()
        logger.error(f"HTTP error during content scheduling: {str(e)}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        await db_session.rollback()
        logger.error(f"Error scheduling content: {str(e)}")
        return fail_response(500, "An error occurred while scheduling content.")


@router.get(
    "/view-content", response_model=List[CalendarViewResponse], tags=["Schedule"]
)
async def get_calendar_view(
    _: Annotated[str, Depends(get_current_user)],
    organisation_id: Annotated[str, Depends(verify_organization)],
    approval_status: Optional[ApprovalStatus] = Query(None),
    schedule_status: Optional[ScheduleContentStatus] = Query(None),
    user_role: Optional[str] = Query(None),
    post_time: Optional[datetime] = Query(None),
    session: AsyncSession = Depends(get_db),
):
    try:
        logger.info(f"Fetching calendar view for organisation_id: {organisation_id}")

        # Query all scheduled posts for a specific organization
        query = select(ScheduledContent).where(
            ScheduledContent.organization_id == organisation_id
        )

        if approval_status:
            query = query.where(ScheduledContent.approval_status == approval_status)
        if schedule_status:
            query = query.where(ScheduledContent.status == schedule_status)
        if user_role:
            query = query.where(ScheduledContent.user_role == user_role)
        if post_time:
            query = query.where(ScheduledContent.post_time == post_time)

        scheduled_posts = await session.execute(query)

        scheduled_content_list = scheduled_posts.scalars().all()
        logger.info(
            f"Successfully retrieved calendar view for organisation_id: {organisation_id}"
        )
        data = [
            CalendarViewResponse(
                    id=content.id,
                    platform=content.platforms,
                    post_time=content.post_time,
                    content=content.content,
                    status=content.status,
                    media_links=content.media_links,
                    media_type=content.media_type,
                    approval_status=content.approval_status,
                    approved_by=content.approved_by,
                    reviewer_ids=content.reviewer_ids,
                    user_id=content.user_id,
                    user_role=content.user_role,
                ).model_dump()
            for content in scheduled_content_list
        ]

        return success_response(
            status_code=200,
            message="Calendar view retrieved successfully",
            data=data
        )
    except HTTPException as e:
        logger.error(f"HTTP error during calendar view retrieval: {str(e)}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"An error occurred while fetching calendar view: {str(e)}")
        return fail_response(500, "An unexpected error occurred")


@router.get(
    "/view-content/{scheduled_content_id}", response_model=ContentView, tags=["Schedule"])
async def get_single_content_view(
    _: Annotated[str, Depends(get_current_user)],
    organisation_id: Annotated[str, Depends(verify_organization)],
    scheduled_content_id: str,
    session: AsyncSession = Depends(get_db),
):
    try:
        logger.info(f"Fetching scheduled content with metrics for organisation_id: {organisation_id}, content_id: {scheduled_content_id}")

        # Query all scheduled posts for a specific organization
        query = select(ScheduledContent).where(
            ScheduledContent.organization_id == organisation_id,
            ScheduledContent.id == scheduled_content_id,
        )

        scheduled_posts = await session.execute(query)
        content = scheduled_posts.scalars().first()

        if not content:
            logger.warning(f"Scheduled content not found for ID: {scheduled_content_id}")
            raise HTTPException(status_code=404, detail="Scheduled content not found")

        logger.info(
            f"Successfully retrieved scheduled view for organisation_id: {organisation_id}"
        )
        #  if content is published, get metrics of the contents

        response_data = {
            "content": content
        }
        if content.status == ScheduleContentStatus.published:
            # get the metrics
            result = await session.execute(
                select(MediaMetrics)
                .where(
                    MediaMetrics.scheduled_content_id == scheduled_content_id
                )
            )
            metrics = result.scalars().all()
            response_data["post_metrics"] = metrics
        return response_data
    except HTTPException as e:
        logger.error(f"HTTP error during calendar view retrieval: {str(e)}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"An error occurred while fetching calendar view: {str(e)}")
        return fail_response(500, "An unexpected error occurred")


@router.post("/approve-content/{content_id}", tags=["Schedule"], response_model=ResponsePayload)
async def approve_content(
    content_id: str,
    token: Annotated[dict, Depends(get_current_user)],
    _: Annotated[str, Depends(verify_organization)],
    session: AsyncSession = Depends(get_db),
):
    user_id = token.get("user_id")

    try:
        logger.info(f"Approving content with ID: {content_id} by user_id: {user_id}")

        # Fetch the scheduled content
        scheduled_content = await session.get(ScheduledContent, content_id)
        if not scheduled_content:
            logger.warning(f"Scheduled content not found for ID: {content_id}")
            raise HTTPException(404, "Scheduled content not found")

        # Verify reviewer
        if user_id not in scheduled_content.reviewer_ids:
            logger.warning(
                f"User {user_id} unauthorized to approve content ID: {content_id}"
            )
            raise HTTPException(403, "Unauthorized to approve this content")

        # Update approval status
        scheduled_content.approval_status = ApprovalStatus.approved
        scheduled_content.approved_by = user_id
        await session.commit()

        logger.info(f"Content with ID: {content_id} approved by user_id: {user_id}")
        return success_response(
            200, "Content approved successfully", {"content_id": content_id}
        )

    except HTTPException as e:
        logger.error(f"HTTP error during content approval: {str(e)}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        await session.rollback()
        logger.error(f"Error approving content: {str(e)}")
        return fail_response(500, "An error occurred while approving content.")


@router.delete("/cancel-content/{content_id}", tags=["Schedule"], response_model=ResponsePayload)
async def cancel_content(
    content_id: str,
    token: Annotated[dict, Depends(get_current_user)],
    organization_id: Annotated[str, Depends(verify_organization)],
    session: AsyncSession = Depends(get_db),
):
    user_id = token.get("user_id")

    try:
        logger.info(f"Canceling content with ID: {content_id} by user_id: {user_id}")

        # Verify permission
        _ = await check_permissions(
            user_id, organization_id, "can approve content")

        # Fetch the scheduled content
        scheduled_content = await session.get(ScheduledContent, content_id)
        if not scheduled_content:
            logger.warning(f"Scheduled content not found for ID: {content_id}")
            raise HTTPException(404, "Scheduled content not found")

        # confirm the content hasn't reached it's post time
        now = (
            datetime.now(scheduled_content.post_time.tzinfo)
            if scheduled_content.post_time.tzinfo
            else datetime.now()
        )
        if (scheduled_content.post_time == now or scheduled_content.post_time < now) and scheduled_content.status == ScheduleContentStatus.published:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Content has already been published"
            )

        # Update approval status
        scheduled_content.status = ScheduleContentStatus.cancelled
        scheduled_content.approved_by = user_id
        await session.commit()

        logger.info(f"Content with ID: {content_id} cancelled by user_id: {user_id}")
        return success_response(
            200, "Content cancelled successfully", {"content_id": content_id}
        )

    except HTTPException as e:
        logger.error(f"HTTP error during content approval: {str(e)}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        await session.rollback()
        logger.error(f"Error canceling content: {str(e)}")
        return fail_response(500, "An error occurred while cancelling content.")


@router.put("/edit-scheduled-content/{scheduled_content_id}", tags=["Schedule"], response_model=ResponsePayload)
async def edit_scheduled_content(
    token: Annotated[dict, Depends(get_current_user)],
    scheduled_content_id: str,
    request: UpdateScheduleContentRequest,
    organisation_id: Annotated[str, Depends(verify_organization)],
    background_tasks: BackgroundTasks,
    session: AsyncSession = Depends(get_db),
):
    user_id = token.get("user_id")

    try:
        logger.info(
            f"{token.get('email')} is trying to edit scheduled content with ID: {scheduled_content_id}"
        )

        # get the user role and check the permissions
        await check_permissions(user_id, organisation_id, "can schedule content")

        # Fetch the scheduled content
        scheduled_content = await session.get(ScheduledContent, scheduled_content_id)
        if not scheduled_content:
            logger.warning(
                f"Scheduled content not found for ID: {scheduled_content_id}"
            )
            raise HTTPException(404, "Scheduled content not found")

        # Verify ownership and organization
        if scheduled_content.organization_id != organisation_id:
            logger.warning(
                f"Unauthorized edit attempt by user_id: {token.get('email')} for content ID: {scheduled_content_id}"
            )
            raise HTTPException(403, "Unauthorized to edit this scheduled content")
        # ensure the content hasn't been published

        if scheduled_content.status == "published":
            raise HTTPException(403, "This content has already been published")

        # Update fields
        if request.platforms:
            if Platform.instagram in request.platforms:
                # check for required params
                if not all([scheduled_content.media_type, scheduled_content.media_links]):
                    raise HTTPException(
                        status_code=400,
                        detail="Instagram post requires media_type and media_links"
                    )
            scheduled_content.platforms = request.platforms
        now = (
                datetime.now(request.post_time.tzinfo)
                if request.post_time
                else datetime.now()
        )
        if request.post_time:
            # confirm the post time is not in the past
            if request.post_time < now:
                raise HTTPException(
                    status_code=400, detail="post time cannot be in the past"
                )
            scheduled_content.post_time = request.post_time
        if request.content:
            scheduled_content.content = request.content
        if request.media_links:
            scheduled_content.media_links = request.media_links
        if request.status:
            if scheduled_content.post_time.replace(tzinfo=timezone.utc) < datetime.now(timezone.utc):
                raise HTTPException(
                    status_code=400, detail="post time cannot be in the past"
                )
            scheduled_content.status = request.status

        if request.reviewer_ids:
            old_reviewer_ids = scheduled_content.reviewer_ids
            scheduled_content.reviewer_ids = request.reviewer_ids

        try:
            await publish_content_updated_with_reviewers(
                content_id=str(scheduled_content.id),
                organization_id=organisation_id,
                updated_by={"id": user_id, "email": token.get('email')},
                user_id=user_id,
                content=scheduled_content.content,
                platform=scheduled_content.platforms,
                scheduled_time=str(scheduled_content.post_time),
                scheduled_status=str(scheduled_content.status),
                reviewers=scheduled_content.reviewer_ids,
            )
        except Exception as e:
            logger.error(
                f"Failed to publish notification for {scheduled_content.id}: {str(e)}"
            )

        # Alert reviewers of the request
        if request.status == "scheduled":
            if hasattr(token, "get"):
                data = token.get("user_details", {})
                first_name = data.get("first_name", '')
                last_name = data.get("last_name", '')
                scheduler_name = f"{first_name.strip()} {last_name.strip()}"

            content_link = f"{settings.FRONTEND_BASE_URL}/content/scheduled/{scheduled_content.id}"

            for reviewer_id in scheduled_content.reviewer_ids:
                # Send personalized email notification to reviewers
                try:
                    reviewer = await get_user_details(
                        user_id=reviewer_id,
                        org_id=organisation_id
                    )

                    reviewer_email = reviewer["email"]
                    reviewer_name = f"{reviewer.get('first_name', '')} {reviewer.get('last_name', '')}".strip() or reviewer_email

                    await EmailService.send(
                        title="Content Updated for Review",
                        template_name="content_edited_notification.html",
                        recipients=[reviewer_email],
                        background_task=background_tasks,
                        template_data={
                            "scheduler_name": scheduler_name,
                            "reviewer_name": reviewer_name,
                            "content_title": scheduled_content.content[:100] + ("..." if len(scheduled_content.content) > 100 else ""),
                            "content_link": content_link,
                            "platform": scheduled_content.platforms[0] if scheduled_content.platforms else "",
                            "is_self": reviewer_id == user_id,
                        },
                    )

                except httpx.HTTPStatusError as e:
                    logger.error(f'error: {e}')
                    r = e.response.json()
                    raise HTTPException(status_code=e.response.status_code, detail=r.get("detail"))
                except Exception as e:
                    logger.error(f"Failed to send email to reviewer {reviewer_name}: {str(e)}")

        # Commit changes
        await session.commit()
        # send emails to reviewers

        logger.info(
            f"Scheduled content with ID: {scheduled_content_id} updated successfully by user: {token.get('email')}"
        )

        return success_response(
            200,
            "Scheduled content updated successfully",
            {"content_id": scheduled_content_id},)

    except HTTPException as e:
        logger.error(f"HTTP error during scheduled content editing: {str(e)}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        await session.rollback()
        logger.error(f"Error editing scheduled content: {str(e)}")
        return fail_response(500, "An error occurred while editing scheduled content.")


@router.delete("/delete-content/{content_id}", tags=["Schedule"])
async def delete_scheduled_content(
    content_id: str,
    token: Annotated[dict, Depends(get_current_user)],
    organisation_id: Annotated[str, Depends(verify_organization)],
    session: AsyncSession = Depends(get_db),
):
    user_id = token.get("user_id")

    try:
        logger.info(
            f"Attempting to delete content with ID: {content_id} by user_id: {user_id}"
        )

        await check_permissions(user_id, organisation_id, "can delete content")

        # Find the scheduled content
        scheduled_content = await session.get(ScheduledContent, content_id)

        if (
            not scheduled_content
            or scheduled_content.organization_id != organisation_id
        ):
            logger.warning(
                f"Content not found or unauthorized access for content ID: {content_id}"
            )
            raise HTTPException(404, "Content not found")

        # Delete the content
        await session.run_sync(lambda s: s.delete(scheduled_content))
        await session.commit()

        logger.info(
            f"Content with ID: {content_id} deleted successfully by user_id: {user_id}"
        )
        return success_response(200, "Content deleted successfully")

    except HTTPException as e:
        logger.error(f"HTTP error during content deletion: {str(e)}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        await session.rollback()
        logger.error(f"Error deleting content: {str(e)}")
        return fail_response(500, "An error occurred while deleting content.")


@router.get("/comments")
async def get_combined_comments(
    _: Annotated[str, Depends(get_current_user)],
    organisation_id: Annotated[str, Depends(verify_organization)],
    scheduled_content_id: str,
    db_session: AsyncSession = Depends(get_db),
):
    """
    Retrieve a combined comments from both Twitter and Facebook.
    """
    try:
        logger.info(
            f"Fetching combined comments for organisation_id: {organisation_id}"
        )

        combined_comments = []

        # Get overview from Twitter
        try:
            twitter_dict = {}
            twitter_response = await twitter_get_comments(
                organisation_id=organisation_id, db_session=db_session, scheduled_content_id=scheduled_content_id
            )
            if twitter_response.status_code != 200:
                logger.error(
                    f"Error fetching Twitter comments: {twitter_response.status_code} - {twitter_response.body}"
                )
                twitter_data = {}
            else:
                twitter_data = json.loads(twitter_response.body)
            if isinstance(twitter_data, dict):
                twitter_comments = twitter_data.get("data", [])
                twitter_dict["platform"] = "twitter"
                twitter_dict["comments"] = twitter_comments
                combined_comments.append(twitter_dict)
            else:
                logger.warning("Invalid data format from twitter comments")
        except Exception as e:
            logger.error(f"Twitter comments failed to fetch: {str(e)}")

        # Get comments from Facebook
        try:
            facebook_dict = {}
            facebook_response = await facebook_get_comments(
                organisation_id=organisation_id,
                db_session=db_session,
                scheduled_content_id=scheduled_content_id
            )
            if facebook_response.status_code != 200:
                logger.error(
                    f"Error fetching Facebook comments: {facebook_response.status_code} - {facebook_response.body}"
                )
                facebook_data = {}
            else:
                logger.info("loading the facebook response")
                facebook_data = json.loads(facebook_response.body)
            if isinstance(facebook_data, dict):
                logger.info(f"formatting the data: {facebook_data}")
                facebook_comments = facebook_data.get("data", [])
                facebook_dict["platform"] = "facebook"
                facebook_dict["comments"] = facebook_comments
                combined_comments.append(facebook_dict)
            else:
                logger.warning("Invalid data format from Facebook comments")
        except Exception as e:
            logger.error(f"Facebook comments failed to fetch: {str(e)}")

        if not combined_comments:
            logger.error("Failed to retrieve comments from both platforms.")
            return fail_response(
                404, "Failed to retrieve comments from both platforms."
            )

        logger.info(
            f"Successfully retrieved combined comments for organisation_id: {organisation_id}"
        )
        return success_response(
            200, "Combined comments retrieved successfully.", combined_comments
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"An error occurred while fetching combined comments: {str(e)}")
        return fail_response(500, "An unexpected error occurred")


@router.get("/messages")
async def get_combined_messages(
    _: Annotated[str, Depends(get_current_user)],
    organisation_id: Annotated[str, Depends(verify_organization)],
    participant_id: str = '',
    conversation_id: str = '',
    db_session: AsyncSession = Depends(get_db),
):
    """
    Retrieve a combined messages from both Twitter and Facebook.
    Send only one of participant_id or conversation_id or None
    """
    try:
        logger.info(
            f"Fetching combined messages for organisation_id: {organisation_id}"
        )

        combined_messages = []

        # Get overview from Twitter
        try:
            twitter_dict = {}
            twitter_response = await get_direct_messages(
                organisation_id=organisation_id,
                db_session=db_session,
                participant_id=participant_id if participant_id else None,
                conversation_id=conversation_id if conversation_id else None,
            )
            if twitter_response.status_code != 200:
                logger.error(
                    f"Error fetching Twitter messages: {twitter_response.status_code} - {twitter_response.body}"
                )
                twitter_data = {}
            else:
                twitter_data = json.loads(twitter_response.body)
            if isinstance(twitter_data, dict):
                twitter_messages= twitter_data.get("data", [])
                twitter_dict["platform"] = "twitter"
                twitter_dict["messages"] = twitter_messages
                combined_messages.append(twitter_dict)
            else:
                logger.warning("Invalid data format from twitter messages")
        except Exception as e:
            logger.error(f"Twitter messages failed to fetch: {str(e)}")

        # Get messages from Facebook
        try:
            facebook_dict = {}
            facebook_response = await get_convo(
                organisation_id=organisation_id,
                db_session=db_session
            )
            if facebook_response.status_code != 200:
                logger.error(
                    f"Error fetching Facebook messages: {facebook_response.status_code} - {facebook_response.body}"
                )
                facebook_data = {}
            else:
                logger.info("loading the facebook response")
                facebook_data = json.loads(facebook_response.body)
            if isinstance(facebook_data, dict):
                logger.info(f"formatting the data: {facebook_data}")
                facebook_messages = facebook_data.get("data", [])
                facebook_dict["platform"] = "facebook"
                facebook_dict["messages"] = facebook_messages
                combined_messages.append(facebook_dict)
            else:
                logger.warning("Invalid data format from Facebook messages")
        except Exception as e:
            logger.error(f"Facebook messages failed to fetch: {str(e)}")

        if not combined_messages:
            logger.error("Failed to retrieve messages from both platforms.")
            return fail_response(
                404, "Failed to retrieve messages from both platforms."
            )

        logger.info(
            f"Successfully retrieved combined messages for organisation_id: {organisation_id}"
        )
        return success_response(
            200, "Combined messages retrieved successfully.", combined_messages
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"An error occurred while fetching combined messages: {str(e)}")
        return fail_response(500, "An unexpected error occurred")


@router.get(
    "/view-published-content", response_model=List[ContentView], tags=["Schedule"])
async def get_calendar_view_for_published_content(
    _: Annotated[str, Depends(get_current_user)],
    organisation_id: Annotated[str, Depends(verify_organization)],
    user_role: Optional[str] = Query(None),
    post_time: Optional[datetime] = Query(None),
    session: AsyncSession = Depends(get_db),
):
    try:
        logger.info(f"Fetching published calendar view for organisation_id: {organisation_id}")

        # Query all scheduled posts for a specific organization
        query = select(ScheduledContent).where(
            ScheduledContent.organization_id == organisation_id,
            ScheduledContent.status == ScheduleContentStatus.published
        )

        if user_role:
            query = query.where(ScheduledContent.user_role == user_role)
        if post_time:
            query = query.where(ScheduledContent.post_time == post_time)

        scheduled_posts = await session.execute(query)
        scheduled_content_list = scheduled_posts.scalars().all()

        logger.info(
            f"Successfully retrieved {len(scheduled_content_list)} published contents for organisation_id: {organisation_id}"
        )

        # loop through the contents to get their respective metrics
        results = []
        for content in scheduled_content_list:
            result = await session.execute(
                select(MediaMetrics)
                .where(
                    MediaMetrics.scheduled_content_id == content.id
                )
            )
            media_metrics = result.scalars().all()

            response_data = {
                "content": content,
                "post_metrics": media_metrics,
            }

            results.append(response_data)

        return results
    except HTTPException as e:
        logger.error(f"HTTP error during published contents retrieval: {str(e)}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"An error occurred while fetching published contents: {str(e)}")
        return fail_response(500, "An unexpected error occurred")


@router.get("/combined-metrics")
async def get_combined_metrics(
    _: Annotated[str, Depends(get_current_user)],
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db),
):
    """
    Retrieve combined metrics (followers, impressions, engagements, reach) from all social media platforms.
    This endpoint aggregates data from different platforms and returns a single set of metrics.
    """
    try:
        logger.info(f"Fetching combined metrics for organisation_id: {organisation_id}")

        # Initialize metrics with zeros
        combined_metrics = {
            "followers": 0,
            "impressions": 0,
            "engagements": 0,
            "reach": 0,
            "updated_at": datetime.now().isoformat()
        }

        # Get Twitter metrics directly from the database using raw SQL
        try:
            # Query Twitter metrics for the specific organisation
            twitter_result = await db_session.execute(
                text("""
                SELECT tm.* FROM twitter_metrics tm
                JOIN social_media_accounts sma ON tm.social_media_account_id = sma.id
                WHERE sma.organisation_id = :organisation_id
                ORDER BY tm.updated_at DESC LIMIT 1
                """),
                {"organisation_id": organisation_id}
            )
            twitter_metrics = twitter_result.fetchone()

            if twitter_metrics:
                combined_metrics["followers"] += twitter_metrics.followers_count
                combined_metrics["impressions"] += twitter_metrics.impressions
                # Calculate Twitter engagement properly using our utility function
                # Extract engagement data from raw_data if available
                likes = None
                retweets = None
                replies = None

                if hasattr(twitter_metrics, 'raw_data') and twitter_metrics.raw_data and 'engagement_metrics' in twitter_metrics.raw_data:
                    engagement_metrics = twitter_metrics.raw_data['engagement_metrics']
                    # Use average metrics multiplied by tweet count to estimate total engagements
                    # This is a rough estimate but better than using the unreliable total_engagement value
                    tweet_count = twitter_metrics.tweet_count if hasattr(twitter_metrics, 'tweet_count') else 0
                    likes = int(engagement_metrics.get('avg_likes', 0) * tweet_count)
                    # Use a reasonable value for retweets instead of the unreliable avg_retweets
                    retweets = int(0.1 * tweet_count)  # Assume 10% of tweets get retweeted
                    replies = int(engagement_metrics.get('avg_replies', 0) * tweet_count)

                twitter_engagement = calculate_twitter_engagement(
                    impressions=twitter_metrics.impressions,
                    followers_count=twitter_metrics.followers_count,
                    likes=likes,
                    retweets=retweets,
                    replies=replies
                )
                combined_metrics["engagements"] += twitter_engagement["total_engagement"]
                # For reach, we don't have a direct column, so we'll estimate it as a percentage of impressions
                combined_metrics["reach"] += int(twitter_metrics.impressions * 0.8)  # Estimate reach as 80% of impressions
                logger.info(f"Twitter metrics retrieved from database: {twitter_metrics.updated_at}")
            else:
                logger.info(f"No Twitter metrics found for organisation_id: {organisation_id}")
        except Exception as e:
            logger.error(f"Error fetching Twitter metrics from database: {str(e)}")

        # Get Facebook metrics directly from the database
        try:
            # Query Facebook metrics for the specific organisation
            facebook_result = await db_session.execute(
                text("""
                SELECT * FROM facebook_page_metrics
                WHERE organisation_id = :organisation_id
                ORDER BY collected_at DESC LIMIT 1
                """),
                {"organisation_id": organisation_id}
            )
            facebook_metrics = facebook_result.fetchone()

            if facebook_metrics:
                combined_metrics["followers"] += facebook_metrics.total_followers
                combined_metrics["impressions"] += facebook_metrics.total_impressions
                combined_metrics["engagements"] += facebook_metrics.total_engagements
                combined_metrics["reach"] += facebook_metrics.total_reach
                logger.info(f"Facebook metrics retrieved from database: {facebook_metrics.collected_at}")
            else:
                logger.info(f"No Facebook metrics found for organisation_id: {organisation_id}")
        except Exception as e:
            logger.error(f"Error fetching Facebook metrics from database: {str(e)}")

        logger.info(f"Successfully retrieved combined metrics for organisation_id: {organisation_id}")
        return success_response(
            200, "Combined metrics retrieved successfully", combined_metrics
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"An error occurred while fetching combined metrics: {str(e)}")
        return fail_response(500, "An unexpected error occurred")


@router.get("/twitter-metrics-history")
async def get_twitter_metrics_history(
    _: Annotated[str, Depends(get_current_user)],
    organisation_id: Annotated[str, Depends(verify_organization)],
    months: int = Query(6, description="Number of months to include in the history (default: 6 months)"),
    db_session: AsyncSession = Depends(get_db),
):
    """
    Retrieve historical Twitter metrics data for time series graphing.

    This endpoint returns all metrics stored in the twitter_metrics table over time,
    which can be used to plot trends on the frontend.

    Parameters:
    - organisation_id: Organisation ID to fetch metrics for
    - months: Number of months to include in the history (default: 6 months)

    Returns:
    - A time series of Twitter metrics suitable for graphing on the frontend
    """
    try:
        logger.info(f"Fetching Twitter metrics history for organisation_id: {organisation_id}")

        # Calculate the start date based on the number of months
        end_date = datetime.now()
        start_date = end_date - relativedelta(months=months)

        # Query Twitter metrics data from the database
        try:
            twitter_result = await db_session.execute(
                text("""
                SELECT tm.* FROM twitter_metrics tm
                JOIN social_media_accounts sma ON tm.social_media_account_id = sma.id
                WHERE sma.organisation_id = :organisation_id
                AND tm.created_at >= :start_date
                ORDER BY tm.created_at
                """),
                {
                    "organisation_id": organisation_id,
                    "start_date": start_date
                }
            )
            twitter_metrics = twitter_result.fetchall()

            if not twitter_metrics:
                logger.info(f"No Twitter metrics history found for organisation_id: {organisation_id}")
                return success_response(
                    200,
                    "No Twitter metrics history found",
                    {"data": []}
                )

            # Format the metrics data for time series visualization
            metrics_data = []
            for metric in twitter_metrics:
                # Extract engagement data from raw_data if available
                engagements = 0
                if hasattr(metric, 'raw_data') and metric.raw_data and 'engagement_metrics' in metric.raw_data:
                    engagement_metrics = metric.raw_data['engagement_metrics']
                    if 'total_engagement' in engagement_metrics:
                        engagements = engagement_metrics['total_engagement']

                metrics_data.append({
                    "date": metric.created_at.isoformat(),
                    "followers": metric.followers_count,
                    "impressions": metric.impressions,
                    "engagements": engagements,
                    "reach": int(metric.impressions * 0.8)  # Estimate reach as 80% of impressions
                })

            result = {
                "platform": "twitter",
                "data": metrics_data
            }

            logger.info(f"Successfully retrieved Twitter metrics history for organisation_id: {organisation_id}")
            return success_response(
                200, "Twitter metrics history retrieved successfully", result
            )
        except Exception as e:
            logger.error(f"Error fetching Twitter metrics history from database: {str(e)}")
            return fail_response(500, f"Error fetching Twitter metrics history: {str(e)}")

    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"An error occurred while fetching Twitter metrics history: {str(e)}")
        return fail_response(500, "An unexpected error occurred")


@router.get("/facebook-metrics-history")
async def get_facebook_metrics_history(
    _: Annotated[str, Depends(get_current_user)],
    organisation_id: Annotated[str, Depends(verify_organization)],
    months: int = Query(6, description="Number of months to include in the history (default: 6 months)"),
    db_session: AsyncSession = Depends(get_db),
):
    """
    Retrieve historical Facebook metrics data for time series graphing.

    This endpoint returns all metrics stored in the facebook_page_metrics table over time,
    which can be used to plot trends on the frontend.

    Parameters:
    - organisation_id: Organisation ID to fetch metrics for
    - months: Number of months to include in the history (default: 6 months)

    Returns:
    - A time series of Facebook metrics suitable for graphing on the frontend
    """
    try:
        logger.info(f"Fetching Facebook metrics history for organisation_id: {organisation_id}")

        # Calculate the start date based on the number of months
        end_date = datetime.now()
        start_date = end_date - relativedelta(months=months)

        # Query Facebook metrics data from the database
        try:
            facebook_result = await db_session.execute(
                text("""
                SELECT * FROM facebook_page_metrics
                WHERE organisation_id = :organisation_id
                AND collected_at >= :start_date
                ORDER BY collected_at
                """),
                {
                    "organisation_id": organisation_id,
                    "start_date": start_date
                }
            )
            facebook_metrics = facebook_result.fetchall()

            if not facebook_metrics:
                logger.info(f"No Facebook metrics history found for organisation_id: {organisation_id}")
                return success_response(
                    200,
                    "No Facebook metrics history found",
                    {"data": []}
                )

            # Format the metrics data for time series visualization
            metrics_data = []
            for metric in facebook_metrics:
                metrics_data.append({
                    "date": metric.collected_at.isoformat(),
                    "followers": metric.total_followers,
                    "impressions": metric.total_impressions,
                    "engagements": metric.total_engagements,
                    "reach": metric.total_reach,
                    "engagement_rate": metric.engagement_rate
                })

            result = {
                "platform": "facebook",
                "data": metrics_data
            }

            logger.info(f"Successfully retrieved Facebook metrics history for organisation_id: {organisation_id}")
            return success_response(
                200, "Facebook metrics history retrieved successfully", result
            )
        except Exception as e:
            logger.error(f"Error fetching Facebook metrics history from database: {str(e)}")
            return fail_response(500, f"Error fetching Facebook metrics history: {str(e)}")

    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"An error occurred while fetching Facebook metrics history: {str(e)}")
        return fail_response(500, "An unexpected error occurred")


@router.get("/combined-metrics-history")
async def get_combined_metrics_history(
    _: Annotated[str, Depends(get_current_user)],
    organisation_id: Annotated[str, Depends(verify_organization)],
    months: int = Query(6, description="Number of months to include in the history (default: 6 months)"),
    db_session: AsyncSession = Depends(get_db),
):
    """
    Retrieve combined historical metrics data from all platforms for time series graphing.

    This endpoint returns metrics from both Twitter and Facebook over time,
    which can be used to plot combined trends on the frontend.

    Parameters:
    - organisation_id: Organisation ID to fetch metrics for
    - months: Number of months to include in the history (default: 6 months)

    Returns:
    - A time series of combined metrics suitable for graphing on the frontend
    """
    try:
        logger.info(f"Fetching combined metrics history for organisation_id: {organisation_id}")

        # Calculate the start date based on the number of months
        end_date = datetime.now()
        start_date = end_date - relativedelta(months=months)

        result = {
            "platforms": [],
            "combined_data": []
        }

        # Get Twitter metrics history
        try:
            twitter_result = await db_session.execute(
                text("""
                SELECT tm.* FROM twitter_metrics tm
                JOIN social_media_accounts sma ON tm.social_media_account_id = sma.id
                WHERE sma.organisation_id = :organisation_id
                AND tm.created_at >= :start_date
                ORDER BY tm.created_at
                """),
                {
                    "organisation_id": organisation_id,
                    "start_date": start_date
                }
            )
            twitter_metrics = twitter_result.fetchall()

            if twitter_metrics:
                # Format Twitter data
                twitter_data = []
                for metric in twitter_metrics:
                    # Extract engagement data from raw_data if available
                    engagements = 0
                    if hasattr(metric, 'raw_data') and metric.raw_data and 'engagement_metrics' in metric.raw_data:
                        engagement_metrics = metric.raw_data['engagement_metrics']
                        if 'total_engagement' in engagement_metrics:
                            engagements = engagement_metrics['total_engagement']

                    twitter_data.append({
                        "date": metric.created_at.isoformat(),
                        "followers": metric.followers_count,
                        "impressions": metric.impressions,
                        "engagements": engagements,
                        "reach": int(metric.impressions * 0.8)  # Estimate reach as 80% of impressions
                    })

                result["platforms"].append({
                    "platform": "twitter",
                    "data": twitter_data
                })
                logger.info(f"Twitter metrics history retrieved: {len(twitter_data)} data points")
            else:
                logger.info(f"No Twitter metrics history found for organisation_id: {organisation_id}")
        except Exception as e:
            logger.error(f"Error fetching Twitter metrics history from database: {str(e)}")

        # Get Facebook metrics history
        try:
            facebook_result = await db_session.execute(
                text("""
                SELECT * FROM facebook_page_metrics
                WHERE organisation_id = :organisation_id
                AND collected_at >= :start_date
                ORDER BY collected_at
                """),
                {
                    "organisation_id": organisation_id,
                    "start_date": start_date
                }
            )
            facebook_metrics = facebook_result.fetchall()

            if facebook_metrics:
                # Format Facebook data
                facebook_data = []
                for metric in facebook_metrics:
                    facebook_data.append({
                        "date": metric.collected_at.isoformat(),
                        "followers": metric.total_followers,
                        "impressions": metric.total_impressions,
                        "engagements": metric.total_engagements,
                        "reach": metric.total_reach
                    })

                result["platforms"].append({
                    "platform": "facebook",
                    "data": facebook_data
                })
                logger.info(f"Facebook metrics history retrieved: {len(facebook_data)} data points")
            else:
                logger.info(f"No Facebook metrics history found for organisation_id: {organisation_id}")
        except Exception as e:
            logger.error(f"Error fetching Facebook metrics history from database: {str(e)}")

        # Combine data from all platforms by date
        all_dates = set()
        for platform in result["platforms"]:
            for item in platform["data"]:
                all_dates.add(item["date"][:10])  # Use just the date part (YYYY-MM-DD)

        all_dates = sorted(list(all_dates))

        for date in all_dates:
            combined_metrics = {
                "date": date,
                "followers": 0,
                "impressions": 0,
                "engagements": 0,
                "reach": 0
            }

            # Sum values from all platforms for this date
            for platform in result["platforms"]:
                for item in platform["data"]:
                    if item["date"][:10] == date:  # Match on date part
                        combined_metrics["followers"] += item["followers"]
                        combined_metrics["impressions"] += item["impressions"]
                        combined_metrics["engagements"] += item["engagements"]
                        combined_metrics["reach"] += item["reach"]

            result["combined_data"].append(combined_metrics)

        logger.info(f"Successfully retrieved combined metrics history for organisation_id: {organisation_id}")
        return success_response(
            200, "Combined metrics history retrieved successfully", result
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"An error occurred while fetching combined metrics history: {str(e)}")
        return fail_response(500, "An unexpected error occurred")


@router.get("/post-analytics")
async def get_post_analytics(
    _: Annotated[str, Depends(get_current_user)],
    organisation_id: Annotated[str, Depends(verify_organization)],
    months: int = Query(6, description="Number of months to include in the trend (default: 6 months)"),
    db_session: AsyncSession = Depends(get_db),
):
    """
    Retrieve post engagement analytics data for graphing, combining data from all social media platforms.

    Parameters:
    - organisation_id: Organisation ID to fetch metrics for
    - months: Number of months to include in the trend (default: 6 months)

    Returns combined post engagement data suitable for graphing on the frontend.
    """
    try:
        logger.info(f"Fetching post engagement analytics for organisation_id: {organisation_id}")

        # Initialize result structure
        result = {
            "metric_type": "engagement",
            "data": [],
            "platforms": []
        }

        # Calculate the start date based on the number of months
        end_date = datetime.now()
        start_date = end_date - relativedelta(months=months)

        # Get Twitter post engagement data directly from the database
        try:
            # Check if twitter_growth_trend table exists
            table_check = await db_session.execute(
                text("SELECT to_regclass('twitter_growth_trend')")
            )
            table_exists = table_check.scalar()

            if table_exists:
                # Query Twitter growth trend data from the database
                twitter_result = await db_session.execute(
                    text("""
                    SELECT * FROM twitter_growth_trend
                    WHERE organisation_id = :organisation_id
                    AND trend_type = 'engagement'
                    AND month >= :start_month
                    ORDER BY month
                    """),
                    {
                        "organisation_id": organisation_id,
                        "start_month": start_date.strftime("%Y-%m")
                    }
                )
                twitter_trends = twitter_result.fetchall()

                if twitter_trends:
                    # Format Twitter data
                    twitter_formatted_data = []
                    for trend in twitter_trends:
                        twitter_formatted_data.append({
                            "month": trend.month,
                            "value": trend.value,
                            "growth_percentage": trend.growth_percentage
                        })

                    result["platforms"].append({
                        "platform": "twitter",
                        "data": twitter_formatted_data
                    })
                    logger.info(f"Twitter post engagement data retrieved from database: {len(twitter_formatted_data)} months")
                else:
                    logger.info(f"No Twitter engagement data found for organisation_id: {organisation_id}")
            else:
                logger.info("Twitter growth trends table doesn't exist yet, skipping Twitter data")
        except Exception as e:
            logger.error(f"Error fetching Twitter post engagement from database: {str(e)}")

        # Get Facebook post engagement data directly from the database
        try:
            # Check if facebook_growth_trends table exists
            table_check = await db_session.execute(
                text("SELECT to_regclass('facebook_growth_trends')")
            )
            table_exists = table_check.scalar()

            if table_exists:
                # Query Facebook growth trend data for the specific organisation
                facebook_result = await db_session.execute(
                    text("""
                    SELECT * FROM facebook_growth_trends
                    WHERE organisation_id = :organisation_id
                    AND trend_type = 'engagement'
                    AND month >= :start_month
                    ORDER BY month
                    """),
                    {
                        "organisation_id": organisation_id,
                        "start_month": start_date.strftime("%Y-%m")
                    }
                )
                facebook_trends = facebook_result.fetchall()

                if facebook_trends:
                    # Format Facebook data
                    facebook_formatted_data = []
                    for trend in facebook_trends:
                        facebook_formatted_data.append({
                            "month": trend.month,
                            "value": trend.value,
                            "growth_percentage": trend.growth_percentage
                        })

                    result["platforms"].append({
                        "platform": "facebook",
                        "data": facebook_formatted_data
                    })
                    logger.info(f"Facebook post engagement data retrieved from database: {len(facebook_formatted_data)} months")
                else:
                    logger.info(f"No Facebook engagement data found for organisation_id: {organisation_id}")
            else:
                logger.info("Facebook growth trends table doesn't exist yet, skipping Facebook data")
        except Exception as e:
            logger.error(f"Error fetching Facebook post engagement from database: {str(e)}")

        # Combine data from all platforms for the combined view
        all_months = set()
        for platform in result["platforms"]:
            for item in platform["data"]:
                all_months.add(item["month"])

        all_months = sorted(list(all_months))
        combined_data = []

        for month in all_months:
            month_data = {"month": month, "value": 0, "growth_percentage": None}

            # Sum values from all platforms for this month
            for platform in result["platforms"]:
                for item in platform["data"]:
                    if item["month"] == month:
                        month_data["value"] += item["value"]

            combined_data.append(month_data)

        # Calculate growth percentages for combined data
        for i in range(1, len(combined_data)):
            current = combined_data[i]
            previous = combined_data[i-1]

            if previous["value"] == 0:
                growth = 100.0 if current["value"] > 0 else 0
            else:
                growth = ((current["value"] - previous["value"]) / previous["value"]) * 100

            current["growth_percentage"] = round(growth, 2)

        result["data"] = combined_data

        logger.info(f"Successfully retrieved post engagement analytics for organisation_id: {organisation_id}")
        return success_response(
            200, "Post engagement analytics retrieved successfully", result
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"An error occurred while fetching post engagement analytics: {str(e)}")
        return fail_response(500, "An unexpected error occurred")
