import logging
from datetime import datetime, timed<PERSON>ta
from typing import List

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app.database.session import get_db
from app.models import model
from app.schemas import schema
from app.services.analysis_service import AnalysisService
from app.services.twitter_service import TwitterService
from app.utils.dependency import get_analysis_service, get_twitter_service, rate_limiter

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/compare-twitter-competitors/", response_model=schema.TwitterComparisonResult, dependencies=[Depends(rate_limiter)])
async def compare_competitors(
    usernames: List[str] = Query(..., description="List of usernames to compare"),
    days: int = Query(30, description="Number of days to analyze"),
    twitter_service: TwitterService = Depends(get_twitter_service)
):
    """Compare multiple X profiles and their engagement metrics"""
    if not usernames:
        raise HTTPException(status_code=400, detail="No usernames provided")

    if len(usernames) > 5:
        raise HTTPException(status_code=400, detail="Maximum 5 usernames allowed for comparison")

    try:
        # Collect basic profile data
        profiles = await twitter_service.compare_profiles(usernames)

        # Collect engagement metrics for each competitor
        engagement_metrics = []
        for username in usernames:
            metrics = await twitter_service.get_engagement_metrics(username, days=days)
            engagement_metrics.append(metrics)

        return {
            "profiles": profiles,
            "engagement": engagement_metrics
        }
    except Exception as e:
        logger.error(f"Error comparing competitors: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error comparing competitors: {str(e)}")

@router.get("/twiter-follower-growth-chart/{competitor_id}", response_model=schema.ChartResponse, dependencies=[Depends(rate_limiter)])
async def generate_follower_growth_chart(
    competitor_id: str,
    days: int = Query(90, description="Number of days to include in the chart"),
    db: AsyncSession = Depends(get_db),
    analysis_service: AnalysisService = Depends(get_analysis_service)
):
    """Generate a chart showing follower growth over time"""
    # Check if competitor exists
    result = await db.execute(
        select(model.TwitterCompetitor).filter(model.TwitterCompetitor.id == competitor_id)
    )
    db_competitor = result.scalars().first()

    if db_competitor is None:
        raise HTTPException(status_code=404, detail="Competitor not found")

    # Get historical metrics within the specified date range
    cutoff_date = datetime.now() - timedelta(days=days)
    metrics_result = await db.execute(
        select(model.TwitterCompetitorMetrics)
        .filter(model.TwitterCompetitorMetrics.competitor_id == competitor_id)
        .filter(model.TwitterCompetitorMetrics.collected_at >= cutoff_date)
        .order_by(model.TwitterCompetitorMetrics.collected_at.asc())
    )
    metrics = metrics_result.scalars().all()

    if not metrics:
        raise HTTPException(status_code=404, detail="No metrics found for this competitor in the specified date range")

    # Extract dates and follower counts
    dates = [m.collected_at for m in metrics]
    followers = [m.followers_count for m in metrics]

    # Generate chart
    chart_data = analysis_service.generate_follower_growth_chart(
        dates, followers, db_competitor.username
    )

    return {
        "username": db_competitor.username,
        "chart": chart_data
    }

@router.get("/twitter-engagement-comparison-chart", response_model=schema.ChartResponse, dependencies=[Depends(rate_limiter)])
async def generate_engagement_comparison_chart(
    usernames: List[str] = Query(..., description="List of usernames to compare"),
    days: int = Query(30, description="Number of days to analyze"),
    twitter_service: TwitterService = Depends(get_twitter_service),
    analysis_service: AnalysisService = Depends(get_analysis_service)
):
    """Generate a chart comparing engagement rates between users"""
    if not usernames:
        raise HTTPException(status_code=400, detail="No usernames provided")

    if len(usernames) > 5:
        raise HTTPException(status_code=400, detail="Maximum 5 usernames allowed for comparison")

    try:
        # Collect engagement metrics for each username
        engagement_data = []
        for username in usernames:
            metrics = await twitter_service.get_engagement_metrics(username, days=days)
            engagement_data.append(metrics)

        # Generate chart
        chart_data = analysis_service.generate_engagement_comparison_chart(engagement_data)

        return {
            "username": "Comparison",
            "chart": chart_data
        }
    except Exception as e:
        logger.error(f"Error generating engagement comparison chart: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error generating chart: {str(e)}")

@router.get("/twitter-content-analysis-chart", response_model=schema.ChartResponse, dependencies=[Depends(rate_limiter)])
async def generate_content_analysis_chart(
    usernames: List[str] = Query(..., description="List of usernames to compare"),
    days: int = Query(30, description="Number of days to analyze"),
    twitter_service: TwitterService = Depends(get_twitter_service),
    analysis_service: AnalysisService = Depends(get_analysis_service)
):
    """Generate a chart showing content type distribution for multiple users"""
    if not usernames:
        raise HTTPException(status_code=400, detail="No usernames provided")

    if len(usernames) > 5:
        raise HTTPException(status_code=400, detail="Maximum 5 usernames allowed for comparison")

    try:
        # Collect content metrics for each username
        content_data = {}
        for username in usernames:
            metrics = await twitter_service.get_engagement_metrics(username, days=days)
            content_data[username] = metrics["content_metrics"]

        # Generate chart
        chart_data = analysis_service.generate_content_analysis_chart(content_data)

        return {
            "username": "Content Analysis",
            "chart": chart_data
        }
    except Exception as e:
        logger.error(f"Error generating content analysis chart: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error generating chart: {str(e)}")

@router.get("/twitter-top-hashtags/{username}", dependencies=[Depends(rate_limiter)])
async def get_top_hashtags(
    username: str,
    count: int = Query(100, description="Number of tweets to analyze"),
    top_n: int = Query(10, description="Number of top hashtags to return"),
    twitter_service: TwitterService = Depends(get_twitter_service)
):
    """Get the most frequently used hashtags by a user"""
    try:
        hashtags = await twitter_service.get_top_hashtags(username, count=count, top_n=top_n)
        return {
            "username": username,
            "tweets_analyzed": count,
            "top_hashtags": hashtags
        }
    except Exception as e:
        logger.error(f"Error getting top hashtags: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting top hashtags: {str(e)}")

@router.get("/twitter-posting-schedule/{username}", response_model=schema.ChartResponse, dependencies=[Depends(rate_limiter)])
async def get_posting_schedule(
    username: str,
    days: int = Query(30, description="Number of days to analyze"),
    twitter_service: TwitterService = Depends(get_twitter_service),
    analysis_service: AnalysisService = Depends(get_analysis_service)
):
    """Analyze and visualize when a user typically posts"""
    try:
        schedule_data = await twitter_service.get_posting_schedule(username, days=days)

        # Generate chart
        chart_data = analysis_service.generate_posting_schedule_chart(
            schedule_data, username
        )

        return {
            "username": username,
            "chart": chart_data
        }
    except Exception as e:
        logger.error(f"Error analyzing posting schedule: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error analyzing posting schedule: {str(e)}")
