from fastapi import APIRouter

from .facebook import router as facebook_router
from .general import router as general_router
from .instagram import router as instagram_router
from .linkedin import router as linkedin_router
from .twitter import router as twitter_router
from .competitors import router as twitter_competitor_router
from .analysis import router as analysis_router
from .metrics import router as metrics_router
from .competitor_metrics import router as competitor_metrics_router

router = APIRouter()

router.include_router(facebook_router, prefix="/facebook", tags=["Facebook"])
router.include_router(instagram_router, prefix="/instagram", tags=["Instagram"])
router.include_router(twitter_router, prefix="/twitter", tags=["Twitter"])
router.include_router(general_router, prefix="", tags=["General"])
router.include_router(linkedin_router, prefix="/linkedin", tags=["LinkedIn"])
router.include_router(twitter_competitor_router, prefix="/competitors", tags=['Twitter Competitors'])
router.include_router(analysis_router, prefix="/analysis", tags=['Analysis'])
router.include_router(metrics_router, prefix="/metrics", tags=['Metrics'])
router.include_router(competitor_metrics_router, prefix="/competitor-metrics", tags=['Competitor Metrics'])
