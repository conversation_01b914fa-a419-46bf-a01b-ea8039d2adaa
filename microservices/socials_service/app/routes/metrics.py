import logging
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app.database.session import get_db
from app.models import model
from app.schemas import schema
from app.services.twitter_service import TwitterService
from app.utils.dependency import get_twitter_service, rate_limiter

router = APIRouter()
logger = logging.getLogger(__name__)

@router.post("/{competitor_id}/collect-twitter-metrics", response_model=schema.TwitterCompetitorMetrics, dependencies=[Depends(rate_limiter)])
async def collect_metrics(
    competitor_id: str,
    days: Optional[int] = Query(30, description="Number of days to analyze"),
    db: AsyncSession = Depends(get_db),
    twitter_service: TwitterService = Depends(get_twitter_service)
):
    """Collect and store metrics for a competitor"""
    # Get competitor
    result = await db.execute(
        select(model.TwitterCompetitor).filter(model.TwitterCompetitor.id == competitor_id)
    )
    db_competitor = result.scalars().first()

    if db_competitor is None:
        raise HTTPException(status_code=404, detail="Competitor not found")

    # Collect metrics
    try:
        # Get profile data
        profile = await twitter_service.get_user_profile(db_competitor.username)

        # Get engagement metrics
        engagement = await twitter_service.get_engagement_metrics(db_competitor.username, days=days)

        # Create metrics record
        metrics = model.TwitterCompetitorMetrics(
            competitor_id=competitor_id,
            followers_count=profile.public_metrics.get('followers_count', 0),
            following_count=profile.public_metrics.get('following_count', 0),
            tweet_count=profile.public_metrics.get('tweet_count', 0),
            listed_count=profile.public_metrics.get('listed_count', 0),
            avg_likes=engagement['avg_likes'],
            avg_retweets=engagement['avg_retweets'],
            avg_replies=engagement['avg_replies'],
            avg_quotes=engagement['avg_quotes'],
            engagement_rate=engagement['engagement_rate'],
            avg_tweet_length=engagement['content_metrics']['avg_tweet_length'],
            media_percentage=engagement['content_metrics']['media_percentage'],
            url_percentage=engagement['content_metrics']['url_percentage'],
            hashtag_percentage=engagement['content_metrics']['hashtag_percentage']
        )

        db.add(metrics)
        await db.commit()
        await db.refresh(metrics)
        logger.info(f"Collected metrics for competitor: {db_competitor.username}")
        return metrics
    except Exception as e:
        await db.rollback()
        logger.error(f"Failed to collect metrics: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to collect metrics: {str(e)}")

@router.get("/{competitor_id}/competitor-twitter-metrics", response_model=List[schema.TwitterCompetitorMetrics], dependencies=[Depends(rate_limiter)])
async def get_competitor_metrics(
    competitor_id: str,
    limit: int = Query(10, description="Number of metrics records to return"),
    db: AsyncSession = Depends(get_db)
):
    """Get historical metrics for a competitor"""
    # Check if competitor exists
    result = await db.execute(
        select(model.TwitterCompetitor).filter(model.TwitterCompetitor.id == competitor_id)
    )
    db_competitor = result.scalars().first()

    if db_competitor is None:
        raise HTTPException(status_code=404, detail="Competitor not found")

    # Get metrics
    metrics_result = await db.execute(
        select(model.TwitterCompetitorMetrics)
        .filter(model.TwitterCompetitorMetrics.competitor_id == competitor_id)
        .order_by(model.TwitterCompetitorMetrics.collected_at.desc())
        .limit(limit)
    )
    metrics = metrics_result.scalars().all()

    if not metrics:
        return []

    return metrics

@router.get("/latest-twitter-metrics-all-competitors", response_model=List[schema.TwitterCompetitorMetrics], dependencies=[Depends(rate_limiter)])
async def get_latest_metrics_all_competitors(
    db: AsyncSession = Depends(get_db)
):
    """Get the latest metrics for all competitors"""
    # Get list of competitors
    result = await db.execute(select(model.TwitterCompetitor))
    competitors = result.scalars().all()

    results = []
    for competitor in competitors:
        # Get latest metrics for this competitor
        metrics_result = await db.execute(
            select(model.TwitterCompetitorMetrics)
            .filter(model.TwitterCompetitorMetrics.competitor_id == competitor.id)
            .order_by(model.TwitterCompetitorMetrics.collected_at.desc())
            .limit(1)
        )
        latest = metrics_result.scalars().first()

        if latest:
            results.append(latest)

    return results

@router.delete("/delete-twitter-metrics/{metrics_id}", response_model=schema.StatusResponse, dependencies=[Depends(rate_limiter)])
async def delete_metrics(
    metrics_id: str,
    db: AsyncSession = Depends(get_db)
):
    """Delete a specific metrics record"""
    result = await db.execute(
        select(model.TwitterCompetitorMetrics).filter(
            model.TwitterCompetitorMetrics.id == metrics_id
        )
    )
    db_metrics = result.scalars().first()

    if db_metrics is None:
        raise HTTPException(status_code=404, detail="Metrics record not found")

    try:
        await db.delete(db_metrics)
        await db.commit()
        logger.info(f"Deleted metrics record #{metrics_id}")
        return {
            "status": "success",
            "message": "Metrics record deleted successfully",
            "data": None
        }
    except Exception as e:
        await db.rollback()
        logger.error(f"Error deleting metrics: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete metrics: {str(e)}")
