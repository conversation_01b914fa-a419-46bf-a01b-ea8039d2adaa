import asyncio
import logging
from datetime import datetime
from typing import Annotated, <PERSON>, Optional, Tuple

from fastapi import APIRouter, Depends, HTTPException
from fastapi.security import <PERSON>TT<PERSON><PERSON>earer
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload

from app.database.session import <PERSON><PERSON><PERSON><PERSON>, get_db
from app.models import model
from app.schemas import schema
from app.services.twitter_service import TwitterService
from app.utils.dependency import rate_limiter, verify_organization

router = APIRouter()
logger = logging.getLogger(__name__)
security = HTTPBearer()

# This module handles Twitter competitor tracking functionality

async def collect_competitor_metrics_immediately(competitor_id: str):
    """
    Collect metrics for a competitor immediately after it's added
    This function runs in the background and doesn't block the API response
    """
    logger.info(f"Starting immediate metrics collection for competitor ID: {competitor_id}")
    twitter_service = TwitterService()

    try:
        async with SessionLocal() as db:
            # Get competitor
            result = await db.execute(
                select(model.TwitterCompetitor).filter(model.TwitterCompetitor.id == competitor_id)
            )
            competitor = result.scalars().first()

            if not competitor:
                logger.error(f"Competitor with ID {competitor_id} not found")
                return

            # Get profile data
            profile = await twitter_service.get_user_profile(competitor.username)

            # Get engagement metrics
            engagement = await twitter_service.get_engagement_metrics(competitor.username, days=30)

            # Create metrics record
            metrics = model.TwitterCompetitorMetrics(
                competitor_id=competitor.id,
                followers_count=profile.public_metrics.get('followers_count', 0),
                following_count=profile.public_metrics.get('following_count', 0),
                tweet_count=profile.public_metrics.get('tweet_count', 0),
                listed_count=profile.public_metrics.get('listed_count', 0),
                avg_likes=engagement['avg_likes'],
                avg_retweets=engagement['avg_retweets'],
                avg_replies=engagement['avg_replies'],
                avg_quotes=engagement['avg_quotes'],
                engagement_rate=engagement['engagement_rate'],
                avg_tweet_length=engagement['content_metrics']['avg_tweet_length'],
                media_percentage=engagement['content_metrics']['media_percentage'],
                url_percentage=engagement['content_metrics']['url_percentage'],
                hashtag_percentage=engagement['content_metrics']['hashtag_percentage']
            )

            # Update competitor's last_metrics_update timestamp
            competitor.last_metrics_update = datetime.now()

            # Add metrics to database
            db.add(metrics)
            await db.commit()

            logger.info(f"Successfully collected metrics for competitor: {competitor.username}")

    except Exception as e:
        logger.error(f"Error collecting metrics for competitor ID {competitor_id}: {str(e)}")

async def _add_single_competitor(
    competitor: schema.OrganizationCompetitorCreate,
    organization_id: str,
    db: AsyncSession
) -> Tuple[Optional[model.OrganizationCompetitor], Optional[str]]:
    """Helper function to add a single competitor to an organization's tracking list"""
    try:
        # First, check if the competitor already exists in the database
        result = await db.execute(
            select(model.TwitterCompetitor).filter(
                model.TwitterCompetitor.username == competitor.username
            )
        )
        db_competitor = result.scalars().first()

        # If competitor doesn't exist, create it
        if not db_competitor:
            db_competitor = model.TwitterCompetitor(username=competitor.username)
            db.add(db_competitor)
            await db.commit()
            await db.refresh(db_competitor)
            logger.info(f"Added new competitor to database: {competitor.username}")

        # Check if this organization is already tracking this competitor
        result = await db.execute(
            select(model.OrganizationCompetitor).filter(
                model.OrganizationCompetitor.organization_id == organization_id,
                model.OrganizationCompetitor.competitor_id == db_competitor.id
            )
        )
        existing_org_competitor = result.scalars().first()

        if existing_org_competitor:
            return None, f"Organization is already tracking competitor: {competitor.username}"

        # Create the organization-competitor relationship
        org_competitor = model.OrganizationCompetitor(
            organization_id=organization_id,
            competitor_id=db_competitor.id,
            notes=competitor.notes
        )

        db.add(org_competitor)
        await db.commit()
        await db.refresh(org_competitor)

        # Load the competitor relationship for the response
        result = await db.execute(
            select(model.OrganizationCompetitor)
            .options(selectinload(model.OrganizationCompetitor.competitor)
                    .selectinload(model.TwitterCompetitor.metrics))
            .filter(model.OrganizationCompetitor.id == org_competitor.id)
        )
        org_competitor = result.scalars().first()

        logger.info(f"Organization {organization_id} is now tracking competitor: {competitor.username}")

        # Schedule immediate metrics collection in the background
        # We'll use asyncio.create_task to run this in the background without blocking
        asyncio.create_task(
            collect_competitor_metrics_immediately(db_competitor.id)
        )

        return org_competitor, None
    except Exception as e:
        await db.rollback()
        logger.error(f"Error adding competitor to organization: {str(e)}")
        return None, f"Failed to add competitor {competitor.username}: {str(e)}"

@router.post("/twitter-competitors", response_model=schema.OrganizationCompetitor, dependencies=[Depends(rate_limiter)])
async def add_twitter_competitor(
    competitor: schema.OrganizationCompetitorCreate,
    organization_id: Annotated[str, Depends(verify_organization)],
    db: AsyncSession = Depends(get_db)
):
    """Add a Twitter competitor to an organization's tracking list"""
    org_competitor, error = await _add_single_competitor(competitor, organization_id, db)

    if error:
        raise HTTPException(status_code=400, detail=error)

    return org_competitor

@router.post("/twitter-competitors/batch", response_model=schema.BatchCompetitorResponse, dependencies=[Depends(rate_limiter)])
async def add_twitter_competitors_batch(
    batch: schema.OrganizationCompetitorBatchCreate,
    organization_id: Annotated[str, Depends(verify_organization)],
    db: AsyncSession = Depends(get_db)
):
    """Add multiple Twitter competitors to an organization's tracking list in a single request"""
    if not batch.competitors:
        raise HTTPException(status_code=400, detail="No competitors provided")

    added_competitors = []
    skipped_competitors = []

    for competitor in batch.competitors:
        org_competitor, error = await _add_single_competitor(competitor, organization_id, db)

        if org_competitor:
            added_competitors.append(org_competitor)
        else:
            skipped_competitors.append({
                "username": competitor.username,
                "reason": error
            })

    total_added = len(added_competitors)
    total_skipped = len(skipped_competitors)

    logger.info(f"Batch operation completed: {total_added} competitors added, {total_skipped} skipped")

    return {
        "status": "success",
        "message": f"Added {total_added} competitors, skipped {total_skipped}",
        "added": added_competitors,
        "skipped": skipped_competitors
    }

@router.get("/twitter-competitors", response_model=List[schema.OrganizationCompetitor], dependencies=[Depends(rate_limiter)])
async def get_twitter_competitors(
    organization_id: Annotated[str, Depends(verify_organization)],
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db)
):
    """Get all Twitter competitors tracked by an organization"""
    result = await db.execute(
        select(model.OrganizationCompetitor)
        .options(selectinload(model.OrganizationCompetitor.competitor)
                 .selectinload(model.TwitterCompetitor.metrics))
        .filter(model.OrganizationCompetitor.organization_id == organization_id)
        .offset(skip).limit(limit)
    )
    org_competitors = result.scalars().all()
    return org_competitors

@router.get("/twitter-competitors/all", response_model=List[schema.TwitterCompetitor], dependencies=[Depends(rate_limiter)])
async def read_all_twitter_competitors(
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db)
):
    """Get all Twitter competitors in the system (admin only)"""
    result = await db.execute(
        select(model.TwitterCompetitor)
        .options(selectinload(model.TwitterCompetitor.metrics))
        .offset(skip).limit(limit)
    )
    competitors = result.scalars().all()
    return competitors

@router.get("/twitter-competitors/{org_competitor_id}", response_model=schema.OrganizationCompetitor, dependencies=[Depends(rate_limiter)])
async def get_twitter_competitor(
    org_competitor_id: str,
    organization_id: Annotated[str, Depends(verify_organization)],
    db: AsyncSession = Depends(get_db)
):
    """Get a specific Twitter competitor tracked by an organization"""
    result = await db.execute(
        select(model.OrganizationCompetitor)
        .options(selectinload(model.OrganizationCompetitor.competitor)
                 .selectinload(model.TwitterCompetitor.metrics))
        .filter(
            model.OrganizationCompetitor.id == org_competitor_id,
            model.OrganizationCompetitor.organization_id == organization_id
        )
    )
    org_competitor = result.scalars().first()

    if org_competitor is None:
        raise HTTPException(status_code=404, detail="Competitor not found for this organization")

    return org_competitor

@router.get("/twitter-competitors/details/{competitor_id}", response_model=schema.TwitterCompetitor, dependencies=[Depends(rate_limiter)])
async def get_twitter_competitor_details(
    competitor_id: str,
    db: AsyncSession = Depends(get_db)
):
    """Get detailed information about a Twitter competitor (admin only)"""
    result = await db.execute(
        select(model.TwitterCompetitor)
        .options(selectinload(model.TwitterCompetitor.metrics))
        .filter(model.TwitterCompetitor.id == competitor_id)
    )
    db_competitor = result.scalars().first()

    if db_competitor is None:
        raise HTTPException(status_code=404, detail="Twitter competitor not found")

    return db_competitor

@router.put("/twitter-competitors/{org_competitor_id}", response_model=schema.OrganizationCompetitor, dependencies=[Depends(rate_limiter)])
async def update_twitter_competitor(
    org_competitor_id: str,
    update_data: schema.OrganizationCompetitorUpdate,
    organization_id: Annotated[str, Depends(verify_organization)],
    db: AsyncSession = Depends(get_db)
):
    """
    Update an organization's Twitter competitor notes
    """
    result = await db.execute(
        select(model.OrganizationCompetitor)
        .options(selectinload(model.OrganizationCompetitor.competitor))
        .filter(
            model.OrganizationCompetitor.id == org_competitor_id,
            model.OrganizationCompetitor.organization_id == organization_id
        )
    )
    org_competitor = result.scalars().first()

    if org_competitor is None:
        raise HTTPException(status_code=404, detail="Competitor not found for this organization")

    # Update notes
    if update_data.notes is not None:
        org_competitor.notes = update_data.notes

    try:
        await db.commit()
        await db.refresh(org_competitor)

        # Reload with metrics
        result = await db.execute(
            select(model.OrganizationCompetitor)
            .options(selectinload(model.OrganizationCompetitor.competitor)
                     .selectinload(model.TwitterCompetitor.metrics))
            .filter(model.OrganizationCompetitor.id == org_competitor_id)
        )
        org_competitor = result.scalars().first()

        logger.info(f"Updated organization competitor #{org_competitor_id}")
        return org_competitor
    except Exception as e:
        await db.rollback()
        logger.error(f"Error updating organization competitor: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update organization competitor: {str(e)}")

@router.delete("/twitter-competitors/{org_competitor_id}", response_model=schema.StatusResponse, dependencies=[Depends(rate_limiter)])
async def delete_twitter_competitor(
    org_competitor_id: str,
    organization_id: Annotated[str, Depends(verify_organization)],
    db: AsyncSession = Depends(get_db)
):
    """Remove a Twitter competitor from an organization's tracking list"""
    result = await db.execute(
        select(model.OrganizationCompetitor)
        .options(selectinload(model.OrganizationCompetitor.competitor))
        .filter(
            model.OrganizationCompetitor.id == org_competitor_id,
            model.OrganizationCompetitor.organization_id == organization_id
        )
    )
    org_competitor = result.scalars().first()

    if org_competitor is None:
        raise HTTPException(status_code=404, detail="Competitor not found for this organization")

    try:
        username = org_competitor.competitor.username
        await db.delete(org_competitor)
        await db.commit()
        logger.info(f"Organization {organization_id} stopped tracking competitor: {username}")
        return {
            "status": "success",
            "message": f"Competitor {username} removed from tracking list",
            "data": None
        }
    except Exception as e:
        await db.rollback()
        logger.error(f"Error removing competitor from organization: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to remove competitor from organization: {str(e)}")

@router.put("/admin/twitter-competitors/{competitor_id}", response_model=schema.TwitterCompetitor, dependencies=[Depends(rate_limiter)])
async def admin_update_twitter_competitor(
    competitor_id: str,
    competitor: schema.TwitterCompetitorCreate,
    db: AsyncSession = Depends(get_db)
):
    """
    Update a Twitter competitor's information (admin only)
    """
    result = await db.execute(
        select(model.TwitterCompetitor)
        .options(selectinload(model.TwitterCompetitor.metrics))
        .filter(model.TwitterCompetitor.id == competitor_id)
    )
    db_competitor = result.scalars().first()

    if db_competitor is None:
        raise HTTPException(status_code=404, detail="Twitter competitor not found")

    # Check if trying to update to a username that already exists
    if competitor.username != db_competitor.username:
        existing_result = await db.execute(
            select(model.TwitterCompetitor).filter(
                model.TwitterCompetitor.username == competitor.username
            )
        )
        existing = existing_result.scalars().first()

        if existing:
            raise HTTPException(status_code=400, detail="Twitter username already exists")

    # Update fields
    db_competitor.username = competitor.username

    try:
        await db.commit()
        await db.refresh(db_competitor)
        logger.info(f"Admin updated Twitter competitor #{competitor_id}: {competitor.username}")
        return db_competitor
    except Exception as e:
        await db.rollback()
        logger.error(f"Error updating Twitter competitor: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update Twitter competitor: {str(e)}")

@router.delete("/admin/twitter-competitors/{competitor_id}", response_model=schema.StatusResponse, dependencies=[Depends(rate_limiter)])
async def admin_delete_twitter_competitor(
    competitor_id: str,
    db: AsyncSession = Depends(get_db)
):
    """Delete a Twitter competitor completely from the system (admin only)"""
    result = await db.execute(
        select(model.TwitterCompetitor).filter(
            model.TwitterCompetitor.id == competitor_id
        )
    )
    db_competitor = result.scalars().first()

    if db_competitor is None:
        raise HTTPException(status_code=404, detail="Twitter competitor not found")

    try:
        username = db_competitor.username
        await db.delete(db_competitor)
        await db.commit()
        logger.info(f"Admin deleted Twitter competitor #{competitor_id}: {username}")
        return {
            "status": "success",
            "message": f"Twitter competitor {username} deleted successfully from the system",
            "data": None
        }
    except Exception as e:
        await db.rollback()
        logger.error(f"Error deleting Twitter competitor: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete Twitter competitor: {str(e)}")
