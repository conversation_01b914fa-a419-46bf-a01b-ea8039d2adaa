from datetime import datetime
from typing import Any, List, Optional, Dict

from enum import Enum
from pydantic import (
    BaseModel, Field, HttpUrl,
    field_validator
)


class EnhancedParticipant(BaseModel):
    id: str
    name: str


class ParticipantsData(BaseModel):
    data: List[EnhancedParticipant]


class SendersData(BaseModel):
    data: List[EnhancedParticipant]


class MessageSender(BaseModel):
    id: str
    name: str


class MessageRecipient(BaseModel):
    id: str
    name: str


class MessageRecipientsData(BaseModel):
    data: List[MessageRecipient]


class EnhancedMessage(BaseModel):
    id: str
    message: Optional[str] = None
    created_time: Optional[str] = None
    from_: Optional[MessageSender] = Field(None, alias='from')
    to: Optional[MessageRecipientsData] = None
    attachments: Optional[Any] = None

    model_config = {"populate_by_name": True}


class Cursors(BaseModel):
    before: Optional[str] = None
    after: Optional[str] = None


class Paging(BaseModel):
    cursors: Optional[Cursors] = None
    next: Optional[str] = None
    previous: Optional[str] = None


class MessagesData(BaseModel):
    data: List[EnhancedMessage]
    paging: Optional[Paging] = None


class EnhancedConversation(BaseModel):
    id: str
    updated_time: Optional[str] = None
    message_count: Optional[int] = None
    participants: ParticipantsData
    senders: SendersData
    messages: MessagesData


class ConversationsResponseData(BaseModel):
    data: List[EnhancedConversation]
    paging: Optional[Paging] = None


class FacebookMessagesResponse(BaseModel):
    status_code: int
    success: bool
    message: str
    data: ConversationsResponseData


# Send Message Response Schemas
class AttachmentType(str, Enum):
    IMAGE = "image"
    VIDEO = "video"
    AUDIO = "audio"
    FILE = "file"


class AttachmentPayload(BaseModel):
    url: HttpUrl
    is_reusable: bool = True

    @field_validator('url', mode='after')
    def validate_url(cls, url):
        return str(url)


class MessageAttachment(BaseModel):
    type: AttachmentType
    payload: AttachmentPayload


class SendMessageSchema(BaseModel):
    receiver_id: str
    message: Optional[str] = None
    attachment: Optional[MessageAttachment] = None
    attachments: Optional[List[MessageAttachment]] = None

    class Config:
        json_schema_extra = {
            "example": {
                "receiver_id": "1254459154682919",
                "message": "Hello! Thanks for reaching out to us.",
                "attachment": {
                    "type": "image",
                    "payload": {
                        "url": "https://example.com/image.jpg",
                        "is_reusable": True
                    }
                }
            }
        }


class FacebookSendMessageData(BaseModel):
    recipient_id: str
    message_id: str


class FacebookSendMessageResponse(BaseModel):
    status_code: int
    success: bool
    message: str
    data: FacebookSendMessageData


# Comment response schemas
class FacebookCommentData(BaseModel):
    id: str


class FacebookCommentResponse(BaseModel):
    status_code: int
    success: bool
    message: str
    data: FacebookCommentData


# Post list response schemas
class FacebookPostItem(BaseModel):
    post_id: str
    message: str
    username: str
    created_time: str
    primary_image: str
    images: List[Dict[str, str]]
    impressions: int
    clicks: int
    likes: int
    reach: int
    comments: int
    engagements: int
    click_rate: float
    schedulecontent_id: str


class FacebookPostsResponse(BaseModel):
    status_code: int
    success: bool
    message: str
    data: List[FacebookPostItem]


# Comment reply schema
class CommentReply(BaseModel):
    created_time: str
    from_: dict = Field(alias='from')
    message: str
    id: str

    model_config = {"populate_by_name": True}


# Get comments response schemas
class CommentItem(BaseModel):
    user_id: str
    name: str
    message: str
    id: str
    likes: int
    total_replies: int
    comments: List[CommentReply]


class CommentAuthor(BaseModel):
    """Author information for a comment"""
    id: Optional[str] = None
    username: Optional[str] = Field(None, alias="name")

    class Config:
        from_attributes = True


class CommentMedia(BaseModel):
    """Media information for a comment"""
    media_type: str = Field('...', alias="type")
    media_url: str = Field('...', alias='url')


class CommentReaction(BaseModel):
    """Reaction information for a comment"""
    reaction: str = Field('...', alias='type')
    name: str

    class Config:
        from_attributes = True


class FacebookCommentsResponse(BaseModel):
    """Individual comment from Instagram API"""
    comment_id: str
    content: str
    created_time: datetime
    parent_id: Optional[str] = None
    sender: Optional[CommentAuthor] = {}
    media: Optional[CommentMedia] = Field(None, alias='attachments')
    reactions: Optional[List[CommentReaction]]
    extra_data: Optional[Dict] = None

    class Config:
        populate_by_name = True
        from_attributes = True


class CommentResponse(BaseModel):
    comments: List[FacebookCommentsResponse]
    total: int
    limit: int
    offset: int
