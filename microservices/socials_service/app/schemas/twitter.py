from typing import Any, List, Optional, Dict, Union
from datetime import datetime
from pydantic import BaseModel, Field


# Base schemas for DM Event types
class DmEventType(BaseModel):
    """Base class for different DM event types"""
    pass


class MessageCreateEvent(DmEventType):
    """Schema for MessageCreate event type"""
    event_type: str = "MessageCreate"
    id: str
    text: Optional[str] = None
    created_at: Optional[datetime] = None
    sender_id: Optional[str] = None
    dm_conversation_id: Optional[str] = None
    participant_ids: Optional[List[str]] = None
    attachments: Optional[Dict[str, Any]] = None
    referenced_tweets: Optional[List[Dict[str, Any]]] = None


class ParticipantsJoinEvent(DmEventType):
    """Schema for ParticipantsJoin event type"""
    event_type: str = "ParticipantsJoin"
    id: str
    created_at: Optional[datetime] = None
    sender_id: Optional[str] = None
    dm_conversation_id: Optional[str] = None
    participant_ids: Optional[List[str]] = None


class ParticipantsLeaveEvent(DmEventType):
    """Schema for ParticipantsLeave event type"""
    event_type: str = "ParticipantsLeave"
    id: str
    created_at: Optional[datetime] = None
    sender_id: Optional[str] = None
    dm_conversation_id: Optional[str] = None
    participant_ids: Optional[List[str]] = None


# Union type for all DM events
DmEvent = Union[MessageCreateEvent, ParticipantsJoinEvent, ParticipantsLeaveEvent]


# User schema for includes
class TwitterUser(BaseModel):
    """Schema for Twitter User object in includes"""
    id: str
    name: str
    username: str
    created_at: Optional[datetime] = None
    description: Optional[str] = None
    location: Optional[str] = None
    profile_image_url: Optional[str] = None
    protected: Optional[bool] = None
    public_metrics: Optional[Dict[str, int]] = None
    url: Optional[str] = None
    verified: Optional[bool] = None
    verified_type: Optional[str] = None


# Media schema for includes
class TwitterMedia(BaseModel):
    """Schema for Twitter Media object in includes"""
    media_key: str
    type: str
    url: Optional[str] = None
    duration_ms: Optional[int] = None
    height: Optional[int] = None
    width: Optional[int] = None
    preview_image_url: Optional[str] = None
    public_metrics: Optional[Dict[str, int]] = None
    alt_text: Optional[str] = None


# Tweet schema for includes
class TwitterTweet(BaseModel):
    """Schema for Twitter Tweet object in includes"""
    id: str
    text: str
    created_at: Optional[datetime] = None
    author_id: Optional[str] = None
    conversation_id: Optional[str] = None
    public_metrics: Optional[Dict[str, int]] = None
    attachments: Optional[Dict[str, Any]] = None
    entities: Optional[Dict[str, Any]] = None


# Includes schema
class TwitterIncludes(BaseModel):
    """Schema for includes in Twitter API responses"""
    users: Optional[List[TwitterUser]] = None
    media: Optional[List[TwitterMedia]] = None
    tweets: Optional[List[TwitterTweet]] = None


# Meta schema for pagination
class TwitterMeta(BaseModel):
    """Schema for meta information in Twitter API responses"""
    result_count: Optional[int] = None
    next_token: Optional[str] = None
    previous_token: Optional[str] = None


# Error schema
class TwitterError(BaseModel):
    """Schema for Twitter API errors"""
    detail: str
    title: str
    resource_type: Optional[str] = None
    parameter: Optional[str] = None
    value: Optional[str] = None
    type: Optional[str] = None


# Base response wrapper
class TwitterBaseResponse(BaseModel):
    """Base response wrapper for all Twitter API responses"""
    status_code: int
    success: bool
    message: str


# 1. GET DIRECT MESSAGES RESPONSE SCHEMAS
class GetDirectMessagesData(BaseModel):
    """Data schema for get direct messages response"""
    data: Optional[List[DmEvent]] = None
    includes: Optional[TwitterIncludes] = None
    meta: Optional[TwitterMeta] = None
    errors: Optional[List[TwitterError]] = None


class GetDirectMessagesResponse(TwitterBaseResponse):
    """Response schema for GET /2/dm_events endpoint"""
    data: GetDirectMessagesData


# 2. SEND MESSAGE TO USER RESPONSE SCHEMAS
class SendMessageToUserData(BaseModel):
    """Data schema for send message to user response"""
    dm_conversation_id: str
    dm_event_id: str


class SendMessageToUserResponse(TwitterBaseResponse):
    """Response schema for POST /2/dm_conversations/with/{participant_id}/messages endpoint"""
    data: SendMessageToUserData


# 3. CREATE NEW DM CONVERSATION RESPONSE SCHEMAS
class CreateDmConversationData(BaseModel):
    """Data schema for create DM conversation response"""
    dm_conversation_id: str
    dm_event_id: str


class CreateDmConversationResponse(TwitterBaseResponse):
    """Response schema for POST /2/dm_conversations endpoint"""
    data: CreateDmConversationData


# 4. SEND MESSAGE TO DM CONVERSATION RESPONSE SCHEMAS
class SendMessageToConversationData(BaseModel):
    """Data schema for send message to conversation response"""
    dm_conversation_id: str
    dm_event_id: str


class SendMessageToConversationResponse(TwitterBaseResponse):
    """Response schema for POST /2/dm_conversations/{dm_conversation_id}/messages endpoint"""
    data: SendMessageToConversationData


# 5. DELETE DM RESPONSE SCHEMAS (Future use - not available in v2 yet)
class DeleteDmData(BaseModel):
    """Data schema for delete DM response"""
    deleted: bool
    dm_event_id: str


class DeleteDmResponse(TwitterBaseResponse):
    """Response schema for DELETE DM endpoint (future use)"""
    data: DeleteDmData


# Additional schemas for DM conversation lookup
class DmConversation(BaseModel):
    """Schema for DM Conversation object"""
    id: str


class GetDmConversationEventsData(BaseModel):
    """Data schema for get DM conversation events response"""
    data: Optional[List[DmEvent]] = None
    includes: Optional[TwitterIncludes] = None
    meta: Optional[TwitterMeta] = None
    errors: Optional[List[TwitterError]] = None


class GetDmConversationEventsResponse(TwitterBaseResponse):
    """Response schema for GET /2/dm_conversations/{id}/dm_events endpoint"""
    data: GetDmConversationEventsData


# Request schemas for creating messages
class CreateMessageRequest(BaseModel):
    """Request schema for creating a new DM message"""
    text: Optional[str] = None
    media_id: Optional[str] = None
    attachments: Optional[List[Dict[str, Any]]] = None


class CreateDmConversationRequest(BaseModel):
    """Request schema for creating a new DM conversation"""
    conversation_type: str = "Group"
    participant_ids: List[str]
    text: Optional[str] = None
    media_id: Optional[str] = None
    attachments: Optional[List[Dict[str, Any]]] = None
