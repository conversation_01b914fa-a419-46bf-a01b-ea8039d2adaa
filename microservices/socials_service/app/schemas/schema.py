from datetime import datetime
from enum import StrEnum, Enum
from typing import List, Optional, Dict, Any
from app.schemas.instagram_schema import InstagramCreateMediaType
import httpx
from pydantic import (
    BaseModel,
    field_validator,
    Field,
    HttpUrl, model_validator)
from app.core.config import settings


# create an Enum of the platforms
class Platform(StrEnum):
    facebook = "facebook"
    twitter = "twitter"
    instagram = "instagram"
    linkedin = "linkedin"


class ScheduleContentStatus(StrEnum):
    draft = "draft"
    published = "published"
    scheduled = "scheduled"
    publishing = "publishing"
    failed = "failed"
    elapsed = "elapsed"
    cancelled = "cancelled"


class ApprovalStatus(StrEnum):
    pending = "pending"
    approved = "approved"
    declined = "declined"
    expired = "expired"


class Message(BaseModel):
    message: str


class InitialiseTwitter(BaseModel):
    social_media_user_id: str
    access_token: str
    refresh_token: str
    platform_name: str
    username: str


class InitialiseFacebook(BaseModel):
    social_media_user_id: str
    access_token: str
    username: str


class InitialiseLinkedin(BaseModel):
    social_media_user_id: str
    access_token: str
    username: str


class SocialAccountPublic(BaseModel):
    login_status: bool
    id: str
    organisation_id: str
    platform: str
    username: str


class SocialAccountDisconnect(BaseModel):
    platform_name: str


allowed_ext = settings.ALLOWED_EXT


class ScheduleContentRequest(BaseModel):
    content: Optional[str] = None
    post_time: datetime = datetime.now()
    platforms: List[Platform]
    reviewer_ids: List[str]
    media_links: Optional[List[HttpUrl]] = None
    media_type: Optional[str] = ''
    is_carousel: bool = False
    media_items_info: Optional[List[Dict[str, str]]] = None
    status: ScheduleContentStatus = ScheduleContentStatus.draft

    @field_validator("platforms", mode='before')
    def confirm_only_one_platform_selected(cls, platforms):
        if len(platforms) > 1:
            raise ValueError("Only one platform can be selected")
        return platforms

    @field_validator("content", mode='before')
    def validate_content(cls, content):
        if content is None:
            content = ''
        return content

    @field_validator("status", mode="before")
    def confirm_status(cls, status):
        if status:
            if status not in [ScheduleContentStatus.draft, ScheduleContentStatus.scheduled]:
                raise ValueError("Unauthorized scheduled content status change")
        return status

    @model_validator(mode='after')
    def validate_ig_content(self):
        if self.platforms and Platform.instagram in self.platforms:
            if not self.media_links:
                raise ValueError("Media links must be provided for Instagram")

            # For carousel posts
            if not self.is_carousel and len(self.media_links) > 1:
                raise ValueError("Only one media link is allowed when 'is_carousel' is False")

            if self.is_carousel:
                if len(self.media_links) < 2:
                    raise ValueError("Carousel posts require at least 2 media items")
                if len(self.media_links) > 10:
                    raise ValueError("Carousel posts can have maximum 10 media items")

                # If media_items_info is provided, validate it matches media_links
                if self.media_items_info and len(self.media_items_info) != len(self.media_links):
                    raise ValueError("media_items_info must have same length as media_links")
            # For single posts
            elif len(self.media_links) == 1 and not self.media_type:
                raise ValueError("Media type must be provided for Instagram")

            # Validate media_type
            if isinstance(self.media_type, str) and self.media_type:
                try:
                    InstagramCreateMediaType(self.media_type.lower())
                except ValueError:
                    valid_types = [e.value for e in InstagramCreateMediaType]
                    raise ValueError(
                        f"Invalid media type: {self.media_type}. "
                        f"Please use one of {', '.join(valid_types)}"
                    )

            # validate file extensions
            for link in self.media_links:
                if not any(str(link).lower().endswith(ext) for ext in allowed_ext):
                    raise ValueError(
                        f"Invalid file extension in link: {link}. "
                        f"Please use one of {', '.join(allowed_ext)}"
                        )
        # confirm each link is reachable
        if self.media_links:
            unreachable = []
            for link in self.media_links:
                try:
                    response = httpx.head(str(link), timeout=10)
                    if response.status_code >= 400:
                        unreachable.append(str(link))
                except Exception:
                    unreachable.append(str(link))
            if unreachable:
                raise ValueError(f"The following links are not reachable: {', '.join(unreachable)}")
            # update the links to be strings
            for link in self.media_links:
                self.media_links = [str(link) for link in self.media_links]
        return self

    class Config:
        arbitrary_types_allowed = True
        use_enum_values = True
        json_encoders = {list: lambda v: str(v)}
        json_schema_extra = {
            "example": {
                "content": "Check out our new product line! #newproduct #launch",
                "post_time": "2023-08-15T14:30:00Z",
                "platforms": ["instagram"],
                "reviewer_ids": ["user123", "user456"],
                "is_carousel": True,
                "media_links": [
                    "https://example.com/images/product1.jpg",
                    "https://example.com/videos/product_demo.mp4",
                    "https://example.com/images/product2.jpg"
                ],
                "media_items_info": [
                    {
                        "media_type": "image",
                        "position": "0",
                        "alt_text": "Product 1 front view"
                    },
                    {
                        "media_type": "video",
                        "position": "1",
                        "duration": "15"
                    },
                    {
                        "media_type": "image",
                        "position": "2",
                        "alt_text": "Product 2 side view"
                    }
                ],
                "media_type": "image",
                "status": "draft"
            }
        }


class UpdateScheduleContentRequest(BaseModel):
    content: Optional[str] = None
    post_time: Optional[datetime] = None
    platforms: Optional[List[Platform]] = None
    reviewer_ids: Optional[List[str]] = None
    media_links: Optional[List[HttpUrl]] = None
    media_type: Optional[str] = ''
    status: Optional[ScheduleContentStatus] = None

    @field_validator("platforms", mode='before')
    def confirm_only_one_platform_selected(cls, platforms):
        if len(platforms) > 1:
            raise ValueError("Only one platform can be selected")
        return platforms

    @field_validator("status", mode="before")
    def confirm_status(cls, status):
        if status:
            if status not in [ScheduleContentStatus.draft, ScheduleContentStatus.scheduled]:
                raise ValueError("Unauthorized scheduled content status change")
        return status

    @model_validator(mode='after')
    def validate_ig_content(self):
        if self.media_type and isinstance(self.media_type, str):
            try:
                InstagramCreateMediaType(self.media_type)
            except ValueError:
                valid_types = [e.value for e in InstagramCreateMediaType]
                raise ValueError(
                    f"Invalid media type: {self.media_type}. "
                    f"Please use one of {', '.join(valid_types)}"
                )

        # validate file extensions

        if self.media_links:
            for link in self.media_links:
                if not any(str(link).lower().endswith(ext) for ext in allowed_ext):
                    raise ValueError(
                        f"Invalid file extension in link: {link}. "
                        f"Please use one of {', '.join(allowed_ext)}"
                        )
            # confirm each link is reachable
            unreachable = []
            for link in self.media_links:
                try:
                    response = httpx.head(str(link), timeout=10)
                    if response.status_code >= 400:
                        unreachable.append(str(link))
                except Exception:
                    unreachable.append(str(link))
            if unreachable:
                raise ValueError(f"The following links are not reachable: {', '.join(unreachable)}")
            # update the links to be strings
            for link in self.media_links:
                self.media_links = [str(link) for link in self.media_links]
        return self

    class Config:
        arbitrary_types_allowed = True
        use_enum_values = True
        json_encoders = {list: lambda v: str(v)}


class CalendarViewResponse(BaseModel):
    id: str
    platform: List[str]
    post_time: datetime
    content: str
    status: str
    media_links: Optional[List[HttpUrl]] = None
    media_type: Optional[str] = None
    approval_status: str
    approved_by: str | None
    user_id: str
    user_role: str
    reviewer_ids: List[str]

    class Config:
        arbitrary_types_allowed = True


class RescheduleContentRequest(BaseModel):
    post_time: datetime

    class Config:
        arbitrary_types_allowed = True


class ContentViewResponse(BaseModel):
    id: str
    platform: List[str] = Field('..', alias="platforms")
    post_time: datetime
    content: str
    status: str
    media_links: Optional[List[HttpUrl]] = None
    media_type: Optional[str] = None
    approval_status: str
    approved_by: str | None
    user_id: str
    user_role: str
    reviewer_ids: List[str]
    is_carousel: Optional[bool] = False
    media_items_info: Optional[List[Dict[str, str]]] = None

    class Config:
        arbitrary_types_allowed = True
        from_attributes = True


class MetricsResponse(BaseModel):
    name: str
    value: int
    period: str
    extra_data: Optional[Dict[str, Any]] = None
    post_id: str
    scheduled_content_id: str

    class Config:
        from_attributes = True


class ContentView(BaseModel):
    content: ContentViewResponse
    post_metrics: Optional[List[MetricsResponse]] = []

    class Config:
        from_attributes = True


class ResponsePayload(BaseModel):
    status_code: int
    message: str
    data: Optional[dict | list] = None
    meta: Optional[Dict[str, Any]] = None
    success: bool = True


class X_DM_WITH(BaseModel):
    participant_id: str
    text: str


class X_DM(BaseModel):
    participant_ids: List[str]
    text: str


class X_DM_CONVERSATION(BaseModel):
    conversation_id: str
    text: str


class CommentResponse(BaseModel):
    text: str
    comment_id: str
    image_urls: Optional[List[str]] = None


class TwitterCompetitorBase(BaseModel):
    username: str


class TwitterCompetitorCreate(TwitterCompetitorBase):
    pass


class OrganizationCompetitorBase(BaseModel):
    organization_id: str
    competitor_id: str
    notes: Optional[str] = None


class OrganizationCompetitorCreate(BaseModel):
    username: str
    notes: Optional[str] = None


class OrganizationCompetitorBatchCreate(BaseModel):
    competitors: List[OrganizationCompetitorCreate]


class OrganizationCompetitorUpdate(BaseModel):
    notes: Optional[str] = None


class OrganizationCompetitor(BaseModel):
    id: str
    organization_id: str
    competitor_id: str
    added_at: datetime
    notes: Optional[str] = None
    competitor: Optional["TwitterCompetitor"] = None

    class Config:
        from_attributes = True
        arbitrary_types_allowed = True


class TwitterCompetitorMetricsBase(BaseModel):
    followers_count: int
    following_count: int
    tweet_count: int
    listed_count: int
    avg_likes: float
    avg_retweets: float
    avg_replies: float
    avg_quotes: float
    avg_impressions: Optional[float] = None
    engagement_rate: float
    avg_tweet_length: float
    media_percentage: float
    url_percentage: float
    hashtag_percentage: float


class TwitterCompetitorMetricsCreate(TwitterCompetitorMetricsBase):
    pass


class TwitterCompetitorMetrics(TwitterCompetitorMetricsBase):
    id: str
    competitor_id: str
    collected_at: datetime

    class Config:
        from_attributes = True


class TwitterCompetitor(TwitterCompetitorBase):
    id: str
    added_at: datetime
    last_metrics_update: Optional[datetime] = None
    metrics: List[TwitterCompetitorMetrics] = []

    class Config:
        from_attributes = True


class TwitterProfile(BaseModel):
    username: str
    followers: int
    following: int
    tweet_count: int
    listed_count: int


class TwitterEngagementMetrics(BaseModel):
    username: str
    period_days: int
    total_tweets: int
    avg_likes: float
    avg_retweets: float
    avg_replies: float
    avg_quotes: float
    engagement_rate: float
    content_metrics: Dict[str, float]


class TwitterComparisonResult(BaseModel):
    profiles: List[TwitterProfile]
    engagement: List[TwitterEngagementMetrics]


class StatusResponse(BaseModel):
    status: str
    message: str
    data: Optional[Any] = None


class BatchCompetitorResponse(BaseModel):
    status: str
    message: str
    added: List[OrganizationCompetitor] = []
    skipped: List[Dict[str, str]] = []


class ChartResponse(BaseModel):
    username: str
    chart: str  # Base64 encoded image


class FacebookPageMetricsBase(BaseModel):
    total_followers: int
    total_impressions: int
    total_reach: int
    total_engagements: int
    engagement_rate: Optional[float] = None


class FacebookPageMetricsCreate(FacebookPageMetricsBase):
    organisation_id: str
    page_id: str


class FacebookPageMetrics(FacebookPageMetricsBase):
    id: str
    organisation_id: str
    page_id: str
    collected_at: datetime

    class Config:
        from_attributes = True


class FacebookAudienceDemographicsBase(BaseModel):
    top_countries: Dict[str, int]
    top_cities: Dict[str, int]
    locales: Dict[str, int]


class FacebookAudienceDemographicsCreate(FacebookAudienceDemographicsBase):
    organisation_id: str
    page_id: str


class FacebookAudienceDemographics(FacebookAudienceDemographicsBase):
    id: str
    organisation_id: str
    page_id: str
    collected_at: datetime

    class Config:
        from_attributes = True


class FacebookGrowthTrendBase(BaseModel):
    trend_type: str
    month: str
    value: int
    growth_percentage: Optional[float] = None


class FacebookGrowthTrendCreate(FacebookGrowthTrendBase):
    organisation_id: str
    page_id: str


class FacebookGrowthTrend(FacebookGrowthTrendBase):
    id: str
    organisation_id: str
    page_id: str
    collected_at: datetime

    class Config:
        from_attributes = True


class FacebookTopPerformingPostBase(BaseModel):
    post_id: str
    message: Optional[str] = None
    created_time: Optional[datetime] = None
    permalink_url: Optional[str] = None
    full_picture: Optional[str] = None
    impressions: Optional[int] = None
    reach: Optional[int] = None
    likes: Optional[int] = None
    comments: Optional[int] = None
    clicks: Optional[int] = None
    engagement_rate: Optional[float] = None
    attachments: Optional[Dict[str, Any]] = None
    insights: Optional[List[Dict[str, Any]]] = None


class FacebookTopPerformingPostCreate(FacebookTopPerformingPostBase):
    organisation_id: str
    page_id: str


class FacebookTopPerformingPost(FacebookTopPerformingPostBase):
    id: str
    organisation_id: str
    page_id: str
    collected_at: datetime

    class Config:
        from_attributes = True


class InstagramAccountMetricsBase(BaseModel):
    followers_count: int
    follows_count: int
    media_count: int


class InstagramAccountMetricsCreate(InstagramAccountMetricsBase):
    organisation_id: str
    instagram_user_id: str


class InstagramAccountMetrics(InstagramAccountMetricsBase):
    id: str
    organisation_id: str
    instagram_user_id: str
    collected_at: datetime

    class Config:
        from_attributes = True


class InstagramMediaMetricsBase(BaseModel):
    media_id: str
    media_type: Optional[str] = None
    caption: Optional[str] = None
    permalink: Optional[str] = None
    timestamp: Optional[str] = None
    engagement: Optional[int] = None
    impressions: Optional[int] = None
    reach: Optional[int] = None


class InstagramMediaMetricsCreate(InstagramMediaMetricsBase):
    organisation_id: str
    instagram_user_id: str


class InstagramMediaMetrics(InstagramMediaMetricsBase):
    id: str
    organisation_id: str
    instagram_user_id: str
    collected_at: datetime

    class Config:
        from_attributes = True


class InstagramGrowthTrendBase(BaseModel):
    trend_type: str
    month: str
    value: int
    growth_percentage: Optional[float] = None


class InstagramGrowthTrendCreate(InstagramGrowthTrendBase):
    organisation_id: str
    instagram_user_id: str


class InstagramGrowthTrend(InstagramGrowthTrendBase):
    id: str
    organisation_id: str
    instagram_user_id: str
    collected_at: datetime

    class Config:
        from_attributes = True
