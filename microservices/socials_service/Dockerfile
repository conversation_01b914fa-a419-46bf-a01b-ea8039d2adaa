# Use the official Python image from the Docker Hub
FROM python:3.12-alpine

# Set the working directory
WORKDIR /app

# Copy the requirements file into the container
COPY requirements.txt ./

# Install the dependencies and curl
RUN pip install --no-cache-dir -r /app/requirements.txt

# Copy the content of the local src directory to the working directory
COPY . .

# create a directory for server logs
RUN mkdir -p /app/logs

EXPOSE 8006

# Command to run the application
CMD ["uvicorn", "--workers", "5", "--host", "0.0.0.0", "--port", "8006", "app.main:app"]
