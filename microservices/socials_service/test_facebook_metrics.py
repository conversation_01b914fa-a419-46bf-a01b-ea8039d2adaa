#!/usr/bin/env python
"""
Script to test Facebook metrics directly using the Graph API.
This will help identify which metrics are valid for the account.
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta, timezone
import httpx

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger("facebook_metrics_tester")

# Facebook API constants
FACEBOOK_GRAPH_API_URL = "https://graph.facebook.com/v16.0"
TIMEOUT = 30.0  # seconds

async def test_facebook_metrics(page_id, access_token):
    """Test which Facebook metrics are valid for the account"""
    
    # Define metrics to test
    metrics_to_test = [
        'page_fans',
        'page_follows',
        'page_impressions',
        'page_impressions_unique',
        'page_post_engagements',
        'page_engaged_users',
        'page_video_views',
        'post_clicks'
    ]
    
    # Calculate date range
    end_date = datetime.now(timezone.utc)
    start_date = end_date - timedelta(days=30)
    
    # Convert dates to UNIX timestamps
    since = int(start_date.timestamp())
    until = int(end_date.timestamp())
    
    valid_metrics = []
    results = {}
    
    # Test each metric individually
    async with httpx.AsyncClient(timeout=TIMEOUT) as client:
        for metric in metrics_to_test:
            try:
                logger.info(f"Testing metric: {metric}")
                
                url = f"{FACEBOOK_GRAPH_API_URL}/{page_id}/insights"
                params = {
                    "metric": metric,
                    "period": "day",
                    "since": since,
                    "until": until,
                    "access_token": access_token
                }
                
                response = await client.get(url, params=params)
                response.raise_for_status()
                data = response.json()
                
                if 'data' in data and len(data['data']) > 0:
                    valid_metrics.append(metric)
                    metric_data = data['data'][0]
                    values = [entry['value'] for entry in metric_data['values']]
                    
                    # For cumulative metrics like followers, take the most recent value
                    if metric in ['page_fans', 'page_follows']:
                        results[metric] = values[-1] if values else 0
                    else:
                        # For other metrics, sum the values over the period
                        results[metric] = sum(values)
                        
                    logger.info(f"  Valid metric: {metric} = {results[metric]}")
                else:
                    logger.warning(f"  No data returned for {metric}")
                    
            except Exception as e:
                logger.error(f"  Error testing {metric}: {str(e)}")
    
    # Print summary
    logger.info(f"Valid metrics: {valid_metrics}")
    logger.info(f"Results: {json.dumps(results, indent=2)}")
    
    return {
        'valid_metrics': valid_metrics,
        'results': results
    }

async def main():
    # Facebook credentials
    page_id = "500710046463557"
    access_token = "EAAH4ojWRVDsBO95kVCNeOyZCFZAn7Y8zhIki7MgzgQWbrgrSPisYz6ZB9TsZBhZAlbPAZA4nzxrM548OBEoZBKsxe8sJuInsCMgJpOVld9SHRXHrwZCzBUNNDvhNaoMFAvO58bDbBGkWrwPhG3vZCnGOQmfq3BYY3aO8CZB59Ji0qBF4ZCv5TdvuyZBQUtYl4LkR"
    
    logger.info("Testing Facebook metrics...")
    results = await test_facebook_metrics(page_id, access_token)
    
    # Save results to file
    with open('facebook_metrics_test.json', 'w') as f:
        json.dump(results, f, indent=2)
        logger.info("Results saved to facebook_metrics_test.json")

if __name__ == "__main__":
    asyncio.run(main())
