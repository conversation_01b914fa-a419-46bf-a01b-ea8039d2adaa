import asyncio
from typing import Dict

from fastapi import WebSocket
import redis.asyncio as aioredis

# Mapping of user_id to WebSocket connection
active_connections: Dict[str, WebSocket] = {}

# Redis connection settings
REDIS_URL = "redis://localhost:6379"
MESSAGE_CHANNEL_PREFIX = "messages:"  # e.g., messages:{user_id}

# Register a new WebSocket connection for a user
def register_connection(user_id: str, websocket: WebSocket):
    active_connections[user_id] = websocket

# Unregister a WebSocket connection for a user
def unregister_connection(user_id: str):
    active_connections.pop(user_id, None)

# Publish a new message event to Redis for a user
async def publish_new_message(user_id: str, message_data: dict):
    redis = aioredis.from_url(REDIS_URL, decode_responses=True)
    channel = f"{MESSAGE_CHANNEL_PREFIX}{user_id}"
    await redis.publish(channel, message_data)
    await redis.close()

# Background task: subscribe to all message channels and push to WebSocket
async def redis_subscriber():
    redis = aioredis.from_url(REDIS_URL, decode_responses=True)
    pubsub = redis.pubsub()
    # Subscribe to pattern for all user message channels
    await pubsub.psubscribe(f"{MESSAGE_CHANNEL_PREFIX}*")
    print("Subscribed to Redis message channels.")

    async for message in pubsub.listen():
        if message["type"] == "pmessage":
            channel = message["channel"]
            data = message["data"]
            # Extract user_id from channel name
            if channel.startswith(MESSAGE_CHANNEL_PREFIX):
                user_id = channel[len(MESSAGE_CHANNEL_PREFIX):]
                websocket = active_connections.get(user_id)
                if websocket:
                    try:
                        await websocket.send_text(data)
                    except Exception:
                        unregister_connection(user_id)
    await redis.close()
