#!/usr/bin/env python
import asyncio
import datetime
import json
import httpx
from app.database.session import SessionLocal
from app.models.model import FacebookPageMetrics, SocialMediaAccount
from sqlalchemy.future import select
from app.utils.redis_cache import redis_client
from app.core.config import settings

# Constants
FACEBOOK_GRAPH_API_URL = "https://graph.facebook.com/v16.0"
redis_api_expiry_time = settings.REDIS_CACHE_EXPIRY_SECONDS  # 5 minutes by default
redis_long_expiry_time = settings.REDIS_LONG_CACHE_EXPIRY_SECONDS  # 12 hours by default
timeout = httpx.Timeout(10.0, connect=10.0, read=15.0, write=20.0)

async def update_facebook_metrics():
    """
    Update Facebook metrics for all organizations by fetching real data from the Facebook Graph API.
    This script can be run manually or scheduled to run periodically.
    """
    db = SessionLocal()
    try:
        # Get all Facebook accounts
        result = await db.execute(
            select(SocialMediaAccount)
            .filter(SocialMediaAccount.platform == 'facebook')
            .filter(SocialMediaAccount.login_status == True)
        )
        accounts = result.scalars().all()

        if not accounts:
            print("No active Facebook accounts found")
            return

        print(f"Found {len(accounts)} Facebook accounts to update")

        for account in accounts:
            try:
                # Define date range (last 30 days)
                end_date = datetime.datetime.now(datetime.timezone.utc)
                start_date = end_date - datetime.timedelta(days=30)

                # Fetch metrics from Facebook API
                async with httpx.AsyncClient(timeout=timeout) as client:
                    print(f"Fetching metrics for account: {account.username}")
                    insights_response = await client.get(
                        f"{FACEBOOK_GRAPH_API_URL}/{account.page_id}/insights",
                        params={
                            "access_token": account.page_access_token,
                            "metric": "page_follows,page_impressions,page_impressions_unique,page_post_engagements",
                            "since": start_date.strftime("%Y-%m-%d"),
                            "until": end_date.strftime("%Y-%m-%d"),
                        },
                    )
                    insights_response.raise_for_status()
                    insights = insights_response.json()

                # Extract metrics - using only the specified metrics
                page_follows = next(
                    (
                        item["values"][0]["value"]
                        for item in insights["data"]
                        if item["name"] == "page_follows"
                    ),
                    0,
                )
                page_impressions = next(
                    (
                        item["values"][0]["value"]
                        for item in insights["data"]
                        if item["name"] == "page_impressions"
                    ),
                    0,
                )
                page_impressions_unique = next(
                    (
                        item["values"][0]["value"]
                        for item in insights["data"]
                        if item["name"] == "page_impressions_unique"
                    ),
                    0,
                )
                page_post_engagements = next(
                    (
                        item["values"][0]["value"]
                        for item in insights["data"]
                        if item["name"] == "page_post_engagements"
                    ),
                    0,
                )

                # Create metrics record - using only the specified metrics
                new_metrics = FacebookPageMetrics(
                    organisation_id=account.organisation_id,
                    page_id=account.page_id,
                    page_follows=page_follows,
                    page_impressions=page_impressions,
                    page_impressions_unique=page_impressions_unique,
                    page_post_engagements=page_post_engagements
                )

                # Add metrics to database
                db.add(new_metrics)

                # Update Redis cache - using only the specified metrics
                cache_key = f"fb_overview_{account.organisation_id}"
                cache_data = {
                    "page_follows": page_follows,
                    "page_impressions": page_impressions,
                    "page_impressions_unique": page_impressions_unique,
                    "page_post_engagements": page_post_engagements,
                    "updated_at": datetime.datetime.now(datetime.timezone.utc).isoformat()
                }
                redis_client.setex(cache_key, redis_api_expiry_time, json.dumps(cache_data))
                print(f"Updated metrics for account: {account.username}")

            except Exception as e:
                print(f"Error updating metrics for account {account.username}: {str(e)}")
                continue

        await db.commit()
        print("Facebook metrics updated successfully")
    except Exception as e:
        await db.rollback()
        print(f"Error updating Facebook metrics: {e}")
    finally:
        await db.close()

if __name__ == "__main__":
    asyncio.run(update_facebook_metrics())
