# ChromaDB Storage

This directory contains the ChromaDB database file used for vector storage in the EllumAI backend system.

## Overview

ChromaDB is a vector database that allows for efficient storage and retrieval of vector embeddings, which are essential for AI-powered search and similarity matching. In the EllumAI system, ChromaDB is used to store and query vector embeddings for various AI functionalities.

## Files

- `chroma.sqlite3`: The SQLite database file used by ChromaDB to store vector embeddings and metadata.

## Usage

This directory is mounted as a volume in the Docker Compose configuration to ensure persistence of the vector database across container restarts.

## Integration

ChromaDB is likely integrated with the FastBot service or other AI-related services in the EllumAI backend to provide vector search capabilities.

## Management

No direct management of this directory is typically needed as the ChromaDB client libraries handle the database operations. The database file should not be manually edited.
