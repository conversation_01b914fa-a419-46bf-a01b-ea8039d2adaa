# Deep Ellum Custom AI Agent Service

The Deep Ellum Custom AI Agent Service provides a comprehensive platform for creating, managing, and chatting with custom AI agents using LangChain, LangGraph, and Google Gemini AI.

## Features

- **Custom Agent Creation**: Users can define specialized AI agents with custom descriptions, personalities, and capabilities
- **Sample Internal Agents**: Pre-built agents including:
  - Code Generator: Specialized for programming assistance and code generation
  - Accessibility Advisor: Expert in web accessibility and WCAG compliance
  - Documentation Specialist: Focused on technical writing and documentation
- **Lang<PERSON>hain Integration**: Leverages LangChain for flexible agent orchestration
- **LangGraph Workflows**: Complex agent workflows for advanced capabilities
- **Google Gemini AI**: Powered by Google's advanced language models
- **Conversation Management**: Persistent chat history and conversation threading
- **Agent Customization**: Full CRUD operations for agent management
- **Testing Framework**: Test agent configurations before deployment

## API Endpoints

The service exposes the following endpoints:

### Agent Management
- `POST /api/v1/agents/` - Create a new custom agent
- `GET /api/v1/agents/` - List user's agents and sample agents
- `GET /api/v1/agents/{agent_id}` - Get a specific agent
- `PUT /api/v1/agents/{agent_id}` - Update an existing agent
- `DELETE /api/v1/agents/{agent_id}` - Delete an agent
- `GET /api/v1/agents/templates/` - List agent templates
- `GET /api/v1/agents/samples/info` - Get sample agents information
- `POST /api/v1/agents/test` - Test an agent configuration

### Chat Interface
- `POST /api/v1/chat/` - Send a message to an agent
- `GET /api/v1/chat/conversations` - List user's conversations
- `GET /api/v1/chat/conversations/{conversation_id}` - Get conversation details
- `DELETE /api/v1/chat/conversations/{conversation_id}` - Delete a conversation

### Service Status
- `/health` - Health check endpoint
- `/agent_status` - Service status and features

Full API documentation is available at `/api/v1/agents/docs` when the service is running.

## Environment Variables

Create a `.env` file with the following variables:

```env
# Database
LOCAL_DATABASE_URI=postgresql://postgres:password@localhost:5432/ellum_deep

# Authentication
AUTH_SERVICE_URL=http://localhost:8000/api/v1/auth
SECRET_KEY=your-secret-key-here

# AI Services
GEMINI_API_KEY=your-gemini-api-key
LANGSMITH_API_KEY=your-langsmith-api-key  # Optional
LANGSMITH_PROJECT=deep-ellum-agents

# Redis
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379

# Service Configuration
SERVICE_PORT=8007
DEBUG=false
ENVIRONMENT=development
```

## Installation and Setup

### Local Development

1. **Create virtual environment**:
   ```bash
   cd microservices/deep_ellum
   python -m venv deep_ellum_venv
   source deep_ellum_venv/bin/activate  # On Windows: deep_ellum_venv\Scripts\activate
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Run the service**:
   ```bash
   uvicorn app.main:app --host 0.0.0.0 --port 8007 --reload
   ```

### Docker Deployment

1. **Build the image**:
   ```bash
   docker build -t deep-ellum-agent-service .
   ```

2. **Run the container**:
   ```bash
   docker run -p 8007:8007 --env-file .env deep-ellum-agent-service
   ```

## Usage Examples

### Creating a Custom Agent

```python
import httpx

# Create a custom agent
agent_data = {
    "name": "Marketing Assistant",
    "description": "An AI assistant specialized in marketing strategy and content creation",
    "personality": "Creative, enthusiastic, and data-driven",
    "instructions": "Help users with marketing strategies, content creation, and campaign planning. Always provide actionable insights and creative ideas.",
    "capabilities": ["documentation", "web_search"],
    "is_active": True
}

response = httpx.post(
    "http://localhost:8007/api/v1/agents/",
    json=agent_data,
    headers={"Authorization": "Bearer your-jwt-token"}
)
```

### Chatting with an Agent

```python
# Send a message to an agent
chat_data = {
    "message": "Help me create a social media strategy for a tech startup",
    "agent_id": "agent-uuid-here"
}

response = httpx.post(
    "http://localhost:8007/api/v1/chat/",
    json=chat_data,
    headers={"Authorization": "Bearer your-jwt-token"}
)
```

## Architecture

### Components

1. **Agent Factory**: Creates and manages agent instances
2. **LangChain Service**: Handles LangChain integration and prompt management
3. **Gemini Service**: Manages Google Gemini AI integration
4. **Database Models**: SQLAlchemy models for persistence
5. **API Routes**: FastAPI endpoints for agent and chat operations

### Agent Types

- **LangChain Agents**: Simple conversational agents using LangChain
- **LangGraph Agents**: Complex workflow-based agents using LangGraph
- **Sample Agents**: Pre-built specialized agents for common use cases

### Database Schema

- **CustomAgent**: Stores agent definitions and configurations
- **AgentConversation**: Manages chat conversations
- **ConversationMessage**: Stores individual messages
- **AgentTemplate**: Reusable agent templates

## Sample Agents

### Code Generator
- **Capabilities**: Code generation, review, debugging, testing, documentation
- **Specialization**: Programming assistance across multiple languages
- **Use Cases**: Writing functions, code review, debugging help, architecture advice

### Accessibility Advisor
- **Capabilities**: Accessibility auditing, code review, documentation, testing
- **Specialization**: WCAG compliance and inclusive design
- **Use Cases**: Accessibility audits, compliance guidance, inclusive UX design

### Documentation Specialist
- **Capabilities**: Documentation, code review, testing
- **Specialization**: Technical writing and documentation
- **Use Cases**: API docs, user guides, technical specifications

## Development

### Adding New Sample Agents

1. Create a new agent class in `app/agents/sample_agents.py`
2. Inherit from `LangChainAgent` or `LangGraphAgent`
3. Define capabilities, instructions, and personality
4. Add to the `SAMPLE_AGENTS` registry

### Extending Capabilities

1. Add new capabilities to `AgentCapability` enum in `schemas.py`
2. Implement capability-specific logic in agent classes
3. Update agent factory to handle new capabilities

### Testing

```bash
# Run tests
pytest

# Run with coverage
pytest --cov=app tests/
```

## Monitoring and Logging

The service includes comprehensive logging and can be monitored through:
- Health check endpoint (`/health`)
- Service status endpoint (`/agent_status`)
- Application logs
- Database query logs (when DEBUG=true)

## Authentication and Authorization

The Deep Ellum Agent Service implements organization-dependent authentication and authorization following the same patterns as other microservices in the system.

### Authentication
- **JWT Token Required**: All endpoints require a valid JWT token
- **Organization Context**: Users must be authenticated within an organization context
- **Token Validation**: Tokens are validated using the authentication service

### Authorization
The service implements role-based permissions:

#### Permissions
- `view_agents`: View agents and sample agents
- `create_agent`: Create new custom agents
- `update_agent`: Update existing agents
- `delete_agent`: Delete agents (soft delete)
- `chat_with_agent`: Chat with agents
- `view_conversations`: View conversation history
- `delete_conversation`: Delete conversations
- `view_templates`: View agent templates
- `test_agent`: Test agent configurations

#### Roles
- **Viewer**: Can view agents, chat, and view conversations
- **Editor**: Can create, update agents, and manage conversations
- **Admin/Owner**: Full access to all features

### Organization Isolation
- **Agent Isolation**: Custom agents are isolated by organization
- **Sample Agents**: Global sample agents are available to all organizations
- **Conversation Isolation**: Users can only access their own conversations within their organization
- **Data Security**: All data access is filtered by organization membership

## Security Considerations

- JWT token authentication required for all endpoints
- Organization-based data isolation
- Role-based permission checking
- Soft deletes for data retention endpoints
- User isolation for agent access
- Input validation and sanitization
- Rate limiting (recommended for production)
- Environment variable protection

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is part of the EllumAI backend system.
