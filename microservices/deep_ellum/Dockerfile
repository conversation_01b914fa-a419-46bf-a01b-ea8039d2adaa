# Use the official Python image from the Docker Hub
FROM python:3.12-alpine

# Set the working directory
WORKDIR /app

# Copy the requirements file into the container
COPY requirements.txt ./

# Install system dependencies and Python packages
RUN apk add --no-cache curl gcc musl-dev libffi-dev && \
    pip install --no-cache-dir -r requirements.txt && \
    apk del gcc musl-dev libffi-dev

# Copy the content of the local src directory to the working directory
COPY . .

# Create a directory for server logs
RUN mkdir -p /app/logs

# Expose the port
EXPOSE 8007

# Command to run the application
CMD ["uvicorn", "--workers", "3", "--host", "0.0.0.0", "--port", "8007", "app.main:app"]
