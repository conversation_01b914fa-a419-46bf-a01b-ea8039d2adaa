from pydantic_settings import BaseSettings
from typing import Optional


class Settings(BaseSettings):
    """Application configuration settings."""
    
    # Database
    LOCAL_DATABASE_URI: str = "postgresql://postgres:password@localhost:5432/ellum_deep"
    PROD_DATABASE_URI: Optional[str] = None
    
    # Authentication
    AUTH_SERVICE_URL: str = "http://localhost:8000/api/v1/auth"
    SECRET_KEY: str
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # AI Services
    GEMINI_API_KEY: str
    LANGSMITH_API_KEY: Optional[str] = None
    LANGSMITH_PROJECT: str = "deep-ellum-agents"
    
    # Redis
    REDIS_URL: str = "redis://localhost:6379"
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    
    # Service Configuration
    SERVICE_NAME: str = "deep-ellum-agent-service"
    SERVICE_PORT: int = 8007
    DEBUG: bool = False
    
    # Environment
    ENVIRONMENT: str = "development"
    
    class Config:
        env_file = ".env"
        case_sensitive = True


settings = Settings()
