import httpx
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Depends, status
from fastapi.security import OA<PERSON><PERSON><PERSON><PERSON>wordBearer
from app.core.config import settings
from app.utils.logger import get_logger
from typing import Optional, Dict, Any, Annotated
from jose import JWTError, jwt

logger = get_logger(__name__)

# Use OAuth2PasswordBearer like other services
oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.AUTH_SERVICE_URL}/login")


async def get_current_user(token: Annotated[str, Depends(oauth2_scheme)]) -> Dict[str, Any]:
    """Get current authenticated user from JWT token."""
    credentials_exception = HTTPException(
        status_code=401,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        user_id: str = payload.get("user_id")
        if user_id is None:
            raise credentials_exception
        user = payload
    except JWTError:
        raise credentials_exception
    return {"decoded": payload, "raw_token": token}


async def get_user_id(user_data: Dict[str, Any] = Depends(get_current_user)) -> str:
    """Extract user ID from authenticated user data."""
    user_id = user_data["decoded"].get("user_id")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User ID not found in token"
        )
    return str(user_id)


async def verify_organization(organisation_id: str) -> str:
    """
    Verify the organization by making an HTTP request to the authentication service.

    Args:
        organisation_id (str): The ID of the organization to verify.

    Returns:
        str: The verified organization ID.
    """
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{settings.AUTH_SERVICE_URL}/org_details/{organisation_id}"
            )

            if response.status_code == 200:
                return organisation_id
            else:
                logger.warning(f"Organization verification failed: {response.status_code}")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Organization not found"
                )
    except httpx.RequestError as e:
        logger.error(f"Auth service request error: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Authentication service unavailable"
        )


async def get_organization_id(user_data: Dict[str, Any] = Depends(get_current_user)) -> str:
    """Extract organization ID from authenticated user data."""
    org_id = user_data["decoded"].get("organisation_id")
    if not org_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Organization ID not found in token"
        )
    return str(org_id)


async def fetch_user_permissions(user_id: str, organisation_id: str) -> Dict[str, Any]:
    """Fetch user permissions from authentication service."""
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{settings.AUTH_SERVICE_URL}/user/{user_id}/permissions",
                params={"organisation_id": organisation_id}
            )

            if response.status_code == 200:
                return response.json()
            else:
                logger.warning(f"Failed to fetch permissions: {response.status_code}")
                return {"permissions": [], "role": ""}
    except httpx.RequestError as e:
        logger.error(f"Auth service request error: {e}")
        return {"permissions": [], "role": ""}


async def check_permissions(user_id: str, organisation_id: str, required_permission: str) -> str:
    """Check if user has required permission."""
    try:
        # Fetch the user permissions
        user_permissions_dict = await fetch_user_permissions(user_id, organisation_id)
        user_permissions = user_permissions_dict.get("permissions", [])
        user_role = user_permissions_dict.get("role", "")

        if (user_role != "admin" and user_role != "owner") and required_permission not in user_permissions:
            raise HTTPException(
                status_code=403,
                detail=f"User does not have permission to access this endpoint: {required_permission}",
            )
        return user_role
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error fetching user permissions: {e}")
        raise HTTPException(status_code=500, detail="Error fetching user permissions")
