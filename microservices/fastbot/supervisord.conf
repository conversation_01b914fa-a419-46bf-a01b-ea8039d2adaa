[supervisord]
nodaemon=true

[program:fastbot_service]
command=uvicorn app.main:app --host 0.0.0.0 --port 8001
autostart=true
autorestart=true
stderr_logfile=/var/log/fastapi.err.log
stdout_logfile=/var/log/fastapi.out.log
user=root

[program:celery]
command=celery -A app.utils.celery_app worker --loglevel=info
autostart=true
autorestart=true
stderr_logfile=/var/log/celery.err.log
stdout_logfile=/var/log/celery.out.log
user=root


[program:flower]
command=celery -A app.worker flower --port=5556
autostart=true
autorestart=true
stderr_logfile=/var/log/flower.err.log
stdout_logfile=/var/log/flower.out.log
user=root
