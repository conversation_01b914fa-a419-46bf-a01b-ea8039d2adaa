from typing import List
from urllib.parse import urlparse, urlunparse
import validators
import httpx
from app.utils.logger import get_logger

logger = get_logger(__name__)

def is_valid_url(url: str) -> bool:
    parsed = urlparse(url)
    return parsed.scheme in {"http", "https"} and bool(parsed.netloc)

def is_internal_url(url: str, base_url: str) -> bool:
    base_domain = urlparse(base_url).netloc.lower()
    return urlparse(url).netloc.lower() == base_domain

def normalize_url(url: str) -> str:
    parsed = urlparse(url)
    scheme = parsed.scheme.lower()
    netloc = parsed.netloc.lower()
    path = parsed.path.rstrip('/')
    return urlunparse((scheme, netloc, path, '', '', ''))

async def check_urls(urls: List[str]) -> List[str]:
    """Validate and check if URLs are live."""
    valid_urls = []
    async with httpx.AsyncClient() as client:
        for url in urls:
            if not validators.url(url):
                logger.warning("Invalid URL format: %s", url)
                continue
            try:
                response = await client.head(url, timeout=5, follow_redirects=True)
                if response.status_code < 400:
                    valid_urls.append(url)
                else:
                    logger.warning("Unreachable URL: %s (Status: %d)", url, response.status_code)
            except httpx.RequestError as e:
                logger.warning("Failed to reach URL: %s (Error: %s)", url, str(e))
    return valid_urls