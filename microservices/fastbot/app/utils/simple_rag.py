"""
Simple RAG System
A streamlined RAG implementation that focuses on accurate retrieval without complex fallbacks.
Replaces the fragmented multi-system approach with a single, reliable solution.
"""

import asyncio
import uuid
from typing import Dict, List, Any, Optional
from pathlib import Path

from app.utils.logger import get_logger
from app.utils.file_extraction import extract_text
from app.gen_models.gemini_model import get_embedding
from app.utils.qdrant_utils import qdrant_client, ensure_qdrant_collection
from qdrant_client.models import PointStruct, Filter, FieldCondition, MatchValue

logger = get_logger(__name__)


class SimpleRAGProcessor:
    """
    COMPLETE DOCUMENT RAG processor that stores entire documents without chunking.
    Focuses on preserving full document context for comprehensive retrieval.
    """

    def __init__(self, organization_id: str):
        self.organization_id = organization_id
        self.collection_name = ensure_qdrant_collection(organization_id)
        self.max_context_length = 32000  # Much larger context for full documents
        
    async def process_document(
        self,
        file_content: bytes,
        filename: str,
        user_id: str,
        username: str
    ) -> Dict[str, Any]:
        """
        Process a document by storing the ENTIRE document as a single embedding.
        No chunking, no filtering - complete document context preservation.
        """
        try:
            logger.info(f"Processing COMPLETE document {filename} with full context RAG")

            # Step 1: Extract ALL text content
            text_content = await self._extract_text_content(file_content, filename)

            if not text_content or len(text_content.strip()) < 10:
                raise ValueError(f"Document {filename} contains no readable text content")

            logger.info(f"Extracted {len(text_content)} characters from {filename}")

            # Step 2: Generate embedding for ENTIRE document
            document_embedding = await get_embedding(text_content)

            # Step 3: Store complete document in Qdrant
            doc_id = str(uuid.uuid4())
            storage_result = await self._store_complete_document(
                doc_id, text_content, document_embedding, filename, user_id, username
            )

            logger.info(f"Successfully stored COMPLETE document {filename}: {len(text_content)} characters")

            return {
                "document_id": doc_id,
                "total_text_length": len(text_content),
                "processing_engine": "complete_document_rag",
                "storage_result": storage_result
            }

        except Exception as e:
            logger.error(f"Error processing document {filename}: {e}")
            raise
    
    async def _extract_text_content(self, file_content: bytes, filename: str) -> str:
        """Extract text using existing reliable extraction methods."""
        try:
            # Create a mock file object for the extraction function
            class MockFile:
                def __init__(self, content: bytes, name: str):
                    self.content = content
                    self.filename = name
                    self.file = self._create_file_obj()

                def _create_file_obj(self):
                    class FileObj:
                        def __init__(self, content):
                            self.content = content
                            self.position = 0

                        def read(self):
                            return self.content

                        def seek(self, position):
                            self.position = position

                    return FileObj(self.content)

            mock_file = MockFile(file_content, filename)
            text_content = extract_text(mock_file)

            if not text_content:
                raise ValueError("No text content extracted from document")

            return text_content.strip()

        except Exception as e:
            logger.error(f"Error extracting text from {filename}: {e}")
            raise
    
    async def _store_complete_document(
        self,
        doc_id: str,
        full_text: str,
        embedding: List[float],
        filename: str,
        user_id: str,
        username: str
    ) -> Dict[str, Any]:
        """Store the complete document as a single point in Qdrant."""
        try:
            metadata = {
                "organization_id": self.organization_id,
                "document_id": doc_id,
                "filename": filename,
                "full_text": full_text,  # Store COMPLETE document text
                "text_length": len(full_text),
                "user_id": user_id,
                "username": username,
                "document_type": "complete_document",
                "processing_engine": "complete_document_rag"
            }

            point = PointStruct(
                id=doc_id,
                vector=embedding,
                payload=metadata
            )

            # Store complete document in Qdrant
            qdrant_client.upsert(
                collection_name=self.collection_name,
                points=[point]
            )

            logger.info(f"Stored complete document {filename} with {len(full_text)} characters")

            return {
                "document_stored": True,
                "document_id": doc_id,
                "text_length": len(full_text),
                "collection": self.collection_name
            }

        except Exception as e:
            logger.error(f"Error storing complete document in Qdrant: {e}")
            raise

    async def retrieve_context(self, query: str, top_k: int = 5) -> str:
        """
        Retrieve COMPLETE document context for a query.
        Returns full documents without filtering or thresholds.
        """
        try:
            logger.info(f"Retrieving COMPLETE document context for query: '{query}' in org: {self.organization_id}")
            logger.info(f"Collection name: {self.collection_name}")

            # Generate query embedding
            query_embedding = await get_embedding(query)
            logger.info(f"Query embedding generated: {len(query_embedding)} dimensions")

            # Search in Qdrant - NO FILTERING, get all relevant documents
            organization_filter = Filter(
                must=[
                    FieldCondition(
                        key="organization_id",
                        match=MatchValue(value=self.organization_id)
                    )
                ]
            )

            search_results = qdrant_client.search(
                collection_name=self.collection_name,
                query_vector=query_embedding,
                query_filter=organization_filter,
                limit=top_k,
                with_payload=True
            )

            logger.info(f"Search results: {len(search_results)} documents found")

            if not search_results:
                logger.info("No documents found in collection")
                return ""

            # Extract COMPLETE document text - NO SCORE FILTERING
            context_parts = []
            for result in search_results:
                full_text = result.payload.get("full_text", "")
                filename = result.payload.get("filename", "Unknown")
                score = result.score

                logger.info(f"Document found: score={score:.3f}, filename={filename}, text_length={len(full_text)}")

                # Include ALL documents regardless of score - user wants complete context
                if full_text:
                    context_parts.append(f"=== Document: {filename} ===\n{full_text}")

            if not context_parts:
                logger.info("No document text found in search results")
                return ""

            # Join ALL documents with clear separators
            final_context = "\n\n" + "="*50 + "\n\n".join(context_parts)

            # NO TRUNCATION - return complete context regardless of length
            logger.info(f"Retrieved COMPLETE context: {len(final_context)} characters from {len(context_parts)} documents")
            return final_context

        except Exception as e:
            logger.error(f"Error retrieving context for query '{query}': {e}")
            return ""


# Global instances for easy access
_rag_processors = {}

def get_simple_rag_processor(organization_id: str) -> SimpleRAGProcessor:
    """Get or create a simple RAG processor instance for an organization."""
    if organization_id not in _rag_processors:
        _rag_processors[organization_id] = SimpleRAGProcessor(organization_id)
    return _rag_processors[organization_id]


async def process_document_simple_rag(
    organization_id: str,
    file_content: bytes,
    filename: str,
    user_id: str,
    username: str
) -> Dict[str, Any]:
    """
    Process a document using the simple RAG system.
    """
    processor = get_simple_rag_processor(organization_id)
    return await processor.process_document(file_content, filename, user_id, username)


async def get_context_simple_rag(
    organization_id: str,
    query: str,
    top_k: int = 10
) -> str:
    """
    Get context using simple RAG system.
    STRICT ORGANIZATION ISOLATION - only retrieves from the exact organization ID.
    """
    # Use ONLY the exact organization ID - no variations or fallbacks
    processor = get_simple_rag_processor(organization_id)
    context = await processor.retrieve_context(query, top_k)

    if context:
        logger.info(f"Found context for organization: {organization_id}")
        return context
    else:
        logger.info(f"No context found for organization: {organization_id}")
        return ""
