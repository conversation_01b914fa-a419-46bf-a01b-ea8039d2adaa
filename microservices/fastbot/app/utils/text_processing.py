import asyncio
from datetime import datetime
from pathlib import Path
from typing import List

from app.core.config import settings
from app.database.database import Session<PERSON>oc<PERSON>
from app.models.models import UploadedFile
from app.utils.file_extraction import (
    extract_text_from_pdf, extract_text_from_doc,
    extract_text_from_excel, extract_text_from_image, extract_text_from_txt
)
from app.utils.logger import get_logger
from fastapi import UploadFile

logger = get_logger(__name__)


async def extract_text_fast(file_content: bytes, filename: str) -> str:
    """Fast text extraction using existing optimized methods."""
    file_ext = Path(filename).suffix.lower()

    try:
        if file_ext == '.pdf':
            return await asyncio.to_thread(extract_text_from_pdf, file_content)
        elif file_ext in ['.doc', '.docx']:
            return await asyncio.to_thread(extract_text_from_doc, file_content)
        elif file_ext in ['.xls', '.xlsx']:
            return await asyncio.to_thread(extract_text_from_excel, file_content)
        elif file_ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
            return await asyncio.to_thread(extract_text_from_image, file_content)
        elif file_ext in ['.txt', '.md']:  # Add markdown support
            return await asyncio.to_thread(extract_text_from_txt, file_content)
        else:
            # Fallback for unsupported formats
            return f"Unsupported file format: {file_ext}"

    except Exception as e:
        logger.error(f"Error extracting text from {filename}: {e}")
        raise


def chunk_text(text: str, chunk_size: int = 1500) -> List[str]:
    """Split text into optimized chunks for better context retention and retrieval."""
    chunks = []
    start = 0
    text_length = len(text)

    while start < text_length:
        end = start + chunk_size
        if end >= text_length:
            chunks.append(text[start:].strip())
            break

        chunk = text[start:end]
        # Try to break at code block, paragraph, or sentence boundary.
        code_block = chunk.rfind("```")
        if code_block != -1 and code_block > chunk_size * 0.3:
            end = start + code_block
        elif "\n\n" in chunk:
            last_break = chunk.rfind("\n\n")
            if last_break > chunk_size * 0.3:
                end = start + last_break
        elif ". " in chunk:
            last_period = chunk.rfind(". ")
            if last_period > chunk_size * 0.3:
                end = start + last_period + 1
        chunk = text[start:end].strip()
        if chunk:
            chunks.append(chunk)
        start = max(start + 1, end)
    return chunks


async def process_file_and_ingest_with_metadata_async(
    organisation_id: str, user_id: str, username: str, file_content: bytes, filename: str
) -> str:
    """
    FAST and EFFICIENT async file processing that captures whole documents.
    Uses the proven FastDocumentProcessor for speed and completeness.
    """
    logger.info(f"🚀 Starting FAST processing of {filename} ({len(file_content)} bytes)")

    try:
        import time
        start_time = time.time()

        # Use simple RAG processing as primary method
        try:
            from app.utils.simple_rag import process_document_simple_rag

            result = await process_document_simple_rag(
                organisation_id, file_content, filename, user_id, username
            )

            # Store all chunks in Qdrant
            from app.utils.qdrant_utils import add_chunked_document_to_qdrant
            await add_chunked_document_to_qdrant(organisation_id, result["chunks"])

            processing_time = time.time() - start_time
            logger.info(f"✅ Fast processing completed in {processing_time:.2f}s - stored {len(result['chunks'])} chunks")

            # Store file metadata in database
            db = SessionLocal()
            try:
                new_file = UploadedFile(
                    id=result["document_id"],
                    filename=filename,
                    filesize=len(file_content),
                    filetype=filename.split(".")[-1] if "." in filename else "unknown",
                    user_id=user_id,
                    username=username,
                    organisation_id=organisation_id,
                    summary=result["summary"],
                    upload_date=datetime.utcnow(),
                    timestamp=int(datetime.utcnow().timestamp()),
                )
                db.add(new_file)
                db.commit()
                db.refresh(new_file)

                logger.info(f"Successfully processed and stored {filename} with ID {result['document_id']}")

            except Exception as e:
                logger.error("Error storing file metadata: %s", e)
                db.rollback()
                raise
            finally:
                db.close()

            return result["document_id"]

        except Exception as fast_error:
            logger.error(f"Fast processing failed for {filename}: {fast_error}")
            # No fallbacks - raise the error to ensure we fix any issues
            raise fast_error

    except Exception as e:
        logger.error(f"Error in file processing for {filename}: {e}")
        raise e


def process_file_and_ingest_with_metadata_sync(
    organisation_id: str, user_id: str, username: str, file: UploadFile
) -> str:
    """
    Synchronous wrapper for the enhanced async processing.
    Maintains backward compatibility while using new Docling capabilities.
    """
    # Read file content
    file_content = file.file.read()
    file.file.seek(0)  # Reset file pointer

    # Run the async processing
    return asyncio.run(
        process_file_and_ingest_with_metadata_async(
            organisation_id, user_id, username, file_content, file.filename
        )
    )
