import io
import json
import asyncio
import redis
from fastapi.datastructures import UploadFile
from app.utils.logger import get_logger
from app.utils.celery_app import celery_app
from app.routers.scrape import scrape_urls_task
from app.utils.text_processing import process_file_and_ingest_with_metadata_sync, process_file_and_ingest_with_metadata_async
from app.utils.simple_rag import process_document_simple_rag
from app.database.database import SessionLocal
from app.models.models import UploadedFile
from datetime import datetime
from app.utils.qdrant_utils import add_org_info_to_qdrant, update_org_info_in_qdrant
from app.core.config import settings

logger = get_logger(__name__)

redis_client = redis.Redis.from_url(settings.REDIS_URL, encoding="utf8", decode_responses=True)

def publish_notification(organisation_id: str, task_name: str, status: str, extra: dict = None):
    """Publish a JSON notification to the Redis channel for the given organisation."""
    channel = f"notifications:{organisation_id}"
    payload = {
        "task": task_name,
        "status": status,
        "organisation_id": organisation_id,
    }
    if extra:
        payload.update(extra)
    
    try:
        redis_client.publish(channel, json.dumps(payload))
        logger.info("Published notification on channel %s: %s", channel, payload)
    except Exception as e:
        logger.error("Error publishing notification: %s", e)

@celery_app.task(bind=True)
def process_file_and_ingest_with_metadata_task(self, organisation_id: str, user_id: str, username: str, file_content: bytes, filename: str):
    """
    LIGHTNING-FAST Celery task for document processing with chunked storage.
    Uses optimized extraction methods with NO model downloads for maximum speed.
    """
    import time

    publish_notification(organisation_id, "process_file_and_ingest_with_metadata", "uploading", {"filename": filename})

    try:
        publish_notification(organisation_id, "process_file_and_ingest_with_metadata", "pending", {"filename": filename})

        # Use simple time tracking instead of event loop time
        start_time = time.time()

        # Use simple RAG processing for reliable results
        logger.info(f"Using simple RAG processing for {filename}")
        result = asyncio.run(
            process_document_simple_rag(
                organisation_id, file_content, filename, user_id, username
            )
        )

        # Store file metadata in database
        db = SessionLocal()
        try:
            new_file = UploadedFile(
                id=result["document_id"],
                filename=filename,
                filesize=len(file_content),
                filetype=filename.split(".")[-1] if "." in filename else "unknown",
                user_id=user_id,
                username=username,
                organisation_id=organisation_id,
                summary=f"Document processed with {result['total_chunks']} chunks",
                upload_date=datetime.utcnow(),
                timestamp=int(datetime.utcnow().timestamp()),
            )
            db.add(new_file)
            db.commit()
            db.refresh(new_file)

            logger.info(f"Successfully stored file metadata for {filename} with ID {result['document_id']}")

        except Exception as e:
            logger.error("Error storing file metadata: %s", e)
            db.rollback()
            # Don't raise here - the file processing was successful, just metadata storage failed
        finally:
            db.close()

        processing_time = time.time() - start_time

        publish_notification(
            organisation_id,
            "process_file_and_ingest_with_metadata",
            "updated",
            {
                "filename": filename,
                "result": result,
                "processing_engine": "simple_rag",
                "processing_time": f"{processing_time:.2f}s",
                "status": "completed"
            }
        )

        logger.info(f"🚀 Successfully processed {filename} in {processing_time:.2f}s - Document ID: {result}")
        return result

    except Exception as e:
        logger.error("Error in fast processing: %s", e)
        publish_notification(
            organisation_id,
            "process_file_and_ingest_with_metadata",
            "failed",
            {
                "filename": filename,
                "error": str(e),
                "processing_engine": "simple_rag"
            }
        )
        raise

@celery_app.task(bind=True)
def scrape_urls_celery(self, token, organisation_id: str, urls: list, max_concurrent: int = 5):
    """Celery task to run the asynchronous URL scraping task with status updates."""
    publish_notification(organisation_id, "scrape_urls", "uploading", {"urls_count": len(urls)})

    try:
        publish_notification(organisation_id, "scrape_urls", "pending", {"urls_count": len(urls)})
        
        asyncio.run(scrape_urls_task(token=token, organization_id=organisation_id, urls=urls, max_concurrent=max_concurrent))

        publish_notification(organisation_id, "scrape_urls", "updated", {"urls_count": len(urls)})
    except Exception as e:
        logger.error("Error scraping URLs: %s", e)
        publish_notification(organisation_id, "scrape_urls", "failed", {"error": str(e)})
        raise

@celery_app.task(bind=True)
def add_org_info_task(self, organisation_id: str, org_info: dict):
    """Task to add organization info to Qdrant with error handling."""
    publish_notification(organisation_id, "add_org_info", "uploading")

    try:
        add_org_info_to_qdrant(organisation_id, org_info)
        publish_notification(organisation_id, "add_org_info", "updated", {"org_info": org_info})
    except Exception as e:
        logger.error("Error adding organization info: %s", e)
        publish_notification(organisation_id, "add_org_info", "failed", {"error": str(e)})
        raise

@celery_app.task(bind=True)
def edit_org_info_task(self, organisation_id: str, org_info: dict):
    """Task to update the organization info in Qdrant with improved status updates."""
    publish_notification(organisation_id, "edit_org_info", "uploading")

    try:
        update_org_info_in_qdrant(organisation_id, org_info)
        publish_notification(organisation_id, "edit_org_info", "updated", {"org_info": org_info})
    except Exception as e:
        logger.error("Error updating organization info: %s", e)
        publish_notification(organisation_id, "edit_org_info", "failed", {"error": str(e)})
        raise
