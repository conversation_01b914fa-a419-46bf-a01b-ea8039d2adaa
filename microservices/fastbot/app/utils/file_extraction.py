import io

import docx  # python-docx
import fitz  # PyMuPDF
import pandas as pd
import pytesseract
from app.utils.logger import get_logger
from fastapi import HTTPException
from PIL import Image

logger = get_logger(__name__)


def extract_text_from_pdf(file_bytes: bytes) -> str:
    """
    Enhanced PDF text extraction that captures all pages and content.
    Uses PyMuPDF to extract text from each page individually to ensure
    no content is lost, especially from the last pages.
    """
    doc = None
    try:
        doc = fitz.open(stream=file_bytes, filetype="pdf")
        full_text = []
        page_count = len(doc)  # Get page count before processing

        # Extract text from each page individually to ensure complete capture
        for page_num in range(page_count):
            page = doc.load_page(page_num)
            page_text = page.get_text()

            # Add page separator for better chunking later
            if page_text.strip():
                full_text.append(f"--- Page {page_num + 1} ---\n{page_text}")

        # Join all pages with double newlines for clear separation
        result = "\n\n".join(full_text)

        logger.info(f"Extracted text from {page_count} pages, total length: {len(result)} characters")

        return result

    except Exception as e:
        logger.error("Error extracting PDF: %s", e)
        raise
    finally:
        # Ensure document is always closed
        if doc is not None:
            try:
                doc.close()
            except:
                pass  # Ignore errors when closing


def extract_text_from_doc(file_bytes: bytes) -> str:
    """
    Enhanced DOCX text extraction that captures all content including tables and headers.
    """
    try:
        document = docx.Document(io.BytesIO(file_bytes))
        full_text = []

        # Extract paragraphs
        for para in document.paragraphs:
            if para.text.strip():
                full_text.append(para.text)

        # Extract tables
        for table in document.tables:
            table_text = []
            for row in table.rows:
                row_text = []
                for cell in row.cells:
                    if cell.text.strip():
                        row_text.append(cell.text.strip())
                if row_text:
                    table_text.append(" | ".join(row_text))
            if table_text:
                full_text.append("TABLE:\n" + "\n".join(table_text))

        result = "\n\n".join(full_text)
        logger.info(f"Extracted text from DOCX: {len(result)} characters")
        return result

    except Exception as e:
        logger.error("Error extracting DOCX: %s", e)
        raise


def extract_text_from_excel(file_bytes: bytes) -> str:
    """
    Enhanced Excel text extraction that captures all sheets and preserves structure.
    """
    try:
        # Read all sheets
        excel_file = pd.ExcelFile(io.BytesIO(file_bytes))
        all_sheets_text = []

        for sheet_name in excel_file.sheet_names:
            df = pd.read_excel(excel_file, sheet_name=sheet_name)

            if not df.empty:
                # Convert to string and preserve structure
                sheet_text = f"SHEET: {sheet_name}\n"
                sheet_text += df.to_string(index=False, na_rep='')
                all_sheets_text.append(sheet_text)

        result = "\n\n".join(all_sheets_text)
        logger.info(f"Extracted text from Excel ({len(excel_file.sheet_names)} sheets): {len(result)} characters")
        return result

    except Exception as e:
        logger.error("Error extracting Excel: %s", e)
        raise


def extract_text_from_image(file_bytes: bytes) -> str:
    try:
        image = Image.open(io.BytesIO(file_bytes))
        return pytesseract.image_to_string(image)
    except Exception as e:
        logger.error("Error performing OCR on image: %s", e)
        raise


def extract_text_from_txt(file_bytes: bytes) -> str:
    try:
        return file_bytes.decode("utf-8")
    except Exception as e:
        logger.error("Error extracting TXT: %s", e)
        raise


def extract_text(file) -> str:
    ext = file.filename.split(".")[-1].lower()
    file_bytes = file.file.read()
    file.file.seek(0)

    if ext == "pdf":
        return extract_text_from_pdf(file_bytes)
    elif ext in ["doc", "docx"]:
        return extract_text_from_doc(file_bytes)
    elif ext in ["xls", "xlsx"]:
        return extract_text_from_excel(file_bytes)
    elif ext in ["jpg", "jpeg", "png", "bmp"]:
        return extract_text_from_image(file_bytes)
    elif ext == "txt":
        return extract_text_from_txt(file_bytes)
    else:
        logger.error("Unsupported file type: %s", ext)
        raise HTTPException(status_code=400, detail="Unsupported file type")
