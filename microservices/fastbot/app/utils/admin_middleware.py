"""
Admin verification middleware for agentic database access.
Only admin users can access cross-database query capabilities.
"""

from typing import Dict, Any
from fastapi import HTTPException, Depends
from app.utils.dependency import get_current_user
from app.utils.external_calls import get_user_info_cached
from app.utils.logger import get_logger

logger = get_logger(__name__)


async def verify_admin_access(
    organization_id: str,
    token: Dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Verify that the current user has admin access for agentic database queries.
    
    Args:
        organization_id: The organization ID to verify admin access for
        token: The decoded JWT token from get_current_user
    
    Returns:
        Dict containing user info and role verification
    
    Raises:
        HTTPException: If user is not an admin or owner
    """
    try:
        user_id = token["decoded"].get("user_id")
        if not user_id:
            raise HTTPException(
                status_code=401,
                detail="Invalid token: user ID not found"
            )
        
        # Get user info including roles and permissions
        user_info = await get_user_info_cached(organization_id, user_id)
        
        if "error" in user_info:
            raise HTTPException(
                status_code=404,
                detail="User not found in organization"
            )
        
        # Extract user role from the response
        user_role = None
        if "role" in user_info:
            user_role = user_info["role"]
        elif "data" in user_info and "role" in user_info["data"]:
            user_role = user_info["data"]["role"]
        
        # Check if user has admin or owner role
        if 'admin' or 'owner' not in user_role:
            logger.warning(f"User {user_id} attempted agentic database access without admin privileges. Role: {user_role}")
            raise HTTPException(
                status_code=403,
                detail="Access denied. Only admin and owner users can access cross-database analytics."
            )
        
        logger.info(f"Admin access verified for user {user_id} with role {user_role} in organization {organization_id}")
        
        return {
            "user_id": user_id,
            "user_role": user_role,
            "organization_id": organization_id,
            "user_info": user_info
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error verifying admin access: {e}")
        raise HTTPException(
            status_code=500,
            detail="Error verifying admin access"
        )


async def check_agentic_permissions(
    organization_id: str,
    token: Dict = Depends(get_current_user)
) -> bool:
    """
    Quick check if user has permissions for agentic database access.
    
    Args:
        organization_id: The organization ID
        token: The decoded JWT token
    
    Returns:
        bool: True if user has admin/owner permissions
    """
    try:
        admin_info = await verify_admin_access(organization_id, token)
        return admin_info["user_role"] in ["admin", "owner"]
    except HTTPException:
        return False
    except Exception as e:
        logger.error(f"Error checking agentic permissions: {e}")
        return False


def get_admin_context(admin_info: Dict[str, Any]) -> str:
    """
    Generate context string for the AI agent about the admin user.
    
    Args:
        admin_info: Admin verification info from verify_admin_access
    
    Returns:
        str: Context string for the AI agent
    """
    user_role = admin_info.get("user_role", "unknown")
    organization_id = admin_info.get("organization_id", "unknown")
    
    context = f"""
You are responding to an {user_role} user in organization {organization_id}.
This user has administrative privileges and can access cross-database analytics.
You have access to query data from the following databases:
- Social media metrics (Facebook, Instagram, Twitter)
- User authentication and organization data
- Chat and conversation analytics
- Settings and configuration data
- Payment and subscription information

When providing analytics, be comprehensive but focus on actionable insights.
Always respect data privacy and only show aggregated metrics unless specifically requested.
"""
    
    return context.strip()


class AdminAccessRequired:
    """Dependency class for admin access verification."""
    
    def __init__(self, require_owner: bool = False):
        self.require_owner = require_owner
    
    async def __call__(
        self,
        organization_id: str,
        token: Dict = Depends(get_current_user)
    ) -> Dict[str, Any]:
        """
        Verify admin access with optional owner requirement.
        
        Args:
            organization_id: The organization ID
            token: The decoded JWT token
        
        Returns:
            Dict containing admin verification info
        """
        admin_info = await verify_admin_access(organization_id, token)
        
        if self.require_owner and admin_info["user_role"] != "owner":
            raise HTTPException(
                status_code=403,
                detail="Access denied. Owner privileges required for this operation."
            )
        
        return admin_info


# Dependency instances
admin_required = AdminAccessRequired(require_owner=False)
owner_required = AdminAccessRequired(require_owner=True)
