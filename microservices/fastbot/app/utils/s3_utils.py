import asyncio
import aiohttp
import boto3
from botocore.exceptions import ClientError
from io import BytesIO
from datetime import datetime
import uuid
from app.core.config import settings
from app.utils.logger import get_logger

logger = get_logger(__name__)

# Configure S3 client
s3_client = boto3.client(
    's3',
    aws_access_key_id=settings.AWS_S3_ACCESS_KEY,
    aws_secret_access_key=settings.AWS_S3_SECRET_ACCESS_KEY,
    region_name=settings.AWS_S3_REGION
)


async def download_image_from_url(image_url: str) -> BytesIO:
    """
    Download an image from a URL and return it as BytesIO object.

    Args:
        image_url (str): The URL of the image to download

    Returns:
        BytesIO: The image data as a BytesIO object

    Raises:
        Exception: If the image download fails
    """
    try:
        if not image_url:
            raise ValueError("Image URL is empty or None")

        logger.info(f"Starting download from URL: {image_url[:100]}...")

        # Set longer timeout for large image downloads
        timeout = aiohttp.ClientTimeout(
            total=60,  # Total timeout: 60 seconds
            connect=10,  # Connection timeout: 10 seconds
            sock_read=30  # Socket read timeout: 30 seconds
        )

        # Add retry logic for transient failures
        max_retries = 3
        for attempt in range(max_retries):
            try:
                logger.info(f"Download attempt {attempt + 1}/{max_retries}")

                async with aiohttp.ClientSession(timeout=timeout) as session:
                    async with session.get(image_url) as response:
                        logger.info(f"Download response status: {response.status}")

                        if response.status == 200:
                            # Read the image data with progress logging
                            content_length = response.headers.get('content-length')
                            if content_length:
                                logger.info(f"Expected download size: {content_length} bytes")

                            image_data = await response.read()
                            logger.info(f"Successfully downloaded {len(image_data)} bytes")

                            if len(image_data) == 0:
                                raise ValueError("Downloaded image data is empty")

                            return BytesIO(image_data)
                        elif response.status == 403:
                            raise ValueError("Access denied to image URL - the image may have expired")
                        elif response.status == 404:
                            raise ValueError("Image not found at the provided URL")
                        elif response.status >= 500:
                            raise ValueError(f"Server error downloading image: HTTP {response.status}")
                        else:
                            raise ValueError(f"Failed to download image: HTTP {response.status}")

            except asyncio.TimeoutError as e:
                logger.warning(f"Timeout on attempt {attempt + 1}: {str(e)}")
                if attempt == max_retries - 1:
                    raise ValueError(f"Download timeout after {max_retries} attempts - the image server may be slow or the file too large")
                await asyncio.sleep(1)  # Brief delay before retry
                continue

            except aiohttp.ClientError as e:
                logger.warning(f"Network error on attempt {attempt + 1}: {str(e)}")
                if attempt == max_retries - 1:
                    raise ValueError(f"Network error downloading image after {max_retries} attempts: {str(e)}")
                await asyncio.sleep(1)  # Brief delay before retry
                continue

            # If we get here, the download was successful
            break

    except ValueError:
        # Re-raise ValueError as-is (these are user-friendly messages)
        raise
    except Exception as e:
        logger.error(f"Unexpected error downloading image from {image_url[:100]}...: {str(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        raise ValueError(f"Failed to download image: {str(e)}")


async def upload_image_to_s3(image_data: BytesIO, organization_id: str, filename: str = None) -> str:
    """
    Upload an image to S3 and return the public URL.
    
    Args:
        image_data (BytesIO): The image data to upload
        organization_id (str): Organization ID for folder structure
        filename (str, optional): Custom filename for the upload
        
    Returns:
        str: The S3 public URL of the uploaded image
        
    Raises:
        Exception: If the upload fails
    """
    try:
        # Reset the BytesIO position to the beginning
        image_data.seek(0)
        
        # Generate filename if not provided
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            unique_id = str(uuid.uuid4())[:8]
            filename = f"image_{timestamp}_{unique_id}.png"
        
        # Create organization-specific folder structure
        s3_key = f"organizations/{organization_id}/chat_images/{filename}"
        
        # Upload to S3 (run in thread pool since it's blocking)
        await asyncio.to_thread(
            s3_client.put_object,
            Bucket=settings.AWS_S3_BUCKET_NAME,
            Key=s3_key,
            Body=image_data.getvalue(),
            ContentType='image/png'
            # Note: Removed ACL parameter as the bucket doesn't support ACLs
            # The bucket should be configured with a public read policy if public access is needed
        )
        
        # Try to construct a public URL first, but if the bucket is private,
        # we'll need to use presigned URLs
        s3_url = f"https://{settings.AWS_S3_BUCKET_NAME}.s3.{settings.AWS_S3_REGION}.amazonaws.com/{s3_key}"
        logger.info(f"Image uploaded to S3: {s3_url}")

        return s3_url
        
    except ClientError as e:
        logger.error(f"AWS S3 error uploading image: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Error uploading image to S3: {str(e)}")
        raise


async def upload_generated_image_to_s3(openai_image_url: str, organization_id: str, custom_filename: str = None) -> str:
    """
    Download an image from OpenAI and upload it to S3.
    Tries public upload first, falls back to presigned URL if ACL is not supported.

    Args:
        openai_image_url (str): The URL of the image from OpenAI
        organization_id (str): Organization ID for folder structure
        custom_filename (str, optional): Custom filename for the upload

    Returns:
        str: The S3 URL (public or presigned) of the uploaded image

    Raises:
        Exception: If the process fails
    """
    try:
        # Validate inputs
        if not openai_image_url:
            raise ValueError("OpenAI image URL is empty or None")
        if not organization_id:
            raise ValueError("Organization ID is empty or None")

        # Download the image from OpenAI
        logger.info(f"Downloading image from OpenAI: {openai_image_url}")
        image_data = await download_image_from_url(openai_image_url)

        # Validate downloaded image data
        if not image_data or image_data.getbuffer().nbytes == 0:
            raise ValueError("Downloaded image data is empty")

        logger.info(f"Successfully downloaded image data: {image_data.getbuffer().nbytes} bytes")

        # Try to upload to S3 with public access first
        try:
            logger.info(f"Uploading image to S3 for organization {organization_id}...")
            s3_url = await upload_image_to_s3(image_data, organization_id, custom_filename)
            logger.info(f"Successfully uploaded to S3 with public URL: {s3_url}")
            return s3_url
        except ClientError as e:
            error_code = e.response['Error']['Code']
            logger.warning(f"S3 ClientError: {error_code} - {str(e)}")

            if 'AccessControlListNotSupported' in str(e) or error_code == 'AccessControlListNotSupported':
                logger.info("Bucket doesn't support ACLs, using presigned URL approach...")
                # Reset image data position for retry
                image_data.seek(0)
                # Use presigned URL approach for private buckets
                presigned_url = await upload_image_to_s3_with_presigned_url(
                    image_data, organization_id, custom_filename, expiration=86400  # 24 hours
                )
                logger.info(f"Successfully uploaded to S3 with presigned URL: {presigned_url[:100]}...")
                return presigned_url
            elif error_code == 'NoSuchBucket':
                raise ValueError(f"S3 bucket '{settings.AWS_S3_BUCKET_NAME}' does not exist")
            elif error_code == 'AccessDenied':
                raise ValueError("Access denied to S3 bucket - check AWS credentials and permissions")
            elif error_code == 'InvalidAccessKeyId':
                raise ValueError("Invalid AWS access key - check AWS credentials")
            elif error_code == 'SignatureDoesNotMatch':
                raise ValueError("AWS signature mismatch - check AWS secret key")
            else:
                # Re-raise other S3 errors with more context
                raise ValueError(f"S3 upload failed: {error_code} - {str(e)}")

    except ValueError:
        # Re-raise ValueError as-is (these are user-friendly messages)
        raise
    except Exception as e:
        logger.error(f"Unexpected error processing image from OpenAI to S3: {str(e)}")
        raise ValueError(f"Failed to process image upload: {str(e)}")


async def delete_image_from_s3(s3_url: str) -> bool:
    """
    Delete an image from S3 using its URL.
    
    Args:
        s3_url (str): The S3 URL of the image to delete
        
    Returns:
        bool: True if deletion was successful, False otherwise
    """
    try:
        # Extract the S3 key from the URL
        # URL format: https://bucket.s3.region.amazonaws.com/key
        url_parts = s3_url.split(f"{settings.AWS_S3_BUCKET_NAME}.s3.{settings.AWS_S3_REGION}.amazonaws.com/")
        if len(url_parts) != 2:
            raise ValueError("Invalid S3 URL format")
        
        s3_key = url_parts[1]
        
        # Delete the object from S3
        await asyncio.to_thread(
            s3_client.delete_object,
            Bucket=settings.AWS_S3_BUCKET_NAME,
            Key=s3_key
        )
        
        logger.info(f"Image deleted from S3: {s3_url}")
        return True
        
    except Exception as e:
        logger.error(f"Error deleting image from S3: {str(e)}")
        return False


async def list_organization_images(organization_id: str, limit: int = 100) -> list:
    """
    List all images for a specific organization.
    
    Args:
        organization_id (str): Organization ID
        limit (int): Maximum number of images to return
        
    Returns:
        list: List of image URLs for the organization
    """
    try:
        prefix = f"organizations/{organization_id}/chat_images/"
        
        # List objects with the organization prefix
        response = await asyncio.to_thread(
            s3_client.list_objects_v2,
            Bucket=settings.AWS_S3_BUCKET_NAME,
            Prefix=prefix,
            MaxKeys=limit
        )
        
        images = []
        if 'Contents' in response:
            for obj in response['Contents']:
                s3_url = f"https://{settings.AWS_S3_BUCKET_NAME}.s3.{settings.AWS_S3_REGION}.amazonaws.com/{obj['Key']}"
                images.append({
                    'url': s3_url,
                    'key': obj['Key'],
                    'last_modified': obj['LastModified'],
                    'size': obj['Size']
                })
        
        logger.info(f"Found {len(images)} images for organization {organization_id}")
        return images

    except Exception as e:
        logger.error(f"Error listing images for organization {organization_id}: {str(e)}")
        return []


async def generate_presigned_url(s3_key: str, expiration: int = 3600) -> str:
    """
    Generate a presigned URL for accessing a private S3 object.

    Args:
        s3_key (str): The S3 key of the object
        expiration (int): URL expiration time in seconds (default: 1 hour)

    Returns:
        str: The presigned URL
    """
    try:
        presigned_url = await asyncio.to_thread(
            s3_client.generate_presigned_url,
            'get_object',
            Params={'Bucket': settings.AWS_S3_BUCKET_NAME, 'Key': s3_key},
            ExpiresIn=expiration
        )

        logger.info(f"Generated presigned URL for {s3_key}")
        return presigned_url

    except Exception as e:
        logger.error(f"Error generating presigned URL for {s3_key}: {str(e)}")
        raise


async def upload_image_to_s3_with_presigned_url(image_data: BytesIO, organization_id: str, filename: str = None, expiration: int = 86400) -> str:
    """
    Upload an image to S3 and return a presigned URL for access.
    This is useful when the bucket is private and doesn't allow public access.

    Args:
        image_data (BytesIO): The image data to upload
        organization_id (str): Organization ID for folder structure
        filename (str, optional): Custom filename for the upload
        expiration (int): Presigned URL expiration time in seconds (default: 24 hours)

    Returns:
        str: The presigned URL for accessing the uploaded image
    """
    try:
        # Reset the BytesIO position to the beginning
        image_data.seek(0)

        # Generate filename if not provided
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            unique_id = str(uuid.uuid4())[:8]
            filename = f"image_{timestamp}_{unique_id}.png"

        # Create organization-specific folder structure
        s3_key = f"organizations/{organization_id}/chat_images/{filename}"

        # Upload to S3 (run in thread pool since it's blocking)
        await asyncio.to_thread(
            s3_client.put_object,
            Bucket=settings.AWS_S3_BUCKET_NAME,
            Key=s3_key,
            Body=image_data.getvalue(),
            ContentType='image/png'
        )

        # Generate presigned URL for access
        presigned_url = await generate_presigned_url(s3_key, expiration)
        logger.info(f"Image uploaded to S3 with presigned URL: {presigned_url[:100]}...")

        return presigned_url

    except Exception as e:
        logger.error(f"Error uploading image to S3 with presigned URL: {str(e)}")
        raise
