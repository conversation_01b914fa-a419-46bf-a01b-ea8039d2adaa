"""
Event publishers for FastBot service.
"""

import logging
from typing import Any, Dict, Optional

from .client import get_event_client

logger = logging.getLogger(__name__)


async def publish_file_uploaded(
    file_id: str,
    user_id: str,
    organization_id: str,
    filename: str,
    file_size: int,
    file_type: str,
    upload_path: str,
    correlation_id: Optional[str] = None,
) -> str:
    """Publish a file upload event."""
    try:
        event_client = get_event_client()
        
        data = {
            "file_id": file_id,
            "user_id": user_id,
            "organization_id": organization_id,
            "filename": filename,
            "file_size": file_size,
            "file_type": file_type,
            "upload_path": upload_path,
        }
        
        return await event_client.publish_event(
            event_type="fastbot.file.uploaded",
            data=data,
            correlation_id=correlation_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    except Exception as e:
        logger.error(f"Failed to publish file uploaded event: {e}")
        return ""


async def publish_file_processed(
    file_id: str,
    user_id: str,
    organization_id: str,
    filename: str,
    processing_status: str,
    processing_result: Optional[Dict[str, Any]] = None,
    correlation_id: Optional[str] = None,
) -> str:
    """Publish a file processing completion event."""
    try:
        event_client = get_event_client()
        
        data = {
            "file_id": file_id,
            "user_id": user_id,
            "organization_id": organization_id,
            "filename": filename,
            "processing_status": processing_status,
            "processing_result": processing_result,
        }
        
        return await event_client.publish_event(
            event_type="fastbot.file.processed",
            data=data,
            correlation_id=correlation_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    except Exception as e:
        logger.error(f"Failed to publish file processed event: {e}")
        return ""


async def publish_knowledge_base_updated(
    user_id: str,
    organization_id: str,
    update_type: str,
    affected_documents: list,
    correlation_id: Optional[str] = None,
) -> str:
    """Publish a knowledge base update event."""
    try:
        event_client = get_event_client()
        
        data = {
            "user_id": user_id,
            "organization_id": organization_id,
            "update_type": update_type,
            "affected_documents": affected_documents,
        }
        
        return await event_client.publish_event(
            event_type="fastbot.knowledge_base.updated",
            data=data,
            correlation_id=correlation_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    except Exception as e:
        logger.error(f"Failed to publish knowledge base updated event: {e}")
        return ""


async def publish_task_completed(
    task_id: str,
    task_name: str,
    user_id: str,
    organization_id: str,
    result: Optional[Dict[str, Any]] = None,
    correlation_id: Optional[str] = None,
) -> str:
    """Publish a task completion event."""
    try:
        event_client = get_event_client()
        
        data = {
            "task_id": task_id,
            "task_name": task_name,
            "user_id": user_id,
            "organization_id": organization_id,
            "result": result,
        }
        
        return await event_client.publish_event(
            event_type="fastbot.task.completed",
            data=data,
            correlation_id=correlation_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    except Exception as e:
        logger.error(f"Failed to publish task completed event: {e}")
        return ""


async def publish_task_failed(
    task_id: str,
    task_name: str,
    user_id: str,
    organization_id: str,
    error: str,
    correlation_id: Optional[str] = None,
) -> str:
    """Publish a task failure event."""
    try:
        event_client = get_event_client()
        
        data = {
            "task_id": task_id,
            "task_name": task_name,
            "user_id": user_id,
            "organization_id": organization_id,
            "error": error,
        }
        
        return await event_client.publish_event(
            event_type="fastbot.task.failed",
            data=data,
            correlation_id=correlation_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    except Exception as e:
        logger.error(f"Failed to publish task failed event: {e}")
        return ""
