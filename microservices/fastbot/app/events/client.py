"""
Event client for FastBot service to publish events to the event bus.
This uses HTTP calls to the settings_and_payment service event API.
"""

import logging
from typing import Any, Dict, Optional

import httpx
from app.core.config import settings

logger = logging.getLogger(__name__)


class EventClient:
    """Client for publishing events to the event bus via HTTP."""
    
    def __init__(self, event_service_url: str = None):
        # Use settings service URL for event publishing
        self.event_service_url = event_service_url or "http://settings_and_payment:8005"
        self.timeout = httpx.Timeout(10.0, connect=10.0, read=15.0, write=20.0)
    
    async def publish_event(
        self,
        event_type: str,
        data: Dict[str, Any],
        correlation_id: Optional[str] = None,
        user_id: Optional[str] = None,
        organization_id: Optional[str] = None,
    ) -> str:
        """
        Publish an event to the event bus.
        
        Returns:
            str: The event ID
        """
        try:
            payload = {
                "event_type": event_type,
                "data": data,
                "correlation_id": correlation_id,
                "user_id": user_id,
                "organization_id": organization_id,
            }
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    f"{self.event_service_url}/api/v1/events/publish",
                    json=payload,
                )
                response.raise_for_status()
                result = response.json()
                
                event_id = result.get("event_id")
                logger.info(f"Published event {event_id} of type {event_type}")
                return event_id
                
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error publishing event {event_type}: {e.response.status_code} - {e.response.text}")
            # Don't raise - we don't want event publishing failures to break main functionality
            return ""
        except httpx.RequestError as e:
            logger.error(f"Request error publishing event {event_type}: {e}")
            return ""
        except Exception as e:
            logger.error(f"Unexpected error publishing event {event_type}: {e}")
            return ""
    
    async def get_event_types(self) -> list:
        """Get all available event types."""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(
                    f"{self.event_service_url}/api/v1/events/event-types"
                )
                response.raise_for_status()
                result = response.json()
                return result.get("event_types", [])
                
        except Exception as e:
            logger.error(f"Error getting event types: {e}")
            return []
    
    async def health_check(self) -> bool:
        """Check if the event service is healthy."""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(
                    f"{self.event_service_url}/api/v1/events/health"
                )
                response.raise_for_status()
                return True
                
        except Exception as e:
            logger.error(f"Event service health check failed: {e}")
            return False


# Global event client instance
_event_client: Optional[EventClient] = None


def get_event_client() -> EventClient:
    """Get the global event client instance."""
    global _event_client
    if _event_client is None:
        _event_client = EventClient()
    return _event_client


def init_event_client(event_service_url: str = None) -> EventClient:
    """Initialize the global event client instance."""
    global _event_client
    _event_client = EventClient(event_service_url)
    return _event_client
