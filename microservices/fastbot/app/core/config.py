from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    # Environment Variables
    SECRET_KEY: str
    ALGORITHM: str
    OPENAI_API_KEY: str
    GEMINI_API_KEY: str
    PROD_DATABASE_URL: str
    LOCAL_DATABASE_URL: str
    REDIS_URL: str
    MODEL_PROVIDER: str
    GOOGLE_SEARCH_API_KEY: str
    GOOGLE_SEARCH_ENGINE_ID: str

    # Qdrant configuration
    QDRANT_HOST: str
    QDRANT_PORT: str
    VECTOR_DIM: str
    KNOWLEDGEBASE_COLLECTION: str
    AUTH_SERVICE_URL: str
    NOTIFICATION_SERVICE_URL: str

    # Multi-service database URLs for agentic queries
    SOCIALS_DATABASE_URL: str = None
    AUTH_DATABASE_URL: str = None
    SETTINGS_PAYMENT_DATABASE_URL: str = None

    # MINIO SETUP
    MINIO_ENDPOINT: str
    MINIO_ROOT_USER: str
    MINIO_ROOT_PASSWORD: str
    MINIO_SECURE: str = False
    MINIO_BUCKET: str

    # CELERY
    CELERY_BROKER_URL: str
    CELERY_RESULT_BACKEND: str

    # AWS S3
    AWS_S3_BUCKET_NAME: str
    AWS_S3_REGION: str
    AWS_S3_ACCESS_KEY: str
    AWS_S3_SECRET_ACCESS_KEY: str

    ENV: str

    class Config:
        env_file = ".env"
        extra = "ignore"


settings = Settings()
