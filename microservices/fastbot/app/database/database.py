from app.core.config import settings
from app.utils.logger import get_logger
from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import declarative_base, sessionmaker

logger = get_logger(__name__)


DATABASE_URL = (
    settings.PROD_DATABASE_URL
    if settings.ENV == "PRODUCTION"
    else settings.LOCAL_DATABASE_URL
)

logger.info(f"Running in {settings.ENV} mode with db uri {DATABASE_URL}")

if not DATABASE_URL:
    raise ValueError("DATABASE_URL is not set in the environment.")

# Create the asynchronous engine.
async_engine = create_async_engine(
    DATABASE_URL,
    future=True,
    echo=False,
    pool_pre_ping=True,
    pool_recycle=1800,
    pool_timeout=30,
    pool_size=10,
    max_overflow=20,
)

# Create an async sessionmaker using AsyncSession.
AsyncSessionLocal = sessionmaker(
    bind=async_engine,
    class_=AsyncSession,  # Use the actual AsyncSession class.
    expire_on_commit=False,
)

# For synchronous operations in Celery tasks, create a synchronous engine.
# Replace "+asyncpg" with the appropriate sync driver, e.g., remove it for psycopg2.
sync_database_url = DATABASE_URL.replace("+asyncpg", "")
sync_engine = create_engine(sync_database_url, echo=False)

# Synchronous sessionmaker.
SessionLocal = sessionmaker(bind=sync_engine, expire_on_commit=False)

Base = declarative_base()
# Dependency to get database session


async def get_db_session():
    async with AsyncSessionLocal() as session:
        yield session
