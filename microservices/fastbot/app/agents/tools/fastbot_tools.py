"""
FastBot Analytics Tools for LangGraph Agent
Provides tools to query chat analytics, file uploads, and AI usage metrics.
"""

from typing import Dict, Any, List, Type
from langchain.tools import BaseTool
from pydantic import BaseModel, Field
from app.database.multi_db_manager import MultiDatabaseManager
from app.utils.logger import get_logger

logger = get_logger(__name__)


class ChatAnalyticsInput(BaseModel):
    """Input schema for chat analytics queries."""
    organization_id: str = Field(description="Organization ID to query analytics for")
    days: int = Field(default=30, description="Number of days to look back for analytics")


class ChatMetricsTool(BaseTool):
    """Tool to get chat and conversation metrics."""
    
    name: str = "get_chat_metrics"
    description: str = """
    Get chat metrics including total conversations, messages, user engagement, and AI usage.
    Use this when users ask about chat activity, conversation volume, or AI assistant usage.
    """
    args_schema: Type[BaseModel] = ChatAnalyticsInput
    
    def __init__(self, db_manager: MultiDatabaseManager):
        super().__init__()
        self.db_manager = db_manager
    
    async def _arun(self, organization_id: str, days: int = 30) -> str:
        """Get chat metrics for the organization."""
        try:
            # Get overall chat statistics
            chat_stats_query = """
                SELECT 
                    COUNT(DISTINCT ct.id) as total_threads,
                    COUNT(cm.id) as total_messages,
                    COUNT(DISTINCT ct.user_id) as unique_users,
                    AVG(
                        CASE WHEN ct.updated_at > ct.created_at 
                        THEN EXTRACT(EPOCH FROM (ct.updated_at - ct.created_at))/60 
                        ELSE 0 END
                    ) as avg_session_duration_minutes
                FROM chat_threads ct
                LEFT JOIN chat_messages cm ON ct.id = cm.thread_id
                WHERE ct.organization_id = :org_id
                AND ct.created_at >= NOW() - INTERVAL ':days days'
            """
            
            # Get message distribution by role
            message_distribution_query = """
                SELECT cm.role, COUNT(*) as message_count
                FROM chat_messages cm
                JOIN chat_threads ct ON cm.thread_id = ct.id
                WHERE ct.organization_id = :org_id
                AND cm.timestamp >= NOW() - INTERVAL ':days days'
                GROUP BY cm.role
                ORDER BY message_count DESC
            """
            
            # Get daily activity
            daily_activity_query = """
                SELECT DATE(ct.created_at) as date, 
                       COUNT(DISTINCT ct.id) as new_threads,
                       COUNT(cm.id) as total_messages
                FROM chat_threads ct
                LEFT JOIN chat_messages cm ON ct.id = cm.thread_id 
                    AND DATE(cm.timestamp) = DATE(ct.created_at)
                WHERE ct.organization_id = :org_id
                AND ct.created_at >= NOW() - INTERVAL ':days days'
                GROUP BY DATE(ct.created_at)
                ORDER BY date DESC
                LIMIT 7
            """
            
            # Get most active users
            active_users_query = """
                SELECT ct.user_id, COUNT(DISTINCT ct.id) as thread_count,
                       COUNT(cm.id) as message_count
                FROM chat_threads ct
                LEFT JOIN chat_messages cm ON ct.id = cm.thread_id
                WHERE ct.organization_id = :org_id
                AND ct.created_at >= NOW() - INTERVAL ':days days'
                GROUP BY ct.user_id
                ORDER BY thread_count DESC
                LIMIT 5
            """
            
            params = {"org_id": organization_id, "days": days}
            
            chat_stats = await self.db_manager.execute_query('fastbot', chat_stats_query, params)
            message_dist = await self.db_manager.execute_query('fastbot', message_distribution_query, params)
            daily_activity = await self.db_manager.execute_query('fastbot', daily_activity_query, params)
            active_users = await self.db_manager.execute_query('fastbot', active_users_query, params)
            
            if not chat_stats:
                return f"No chat data found for organization {organization_id}"
            
            stats = chat_stats[0]
            
            result = f"Chat Metrics for Organization {organization_id} (Last {days} days):\n\n"
            result += f"Total Conversations: {stats['total_threads']}\n"
            result += f"Total Messages: {stats['total_messages']}\n"
            result += f"Unique Users: {stats['unique_users']}\n"
            result += f"Average Session Duration: {stats['avg_session_duration_minutes']:.1f} minutes\n\n"
            
            if message_dist:
                result += "Message Distribution:\n"
                for msg in message_dist:
                    result += f"  {msg['role'].title()}: {msg['message_count']} messages\n"
                result += "\n"
            
            if daily_activity:
                result += "Recent Daily Activity:\n"
                for day in daily_activity:
                    result += f"  {day['date']}: {day['new_threads']} new conversations, {day['total_messages']} messages\n"
                result += "\n"
            
            if active_users:
                result += "Most Active Users:\n"
                for i, user in enumerate(active_users, 1):
                    result += f"  {i}. User {user['user_id']}: {user['thread_count']} conversations, {user['message_count']} messages\n"
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting chat metrics: {e}")
            return f"Error retrieving chat metrics: {str(e)}"


class FileUploadMetricsTool(BaseTool):
    """Tool to get file upload and processing metrics."""
    
    name: str = "get_file_metrics"
    description: str = """
    Get file upload metrics including file types, sizes, processing status, and usage patterns.
    Use this when users ask about file uploads, document processing, or storage usage.
    """
    args_schema: Type[BaseModel] = ChatAnalyticsInput

    def __init__(self, db_manager: MultiDatabaseManager):
        super().__init__()
        self.db_manager = db_manager
    
    async def _arun(self, organization_id: str, days: int = 30) -> str:
        """Get file upload metrics for the organization."""
        try:
            # Get file upload statistics
            file_stats_query = """
                SELECT 
                    COUNT(*) as total_files,
                    COUNT(DISTINCT user_id) as unique_uploaders,
                    SUM(filesize) as total_size_bytes,
                    AVG(filesize) as avg_file_size_bytes
                FROM uploaded_files
                WHERE organisation_id = :org_id
                AND upload_date >= NOW() - INTERVAL ':days days'
            """
            
            # Get file type distribution
            file_types_query = """
                SELECT filetype, COUNT(*) as file_count,
                       SUM(filesize) as total_size_bytes
                FROM uploaded_files
                WHERE organisation_id = :org_id
                AND upload_date >= NOW() - INTERVAL ':days days'
                GROUP BY filetype
                ORDER BY file_count DESC
            """
            
            # Get recent uploads
            recent_uploads_query = """
                SELECT filename, filetype, filesize, username, upload_date
                FROM uploaded_files
                WHERE organisation_id = :org_id
                AND upload_date >= NOW() - INTERVAL ':days days'
                ORDER BY upload_date DESC
                LIMIT 10
            """
            
            params = {"org_id": organization_id, "days": days}
            
            file_stats = await self.db_manager.execute_query('fastbot', file_stats_query, params)
            file_types = await self.db_manager.execute_query('fastbot', file_types_query, params)
            recent_uploads = await self.db_manager.execute_query('fastbot', recent_uploads_query, params)
            
            if not file_stats or file_stats[0]['total_files'] == 0:
                return f"No file uploads found for organization {organization_id} in the last {days} days"
            
            stats = file_stats[0]
            total_size_mb = stats['total_size_bytes'] / (1024 * 1024) if stats['total_size_bytes'] else 0
            avg_size_mb = stats['avg_file_size_bytes'] / (1024 * 1024) if stats['avg_file_size_bytes'] else 0
            
            result = f"File Upload Metrics for Organization {organization_id} (Last {days} days):\n\n"
            result += f"Total Files Uploaded: {stats['total_files']}\n"
            result += f"Unique Uploaders: {stats['unique_uploaders']}\n"
            result += f"Total Storage Used: {total_size_mb:.2f} MB\n"
            result += f"Average File Size: {avg_size_mb:.2f} MB\n\n"
            
            if file_types:
                result += "File Type Distribution:\n"
                for file_type in file_types:
                    type_size_mb = file_type['total_size_bytes'] / (1024 * 1024) if file_type['total_size_bytes'] else 0
                    result += f"  {file_type['filetype']}: {file_type['file_count']} files ({type_size_mb:.2f} MB)\n"
                result += "\n"
            
            if recent_uploads:
                result += "Recent Uploads:\n"
                for upload in recent_uploads:
                    size_mb = upload['filesize'] / (1024 * 1024) if upload['filesize'] else 0
                    result += f"  {upload['filename']} ({upload['filetype']}) - {size_mb:.2f} MB by {upload['username']} on {upload['upload_date']}\n"
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting file metrics: {e}")
            return f"Error retrieving file metrics: {str(e)}"


class KnowledgeBaseMetricsTool(BaseTool):
    """Tool to get knowledge base and RAG metrics."""
    
    name: str = "get_knowledge_metrics"
    description: str = """
    Get knowledge base metrics including document processing, embeddings, and RAG usage.
    Use this when users ask about knowledge base performance, document analysis, or AI knowledge retrieval.
    """
    args_schema: Type[BaseModel] = ChatAnalyticsInput

    def __init__(self, db_manager: MultiDatabaseManager):
        super().__init__()
        self.db_manager = db_manager
    
    async def _arun(self, organization_id: str, days: int = 30) -> str:
        """Get knowledge base metrics for the organization."""
        try:
            # Get processed files count
            processed_files_query = """
                SELECT COUNT(*) as processed_files,
                       COUNT(CASE WHEN summary IS NOT NULL AND summary != '' THEN 1 END) as files_with_summary
                FROM uploaded_files
                WHERE organisation_id = :org_id
                AND upload_date >= NOW() - INTERVAL ':days days'
            """
            
            # Get Gemini files (if using Gemini for processing)
            gemini_files_query = """
                SELECT COUNT(*) as gemini_files
                FROM gemini_files
                WHERE organisation_id = :org_id
                AND created_at >= NOW() - INTERVAL ':days days'
            """
            
            params = {"org_id": organization_id, "days": days}
            
            processed_files = await self.db_manager.execute_query('fastbot', processed_files_query, params)
            gemini_files = await self.db_manager.execute_query('fastbot', gemini_files_query, params)
            
            result = f"Knowledge Base Metrics for Organization {organization_id} (Last {days} days):\n\n"
            
            if processed_files:
                stats = processed_files[0]
                result += f"Processed Files: {stats['processed_files']}\n"
                result += f"Files with AI Summary: {stats['files_with_summary']}\n"
                
                if stats['processed_files'] > 0:
                    summary_rate = (stats['files_with_summary'] / stats['processed_files']) * 100
                    result += f"Summary Generation Rate: {summary_rate:.1f}%\n"
            
            if gemini_files:
                result += f"Gemini Processed Files: {gemini_files[0]['gemini_files']}\n"
            
            # Note: For more detailed RAG metrics, we would need to track query performance,
            # embedding quality, and retrieval accuracy in separate tables
            result += "\nNote: Enhanced RAG metrics require additional tracking implementation.\n"
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting knowledge metrics: {e}")
            return f"Error retrieving knowledge base metrics: {str(e)}"


class UserEngagementTool(BaseTool):
    """Tool to get user engagement and activity patterns."""
    
    name: str = "get_user_engagement"
    description: str = """
    Get user engagement metrics including session patterns, feature usage, and activity trends.
    Use this when users ask about user behavior, engagement levels, or usage patterns.
    """
    args_schema: Type[BaseModel] = ChatAnalyticsInput

    def __init__(self, db_manager: MultiDatabaseManager):
        super().__init__()
        self.db_manager = db_manager
    
    async def _arun(self, organization_id: str, days: int = 30) -> str:
        """Get user engagement metrics for the organization."""
        try:
            # Get user activity patterns
            activity_patterns_query = """
                SELECT 
                    EXTRACT(HOUR FROM ct.created_at) as hour_of_day,
                    COUNT(*) as activity_count
                FROM chat_threads ct
                WHERE ct.organization_id = :org_id
                AND ct.created_at >= NOW() - INTERVAL ':days days'
                GROUP BY EXTRACT(HOUR FROM ct.created_at)
                ORDER BY hour_of_day
            """
            
            # Get user retention (users who came back)
            retention_query = """
                SELECT 
                    COUNT(DISTINCT user_id) as total_users,
                    COUNT(DISTINCT CASE 
                        WHEN user_thread_count > 1 THEN user_id 
                    END) as returning_users
                FROM (
                    SELECT user_id, COUNT(*) as user_thread_count
                    FROM chat_threads
                    WHERE organization_id = :org_id
                    AND created_at >= NOW() - INTERVAL ':days days'
                    GROUP BY user_id
                ) user_activity
            """
            
            # Get feature usage (files vs text-only chats)
            feature_usage_query = """
                SELECT 
                    COUNT(CASE WHEN cm.file_ids IS NOT NULL AND cm.file_ids != '' THEN 1 END) as messages_with_files,
                    COUNT(CASE WHEN cm.file_ids IS NULL OR cm.file_ids = '' THEN 1 END) as text_only_messages,
                    COUNT(*) as total_messages
                FROM chat_messages cm
                JOIN chat_threads ct ON cm.thread_id = ct.id
                WHERE ct.organization_id = :org_id
                AND cm.timestamp >= NOW() - INTERVAL ':days days'
                AND cm.role = 'user'
            """
            
            params = {"org_id": organization_id, "days": days}
            
            activity_patterns = await self.db_manager.execute_query('fastbot', activity_patterns_query, params)
            retention_data = await self.db_manager.execute_query('fastbot', retention_query, params)
            feature_usage = await self.db_manager.execute_query('fastbot', feature_usage_query, params)
            
            result = f"User Engagement Metrics for Organization {organization_id} (Last {days} days):\n\n"
            
            if retention_data:
                ret_stats = retention_data[0]
                if ret_stats['total_users'] > 0:
                    retention_rate = (ret_stats['returning_users'] / ret_stats['total_users']) * 100
                    result += f"Total Active Users: {ret_stats['total_users']}\n"
                    result += f"Returning Users: {ret_stats['returning_users']}\n"
                    result += f"User Retention Rate: {retention_rate:.1f}%\n\n"
            
            if feature_usage:
                usage_stats = feature_usage[0]
                if usage_stats['total_messages'] > 0:
                    file_usage_rate = (usage_stats['messages_with_files'] / usage_stats['total_messages']) * 100
                    result += f"Feature Usage:\n"
                    result += f"  Messages with Files: {usage_stats['messages_with_files']} ({file_usage_rate:.1f}%)\n"
                    result += f"  Text-only Messages: {usage_stats['text_only_messages']}\n\n"
            
            if activity_patterns:
                result += "Activity Patterns by Hour:\n"
                peak_hour = max(activity_patterns, key=lambda x: x['activity_count'])
                result += f"  Peak Activity: {peak_hour['hour_of_day']}:00 ({peak_hour['activity_count']} activities)\n"
                
                # Show top 3 active hours
                sorted_hours = sorted(activity_patterns, key=lambda x: x['activity_count'], reverse=True)[:3]
                result += "  Top Active Hours:\n"
                for hour_data in sorted_hours:
                    result += f"    {hour_data['hour_of_day']}:00 - {hour_data['activity_count']} activities\n"
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting user engagement metrics: {e}")
            return f"Error retrieving user engagement metrics: {str(e)}"
