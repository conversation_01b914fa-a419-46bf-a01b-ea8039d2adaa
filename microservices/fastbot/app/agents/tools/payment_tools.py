"""
Payment and Subscription Tools for LangGraph Agent
Provides tools to query payment data, subscription status, and billing information.
"""

from typing import Dict, Any, List, Type
from langchain.tools import BaseTool
from pydantic import BaseModel, Field
from app.database.multi_db_manager import MultiDatabaseManager
from app.utils.logger import get_logger

logger = get_logger(__name__)


class PaymentAnalyticsInput(BaseModel):
    """Input schema for payment analytics queries."""
    organization_id: str = Field(description="Organization ID to query payment data for")
    days: int = Field(default=30, description="Number of days to look back for payment analytics")


class SubscriptionStatusTool(BaseTool):
    """Tool to get subscription status and plan information."""
    
    name: str = "get_subscription_status"
    description: str = """
    Get current subscription status, plan details, and billing information.
    Use this when users ask about their subscription, plan features, or billing status.
    """
    args_schema: Type[BaseModel] = PaymentAnalyticsInput

    def __init__(self, db_manager: MultiDatabaseManager):
        super().__init__()
        self.db_manager = db_manager

    async def _arun(self, organization_id: str, days: int = 30) -> str:
        """Get subscription status for the organization."""
        try:
            # Note: These queries assume subscription tables exist in the payment service
            # The actual table structure may vary based on your implementation
            
            subscription_query = """
                SELECT plan_name, plan_type, status, start_date, end_date,
                       billing_cycle, price, currency, auto_renewal,
                       features, limits, created_at, updated_at
                FROM subscriptions
                WHERE organization_id = :org_id
                AND status IN ('active', 'trial', 'past_due')
                ORDER BY created_at DESC
                LIMIT 1
            """
            
            params = {"org_id": organization_id}
            subscription_data = await self.db_manager.execute_query('settings_payment', subscription_query, params)
            
            if not subscription_data:
                return f"No active subscription found for organization {organization_id}"
            
            sub = subscription_data[0]
            
            result = f"Subscription Status for Organization {organization_id}:\n\n"
            result += f"Plan: {sub['plan_name']} ({sub['plan_type']})\n"
            result += f"Status: {sub['status'].title()}\n"
            result += f"Price: {sub['currency']} {sub['price']}/{sub['billing_cycle']}\n"
            result += f"Start Date: {sub['start_date']}\n"
            result += f"End Date: {sub['end_date']}\n"
            result += f"Auto Renewal: {'Enabled' if sub['auto_renewal'] else 'Disabled'}\n"
            
            if sub['features']:
                result += f"Features: {sub['features']}\n"
            
            if sub['limits']:
                result += f"Limits: {sub['limits']}\n"
            
            # Calculate days until renewal/expiry
            from datetime import datetime
            if sub['end_date']:
                try:
                    end_date = datetime.fromisoformat(str(sub['end_date']).replace('Z', '+00:00'))
                    days_remaining = (end_date - datetime.now()).days
                    if days_remaining > 0:
                        result += f"Days Until Renewal: {days_remaining}\n"
                    else:
                        result += f"⚠️ Subscription expired {abs(days_remaining)} days ago\n"
                except:
                    pass
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting subscription status: {e}")
            return f"Error retrieving subscription status: {str(e)}"


class PaymentHistoryTool(BaseTool):
    """Tool to get payment history and transaction data."""
    
    name: str = "get_payment_history"
    description: str = """
    Get payment history including transactions, invoices, and billing records.
    Use this when users ask about payment history, invoices, or transaction records.
    """
    args_schema: Type[BaseModel] = PaymentAnalyticsInput

    def __init__(self, db_manager: MultiDatabaseManager):
        super().__init__()
        self.db_manager = db_manager

    async def _arun(self, organization_id: str, days: int = 30) -> str:
        """Get payment history for the organization."""
        try:
            payment_query = """
                SELECT transaction_id, amount, currency, status, payment_method,
                       description, created_at, processed_at, invoice_id
                FROM payments
                WHERE organization_id = :org_id
                AND created_at >= NOW() - INTERVAL ':days days'
                ORDER BY created_at DESC
                LIMIT 20
            """
            
            # Get payment summary
            summary_query = """
                SELECT 
                    COUNT(*) as total_transactions,
                    SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as total_paid,
                    SUM(CASE WHEN status = 'failed' THEN amount ELSE 0 END) as failed_amount,
                    COUNT(CASE WHEN status = 'completed' THEN 1 END) as successful_payments,
                    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_payments
                FROM payments
                WHERE organization_id = :org_id
                AND created_at >= NOW() - INTERVAL ':days days'
            """
            
            params = {"org_id": organization_id, "days": days}
            
            payments = await self.db_manager.execute_query('settings_payment', payment_query, params)
            summary = await self.db_manager.execute_query('settings_payment', summary_query, params)
            
            result = f"Payment History for Organization {organization_id} (Last {days} days):\n\n"
            
            if summary:
                stats = summary[0]
                success_rate = (stats['successful_payments'] / stats['total_transactions'] * 100) if stats['total_transactions'] > 0 else 0
                
                result += f"Payment Summary:\n"
                result += f"  Total Transactions: {stats['total_transactions']}\n"
                result += f"  Successful Payments: {stats['successful_payments']}\n"
                result += f"  Failed Payments: {stats['failed_payments']}\n"
                result += f"  Success Rate: {success_rate:.1f}%\n"
                result += f"  Total Amount Paid: {stats['total_paid']}\n"
                result += f"  Failed Amount: {stats['failed_amount']}\n\n"
            
            if payments:
                result += "Recent Transactions:\n"
                for payment in payments:
                    result += f"  {payment['created_at']} - {payment['currency']} {payment['amount']} "
                    result += f"({payment['status']}) - {payment['description']}\n"
                    if payment['payment_method']:
                        result += f"    Method: {payment['payment_method']}\n"
                    if payment['invoice_id']:
                        result += f"    Invoice: {payment['invoice_id']}\n"
                    result += "\n"
            else:
                result += "No payment transactions found in the specified period\n"
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting payment history: {e}")
            return f"Error retrieving payment history: {str(e)}"


class BillingAnalyticsTool(BaseTool):
    """Tool to get billing analytics and revenue metrics."""
    
    name: str = "get_billing_analytics"
    description: str = """
    Get billing analytics including revenue trends, payment patterns, and financial metrics.
    Use this when users ask about revenue, billing trends, or financial performance.
    """
    args_schema: Type[BaseModel] = PaymentAnalyticsInput

    def __init__(self, db_manager: MultiDatabaseManager):
        super().__init__()
        self.db_manager = db_manager

    async def _arun(self, organization_id: str, days: int = 30) -> str:
        """Get billing analytics for the organization."""
        try:
            # Monthly revenue trend
            revenue_trend_query = """
                SELECT 
                    DATE_TRUNC('month', created_at) as month,
                    SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as revenue,
                    COUNT(CASE WHEN status = 'completed' THEN 1 END) as successful_payments
                FROM payments
                WHERE organization_id = :org_id
                AND created_at >= NOW() - INTERVAL '12 months'
                GROUP BY DATE_TRUNC('month', created_at)
                ORDER BY month DESC
                LIMIT 6
            """
            
            # Payment method distribution
            payment_methods_query = """
                SELECT payment_method, 
                       COUNT(*) as transaction_count,
                       SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as total_amount
                FROM payments
                WHERE organization_id = :org_id
                AND created_at >= NOW() - INTERVAL ':days days'
                AND payment_method IS NOT NULL
                GROUP BY payment_method
                ORDER BY total_amount DESC
            """
            
            # Average transaction value
            avg_transaction_query = """
                SELECT 
                    AVG(CASE WHEN status = 'completed' THEN amount END) as avg_transaction_value,
                    MIN(CASE WHEN status = 'completed' THEN amount END) as min_transaction,
                    MAX(CASE WHEN status = 'completed' THEN amount END) as max_transaction
                FROM payments
                WHERE organization_id = :org_id
                AND created_at >= NOW() - INTERVAL ':days days'
            """
            
            params = {"org_id": organization_id, "days": days}
            
            revenue_trend = await self.db_manager.execute_query('settings_payment', revenue_trend_query, params)
            payment_methods = await self.db_manager.execute_query('settings_payment', payment_methods_query, params)
            avg_transaction = await self.db_manager.execute_query('settings_payment', avg_transaction_query, params)
            
            result = f"Billing Analytics for Organization {organization_id}:\n\n"
            
            if avg_transaction and avg_transaction[0]['avg_transaction_value']:
                stats = avg_transaction[0]
                result += f"Transaction Analytics (Last {days} days):\n"
                result += f"  Average Transaction Value: {stats['avg_transaction_value']:.2f}\n"
                result += f"  Minimum Transaction: {stats['min_transaction']:.2f}\n"
                result += f"  Maximum Transaction: {stats['max_transaction']:.2f}\n\n"
            
            if payment_methods:
                result += "Payment Method Distribution:\n"
                for method in payment_methods:
                    result += f"  {method['payment_method']}: {method['transaction_count']} transactions, "
                    result += f"Total: {method['total_amount']:.2f}\n"
                result += "\n"
            
            if revenue_trend:
                result += "Monthly Revenue Trend:\n"
                for month_data in revenue_trend:
                    result += f"  {month_data['month']}: {month_data['revenue']:.2f} "
                    result += f"({month_data['successful_payments']} payments)\n"
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting billing analytics: {e}")
            return f"Error retrieving billing analytics: {str(e)}"


class InvoiceStatusTool(BaseTool):
    """Tool to get invoice status and billing documents."""
    
    name: str = "get_invoice_status"
    description: str = """
    Get invoice status, pending bills, and billing document information.
    Use this when users ask about invoices, bills, or outstanding payments.
    """
    args_schema: Type[BaseModel] = PaymentAnalyticsInput

    def __init__(self, db_manager: MultiDatabaseManager):
        super().__init__()
        self.db_manager = db_manager

    async def _arun(self, organization_id: str, days: int = 30) -> str:
        """Get invoice status for the organization."""
        try:
            invoice_query = """
                SELECT invoice_number, amount, currency, status, due_date,
                       issued_date, description, payment_id, created_at
                FROM invoices
                WHERE organization_id = :org_id
                AND created_at >= NOW() - INTERVAL ':days days'
                ORDER BY created_at DESC
                LIMIT 20
            """
            
            # Get outstanding invoices
            outstanding_query = """
                SELECT COUNT(*) as outstanding_count,
                       SUM(amount) as outstanding_amount
                FROM invoices
                WHERE organization_id = :org_id
                AND status IN ('pending', 'overdue')
            """
            
            params = {"org_id": organization_id, "days": days}
            
            invoices = await self.db_manager.execute_query('settings_payment', invoice_query, params)
            outstanding = await self.db_manager.execute_query('settings_payment', outstanding_query, params)
            
            result = f"Invoice Status for Organization {organization_id} (Last {days} days):\n\n"
            
            if outstanding:
                out_stats = outstanding[0]
                if out_stats['outstanding_count'] > 0:
                    result += f"⚠️ Outstanding Invoices: {out_stats['outstanding_count']} "
                    result += f"(Total: {out_stats['outstanding_amount']:.2f})\n\n"
                else:
                    result += "✅ No outstanding invoices\n\n"
            
            if invoices:
                result += "Recent Invoices:\n"
                for invoice in invoices:
                    status_emoji = {
                        'paid': '✅',
                        'pending': '⏳',
                        'overdue': '🚨',
                        'cancelled': '❌'
                    }.get(invoice['status'], '📄')
                    
                    result += f"  {status_emoji} {invoice['invoice_number']} - "
                    result += f"{invoice['currency']} {invoice['amount']} ({invoice['status']})\n"
                    result += f"    Issued: {invoice['issued_date']}, Due: {invoice['due_date']}\n"
                    if invoice['description']:
                        result += f"    Description: {invoice['description']}\n"
                    result += "\n"
            else:
                result += "No invoices found in the specified period\n"
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting invoice status: {e}")
            return f"Error retrieving invoice status: {str(e)}"


class SubscriptionUsageTool(BaseTool):
    """Tool to get subscription usage and limits tracking."""
    
    name: str = "get_subscription_usage"
    description: str = """
    Get subscription usage metrics including feature usage, limits, and quota consumption.
    Use this when users ask about usage limits, quota consumption, or plan utilization.
    """
    args_schema: Type[BaseModel] = PaymentAnalyticsInput

    def __init__(self, db_manager: MultiDatabaseManager):
        super().__init__()
        self.db_manager = db_manager

    async def _arun(self, organization_id: str, days: int = 30) -> str:
        """Get subscription usage for the organization."""
        try:
            usage_query = """
                SELECT feature_name, usage_count, usage_limit, 
                       reset_period, last_reset, created_at, updated_at
                FROM subscription_usage
                WHERE organization_id = :org_id
                ORDER BY usage_count DESC
            """
            
            params = {"org_id": organization_id}
            usage_data = await self.db_manager.execute_query('settings_payment', usage_query, params)
            
            if not usage_data:
                return f"No subscription usage data found for organization {organization_id}"
            
            result = f"Subscription Usage for Organization {organization_id}:\n\n"
            
            for usage in usage_data:
                usage_percent = (usage['usage_count'] / usage['usage_limit'] * 100) if usage['usage_limit'] > 0 else 0
                
                # Add warning emojis based on usage
                if usage_percent >= 90:
                    status_emoji = "🚨"
                elif usage_percent >= 75:
                    status_emoji = "⚠️"
                elif usage_percent >= 50:
                    status_emoji = "📊"
                else:
                    status_emoji = "✅"
                
                result += f"{status_emoji} {usage['feature_name']}:\n"
                result += f"  Usage: {usage['usage_count']} / {usage['usage_limit']} ({usage_percent:.1f}%)\n"
                result += f"  Reset Period: {usage['reset_period']}\n"
                result += f"  Last Reset: {usage['last_reset']}\n\n"
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting subscription usage: {e}")
            return f"Error retrieving subscription usage: {str(e)}"
