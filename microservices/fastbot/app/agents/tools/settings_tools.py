"""
Settings and Configuration Tools for LangGraph Agent
Provides tools to query application settings, preferences, and configuration data.
"""

from typing import Dict, Any, List, Type
from langchain.tools import BaseTool
from pydantic import BaseModel, Field
from app.database.multi_db_manager import MultiDatabaseManager
from app.utils.logger import get_logger

logger = get_logger(__name__)


class SettingsAnalyticsInput(BaseModel):
    """Input schema for settings analytics queries."""
    organization_id: str = Field(description="Organization ID to query settings for")


class ApplicationSettingsTool(BaseTool):
    """Tool to get application settings and preferences."""
    
    name: str = "get_application_settings"
    description: str = """
    Get application settings including language preferences, notification settings, and app configuration.
    Use this when users ask about app settings, preferences, or configuration options.
    """
    args_schema: Type[BaseModel] = SettingsAnalyticsInput

    def __init__(self, db_manager: MultiDatabaseManager):
        super().__init__()
        self.db_manager = db_manager

    async def _arun(self, organization_id: str) -> str:
        """Get application settings for the organization."""
        try:
            settings_query = """
                SELECT default_language, content_auto_save_interval,
                       in_app_notifications, email_notifications,
                       created_at, updated_at
                FROM application_settings
                WHERE organization_id = :org_id
                ORDER BY updated_at DESC
                LIMIT 1
            """
            
            params = {"org_id": organization_id}
            settings_data = await self.db_manager.execute_query('settings_payment', settings_query, params)
            
            if not settings_data:
                return f"No application settings found for organization {organization_id}"
            
            settings = settings_data[0]
            
            result = f"Application Settings for Organization {organization_id}:\n\n"
            result += f"Default Language: {settings['default_language']}\n"
            result += f"Auto-save Interval: {settings['content_auto_save_interval']}\n"
            result += f"In-app Notifications: {'Enabled' if settings['in_app_notifications'] else 'Disabled'}\n"
            result += f"Email Notifications: {'Enabled' if settings['email_notifications'] else 'Disabled'}\n"
            result += f"Last Updated: {settings['updated_at']}\n"
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting application settings: {e}")
            return f"Error retrieving application settings: {str(e)}"


class NotificationSettingsTool(BaseTool):
    """Tool to get notification preferences and settings."""
    
    name: str = "get_notification_settings"
    description: str = """
    Get notification settings including email preferences, push notifications, and alert configurations.
    Use this when users ask about notification preferences or communication settings.
    """
    args_schema: Type[BaseModel] = SettingsAnalyticsInput

    def __init__(self, db_manager: MultiDatabaseManager):
        super().__init__()
        self.db_manager = db_manager

    async def _arun(self, organization_id: str) -> str:
        """Get notification settings for the organization."""
        try:
            notification_query = """
                SELECT email_notifications, push_notifications, sms_notifications,
                       marketing_emails, security_alerts, system_updates,
                       notification_frequency, quiet_hours_start, quiet_hours_end,
                       created_at, updated_at
                FROM notification_settings
                WHERE organization_id = :org_id
                ORDER BY updated_at DESC
                LIMIT 1
            """
            
            params = {"org_id": organization_id}
            notification_data = await self.db_manager.execute_query('settings_payment', notification_query, params)
            
            if not notification_data:
                return f"No notification settings found for organization {organization_id}"
            
            settings = notification_data[0]
            
            result = f"Notification Settings for Organization {organization_id}:\n\n"
            result += f"Email Notifications: {'Enabled' if settings['email_notifications'] else 'Disabled'}\n"
            result += f"Push Notifications: {'Enabled' if settings['push_notifications'] else 'Disabled'}\n"
            result += f"SMS Notifications: {'Enabled' if settings['sms_notifications'] else 'Disabled'}\n"
            result += f"Marketing Emails: {'Enabled' if settings['marketing_emails'] else 'Disabled'}\n"
            result += f"Security Alerts: {'Enabled' if settings['security_alerts'] else 'Disabled'}\n"
            result += f"System Updates: {'Enabled' if settings['system_updates'] else 'Disabled'}\n"
            result += f"Notification Frequency: {settings['notification_frequency']}\n"
            
            if settings['quiet_hours_start'] and settings['quiet_hours_end']:
                result += f"Quiet Hours: {settings['quiet_hours_start']} - {settings['quiet_hours_end']}\n"
            
            result += f"Last Updated: {settings['updated_at']}\n"
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting notification settings: {e}")
            return f"Error retrieving notification settings: {str(e)}"


class DataStorageSettingsTool(BaseTool):
    """Tool to get data storage and backup settings."""
    
    name: str = "get_storage_settings"
    description: str = """
    Get data storage settings including storage limits, backup configuration, and data retention policies.
    Use this when users ask about storage usage, backup settings, or data management.
    """
    args_schema: Type[BaseModel] = SettingsAnalyticsInput

    def __init__(self, db_manager: MultiDatabaseManager):
        super().__init__()
        self.db_manager = db_manager

    async def _arun(self, organization_id: str) -> str:
        """Get data storage settings for the organization."""
        try:
            storage_query = """
                SELECT storage_limit, storage_used, automatic_backup,
                       backup_frequency, backup_location, export_format,
                       data_retention_period, deletion_warning,
                       created_at, updated_at
                FROM data_storage_settings
                WHERE organization_id = :org_id
                ORDER BY updated_at DESC
                LIMIT 1
            """
            
            params = {"org_id": organization_id}
            storage_data = await self.db_manager.execute_query('settings_payment', storage_query, params)
            
            if not storage_data:
                return f"No storage settings found for organization {organization_id}"
            
            settings = storage_data[0]
            storage_used_percent = (settings['storage_used'] / settings['storage_limit']) * 100 if settings['storage_limit'] > 0 else 0
            
            result = f"Data Storage Settings for Organization {organization_id}:\n\n"
            result += f"Storage Limit: {settings['storage_limit']} GB\n"
            result += f"Storage Used: {settings['storage_used']} GB ({storage_used_percent:.1f}%)\n"
            result += f"Available Storage: {settings['storage_limit'] - settings['storage_used']} GB\n"
            result += f"Automatic Backup: {'Enabled' if settings['automatic_backup'] else 'Disabled'}\n"
            result += f"Backup Frequency: {settings['backup_frequency']}\n"
            result += f"Backup Location: {settings['backup_location']}\n"
            result += f"Export Format: {settings['export_format']}\n"
            result += f"Data Retention Period: {settings['data_retention_period']}\n"
            result += f"Deletion Warning: {'Enabled' if settings['deletion_warning'] else 'Disabled'}\n"
            result += f"Last Updated: {settings['updated_at']}\n"
            
            # Add storage usage warning if needed
            if storage_used_percent > 80:
                result += f"\n⚠️ Warning: Storage usage is at {storage_used_percent:.1f}% capacity\n"
            elif storage_used_percent > 90:
                result += f"\n🚨 Critical: Storage usage is at {storage_used_percent:.1f}% capacity\n"
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting storage settings: {e}")
            return f"Error retrieving storage settings: {str(e)}"


class UsageMetricsTool(BaseTool):
    """Tool to get usage metrics and analytics from settings service."""
    
    name: str = "get_usage_metrics"
    description: str = """
    Get usage metrics including login statistics, active users, and system usage patterns.
    Use this when users ask about system usage, user activity, or performance metrics.
    """
    args_schema: Type[BaseModel] = SettingsAnalyticsInput

    def __init__(self, db_manager: MultiDatabaseManager):
        super().__init__()
        self.db_manager = db_manager

    async def _arun(self, organization_id: str) -> str:
        """Get usage metrics for the organization."""
        try:
            usage_query = """
                SELECT total_logins, active_users_today, total_sessions,
                       average_session_duration, peak_usage_start, peak_usage_end,
                       created_at, updated_at
                FROM usage_metrics
                WHERE organization_id = :org_id
                ORDER BY updated_at DESC
                LIMIT 1
            """
            
            params = {"org_id": organization_id}
            usage_data = await self.db_manager.execute_query('settings_payment', usage_query, params)
            
            if not usage_data:
                return f"No usage metrics found for organization {organization_id}"
            
            metrics = usage_data[0]
            
            result = f"Usage Metrics for Organization {organization_id}:\n\n"
            result += f"Total Logins: {metrics['total_logins']}\n"
            result += f"Active Users Today: {metrics['active_users_today']}\n"
            result += f"Total Sessions: {metrics['total_sessions']}\n"
            result += f"Average Session Duration: {metrics['average_session_duration']:.1f} minutes\n"
            
            if metrics['peak_usage_start'] and metrics['peak_usage_end']:
                result += f"Peak Usage Hours: {metrics['peak_usage_start']} - {metrics['peak_usage_end']}\n"
            
            result += f"Last Updated: {metrics['updated_at']}\n"
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting usage metrics: {e}")
            return f"Error retrieving usage metrics: {str(e)}"


class SessionActivityTool(BaseTool):
    """Tool to get session activity and user behavior data."""
    
    name: str = "get_session_activity"
    description: str = """
    Get session activity data including user sessions, devices, locations, and activity patterns.
    Use this when users ask about user sessions, device usage, or login patterns.
    """
    args_schema: Type[BaseModel] = SettingsAnalyticsInput

    def __init__(self, db_manager: MultiDatabaseManager):
        super().__init__()
        self.db_manager = db_manager

    async def _arun(self, organization_id: str) -> str:
        """Get session activity for the organization."""
        try:
            # Get recent session activity
            session_query = """
                SELECT username, device, location, start_time, end_time,
                       EXTRACT(EPOCH FROM (COALESCE(end_time, NOW()) - start_time))/60 as duration_minutes
                FROM session_activity
                WHERE organization_id = :org_id
                AND start_time >= NOW() - INTERVAL '7 days'
                ORDER BY start_time DESC
                LIMIT 20
            """
            
            # Get device distribution
            device_query = """
                SELECT device, COUNT(*) as session_count
                FROM session_activity
                WHERE organization_id = :org_id
                AND start_time >= NOW() - INTERVAL '30 days'
                GROUP BY device
                ORDER BY session_count DESC
            """
            
            # Get location distribution
            location_query = """
                SELECT location, COUNT(*) as session_count
                FROM session_activity
                WHERE organization_id = :org_id
                AND start_time >= NOW() - INTERVAL '30 days'
                AND location IS NOT NULL
                GROUP BY location
                ORDER BY session_count DESC
                LIMIT 10
            """
            
            params = {"org_id": organization_id}
            
            sessions = await self.db_manager.execute_query('settings_payment', session_query, params)
            devices = await self.db_manager.execute_query('settings_payment', device_query, params)
            locations = await self.db_manager.execute_query('settings_payment', location_query, params)
            
            result = f"Session Activity for Organization {organization_id}:\n\n"
            
            if devices:
                result += "Device Usage (Last 30 days):\n"
                for device in devices:
                    result += f"  {device['device']}: {device['session_count']} sessions\n"
                result += "\n"
            
            if locations:
                result += "Top Locations (Last 30 days):\n"
                for location in locations:
                    result += f"  {location['location']}: {location['session_count']} sessions\n"
                result += "\n"
            
            if sessions:
                result += "Recent Sessions (Last 7 days):\n"
                for session in sessions[:10]:  # Show top 10
                    status = "Active" if not session['end_time'] else "Ended"
                    duration = session['duration_minutes'] if session['duration_minutes'] else 0
                    result += f"  {session['username']} - {session['device']} ({session['location']}) - {duration:.1f} min ({status})\n"
                
                if len(sessions) > 10:
                    result += f"  ... and {len(sessions) - 10} more sessions\n"
            else:
                result += "No recent session activity found\n"
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting session activity: {e}")
            return f"Error retrieving session activity: {str(e)}"
