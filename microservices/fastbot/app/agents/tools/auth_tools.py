"""
Authentication and User Analytics Tools for LangGraph Agent
Provides tools to query user data, organization info, and authentication metrics.
"""

from typing import Dict, Any, List, Type
from langchain.tools import BaseTool
from pydantic import BaseModel, Field
from app.database.multi_db_manager import MultiDatabaseManager
from app.utils.logger import get_logger

logger = get_logger(__name__)


class UserAnalyticsInput(BaseModel):
    """Input schema for user analytics queries."""
    organization_id: str = Field(description="Organization ID to query analytics for")
    days: int = Field(default=30, description="Number of days to look back for analytics")


class OrganizationInfoTool(BaseTool):
    """Tool to get organization information and details."""
    
    name: str = "get_organization_info"
    description: str = """
    Get detailed organization information including name, industry, settings, and configuration.
    Use this when users ask about organization details, company info, or business settings.
    """
    args_schema: Type[BaseModel] = UserAnalyticsInput
    
    def __init__(self, db_manager: MultiDatabaseManager):
        super().__init__()
        self.db_manager = db_manager
    
    async def _arun(self, organization_id: str, days: int = 30) -> str:
        """Get organization information."""
        try:
            org_query = """
                SELECT name, industry, description, company_size, target_audience,
                       brand_voice, business_tone, primary_color, brand_logo,
                       created_at, updated_at
                FROM organisations 
                WHERE id = :org_id
            """
            
            # Get social accounts linked to organization
            social_accounts_query = """
                SELECT platform, username, login_status
                FROM social_media_accounts
                WHERE organisation_id = :org_id
            """
            
            params = {"org_id": organization_id}
            
            org_data = await self.db_manager.execute_query('authentication', org_query, params)
            social_accounts = await self.db_manager.execute_query('socials', social_accounts_query, params)
            
            if not org_data:
                return f"Organization {organization_id} not found"
            
            org_info = org_data[0]
            
            result = f"Organization Information:\n"
            result += f"Name: {org_info.get('name', 'N/A')}\n"
            result += f"Industry: {org_info.get('industry', 'N/A')}\n"
            result += f"Company Size: {org_info.get('company_size', 'N/A')}\n"
            result += f"Target Audience: {org_info.get('target_audience', 'N/A')}\n"
            result += f"Brand Voice: {org_info.get('brand_voice', 'N/A')}\n"
            result += f"Business Tone: {org_info.get('business_tone', 'N/A')}\n"
            result += f"Description: {org_info.get('description', 'N/A')}\n"
            result += f"Created: {org_info.get('created_at', 'N/A')}\n\n"
            
            if social_accounts:
                result += "Connected Social Media Accounts:\n"
                for account in social_accounts:
                    status = "Connected" if account['login_status'] else "Disconnected"
                    result += f"  {account['platform'].title()}: @{account['username']} ({status})\n"
            else:
                result += "No social media accounts connected\n"
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting organization info: {e}")
            return f"Error retrieving organization information: {str(e)}"


class UserMetricsTool(BaseTool):
    """Tool to get user metrics and analytics."""
    
    name: str = "get_user_metrics"
    description: str = """
    Get user metrics including total users, active users, roles, and user activity.
    Use this when users ask about team size, user activity, or user management analytics.
    """
    args_schema: Type[BaseModel] = UserAnalyticsInput

    def __init__(self, db_manager: MultiDatabaseManager):
        super().__init__()
        self.db_manager = db_manager
    
    async def _arun(self, organization_id: str, days: int = 30) -> str:
        """Get user metrics for the organization."""
        try:
            # Get user counts and status
            user_stats_query = """
                SELECT 
                    COUNT(DISTINCT u.id) as total_users,
                    COUNT(DISTINCT CASE WHEN u.is_active = true THEN u.id END) as active_users,
                    COUNT(DISTINCT CASE WHEN u.is_verified = true THEN u.id END) as verified_users,
                    COUNT(DISTINCT CASE WHEN u.is_onboarded = true THEN u.id END) as onboarded_users
                FROM users u
                JOIN user_organisation_roles uor ON u.id = uor.user_id
                WHERE uor.organisation_id = :org_id
            """
            
            # Get role distribution
            role_distribution_query = """
                SELECT r.name as role_name, COUNT(uor.user_id) as user_count
                FROM roles r
                JOIN user_organisation_roles uor ON r.id = uor.role_id
                WHERE r.org_id = :org_id
                GROUP BY r.name
                ORDER BY user_count DESC
            """
            
            # Get recent user activity (if we have login tracking)
            recent_activity_query = """
                SELECT COUNT(DISTINCT u.id) as recent_active_users
                FROM users u
                JOIN user_organisation_roles uor ON u.id = uor.user_id
                WHERE uor.organisation_id = :org_id
                AND u.updated_at >= NOW() - INTERVAL ':days days'
            """
            
            params = {"org_id": organization_id, "days": days}
            
            user_stats = await self.db_manager.execute_query('authentication', user_stats_query, params)
            role_distribution = await self.db_manager.execute_query('authentication', role_distribution_query, params)
            recent_activity = await self.db_manager.execute_query('authentication', recent_activity_query, params)
            
            if not user_stats:
                return f"No user data found for organization {organization_id}"
            
            stats = user_stats[0]
            
            result = f"User Metrics for Organization {organization_id}:\n\n"
            result += f"Total Users: {stats['total_users']}\n"
            result += f"Active Users: {stats['active_users']}\n"
            result += f"Verified Users: {stats['verified_users']}\n"
            result += f"Onboarded Users: {stats['onboarded_users']}\n"
            
            if recent_activity:
                result += f"Recently Active Users ({days} days): {recent_activity[0]['recent_active_users']}\n"
            
            if role_distribution:
                result += f"\nRole Distribution:\n"
                for role in role_distribution:
                    result += f"  {role['role_name']}: {role['user_count']} users\n"
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting user metrics: {e}")
            return f"Error retrieving user metrics: {str(e)}"


class PermissionsOverviewTool(BaseTool):
    """Tool to get permissions and roles overview."""
    
    name: str = "get_permissions_overview"
    description: str = """
    Get overview of roles, permissions, and access control in the organization.
    Use this when users ask about user permissions, roles, or access management.
    """
    args_schema: Type[BaseModel] = UserAnalyticsInput

    def __init__(self, db_manager: MultiDatabaseManager):
        super().__init__()
        self.db_manager = db_manager
    
    async def _arun(self, organization_id: str, days: int = 30) -> str:
        """Get permissions overview for the organization."""
        try:
            # Get all roles in the organization
            roles_query = """
                SELECT r.name, r.is_builtin, COUNT(uor.user_id) as user_count,
                       r.created_at, r.updated_at
                FROM roles r
                LEFT JOIN user_organisation_roles uor ON r.id = uor.role_id
                WHERE r.org_id = :org_id
                GROUP BY r.id, r.name, r.is_builtin, r.created_at, r.updated_at
                ORDER BY user_count DESC
            """
            
            # Get permissions count
            permissions_query = """
                SELECT COUNT(DISTINCT p.id) as total_permissions
                FROM permissions p
                JOIN role_permissions rp ON p.id = rp.permission_id
                JOIN roles r ON rp.role_id = r.id
                WHERE r.org_id = :org_id
            """
            
            params = {"org_id": organization_id}
            
            roles_data = await self.db_manager.execute_query('authentication', roles_query, params)
            permissions_data = await self.db_manager.execute_query('authentication', permissions_query, params)
            
            result = f"Permissions Overview for Organization {organization_id}:\n\n"
            
            if permissions_data:
                result += f"Total Permissions Configured: {permissions_data[0]['total_permissions']}\n\n"
            
            if roles_data:
                result += "Roles and User Assignment:\n"
                for role in roles_data:
                    role_type = "Built-in" if role['is_builtin'] else "Custom"
                    result += f"  {role['name']} ({role_type}): {role['user_count']} users\n"
            else:
                result += "No roles configured for this organization\n"
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting permissions overview: {e}")
            return f"Error retrieving permissions overview: {str(e)}"


class UserProfilesTool(BaseTool):
    """Tool to get user profiles and detailed user information."""
    
    name: str = "get_user_profiles"
    description: str = """
    Get detailed user profiles including names, roles, and profile information.
    Use this when users ask about team members, user details, or staff information.
    """
    args_schema: Type[BaseModel] = UserAnalyticsInput

    def __init__(self, db_manager: MultiDatabaseManager):
        super().__init__()
        self.db_manager = db_manager
    
    async def _arun(self, organization_id: str, days: int = 30) -> str:
        """Get user profiles for the organization."""
        try:
            profiles_query = """
                SELECT up.full_name, up.email_address, up.phone_number, 
                       up.user_role, up.bio, u.is_active, u.is_verified,
                       u.created_at, u.updated_at
                FROM user_profiles up
                JOIN users u ON up.user_id = u.id
                WHERE up.organisation_id = :org_id
                ORDER BY up.created_at DESC
                LIMIT 20
            """
            
            params = {"org_id": organization_id}
            
            profiles_data = await self.db_manager.execute_query('authentication', profiles_query, params)
            
            if not profiles_data:
                return f"No user profiles found for organization {organization_id}"
            
            result = f"User Profiles for Organization {organization_id}:\n\n"
            
            for i, profile in enumerate(profiles_data, 1):
                result += f"{i}. {profile['full_name']}\n"
                result += f"   Email: {profile['email_address']}\n"
                result += f"   Role: {profile['user_role']}\n"
                result += f"   Status: {'Active' if profile['is_active'] else 'Inactive'}"
                result += f" | {'Verified' if profile['is_verified'] else 'Unverified'}\n"
                if profile['phone_number']:
                    result += f"   Phone: {profile['phone_number']}\n"
                if profile['bio']:
                    result += f"   Bio: {profile['bio'][:100]}{'...' if len(profile['bio']) > 100 else ''}\n"
                result += f"   Joined: {profile['created_at']}\n\n"
            
            if len(profiles_data) == 20:
                result += "... (showing first 20 profiles)\n"
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting user profiles: {e}")
            return f"Error retrieving user profiles: {str(e)}"
