"""
Social Media Database Query Tools for LangGraph Agent
Provides tools to query social media metrics and data.
"""

from typing import Dict, Any, List, Type
from langchain.tools import BaseTool
from pydantic import BaseModel, Field
from app.database.multi_db_manager import MultiDatabaseManager
from app.utils.logger import get_logger

logger = get_logger(__name__)


class SocialMetricsInput(BaseModel):
    """Input schema for social media metrics queries."""
    organization_id: str = Field(description="Organization ID to query metrics for")
    platform: str = Field(default="all", description="Social media platform (facebook, instagram, twitter, or 'all')")
    days: int = Field(default=30, description="Number of days to look back for metrics")


class FacebookMetricsTool(BaseTool):
    """Tool to get Facebook metrics and analytics."""

    name: str = "get_facebook_metrics"
    description: str = """
    Get Facebook metrics including posts, engagement, audience demographics, and performance data.
    Use this when users ask about Facebook performance, posts, likes, comments, or audience insights.
    """
    args_schema: Type[BaseModel] = SocialMetricsInput
    
    def __init__(self, db_manager: MultiDatabaseManager):
        super().__init__()
        self.db_manager = db_manager
    
    async def _arun(self, organization_id: str, platform: str = "facebook", days: int = 30) -> str:
        """Get Facebook metrics for the organization."""
        try:
            # Get Facebook account info
            account_query = """
                SELECT platform, username, login_status, social_media_user_id,
                       page_id, created_at, updated_at
                FROM social_media_accounts 
                WHERE organisation_id = :org_id AND platform = 'facebook'
            """
            
            # Get Facebook posts metrics
            posts_query = """
                SELECT COUNT(*) as total_posts,
                       AVG(CASE WHEN likes_count IS NOT NULL THEN likes_count ELSE 0 END) as avg_likes,
                       AVG(CASE WHEN comments_count IS NOT NULL THEN comments_count ELSE 0 END) as avg_comments,
                       AVG(CASE WHEN shares_count IS NOT NULL THEN shares_count ELSE 0 END) as avg_shares
                FROM posts p
                JOIN social_media_accounts sma ON p.social_media_account_id = sma.id
                WHERE sma.organisation_id = :org_id AND sma.platform = 'facebook'
                AND p.created_at >= NOW() - INTERVAL ':days days'
            """
            
            # Get recent top performing posts
            top_posts_query = """
                SELECT p.content, p.likes_count, p.comments_count, p.shares_count,
                       p.created_at, p.post_url
                FROM posts p
                JOIN social_media_accounts sma ON p.social_media_account_id = sma.id
                WHERE sma.organisation_id = :org_id AND sma.platform = 'facebook'
                AND p.created_at >= NOW() - INTERVAL ':days days'
                ORDER BY (COALESCE(p.likes_count, 0) + COALESCE(p.comments_count, 0) + COALESCE(p.shares_count, 0)) DESC
                LIMIT 5
            """
            
            params = {"org_id": organization_id, "days": days}
            
            account_data = await self.db_manager.execute_query('socials', account_query, params)
            posts_metrics = await self.db_manager.execute_query('socials', posts_query, params)
            top_posts = await self.db_manager.execute_query('socials', top_posts_query, params)
            
            # Format the response
            result = {
                "platform": "Facebook",
                "account_info": account_data,
                "metrics": posts_metrics[0] if posts_metrics else {},
                "top_posts": top_posts,
                "period_days": days
            }
            
            return f"Facebook Metrics for Organization {organization_id}:\n" + \
                   f"Account Status: {account_data[0]['login_status'] if account_data else 'Not connected'}\n" + \
                   f"Total Posts ({days} days): {posts_metrics[0]['total_posts'] if posts_metrics else 0}\n" + \
                   f"Average Likes: {posts_metrics[0]['avg_likes']:.1f if posts_metrics else 0}\n" + \
                   f"Average Comments: {posts_metrics[0]['avg_comments']:.1f if posts_metrics else 0}\n" + \
                   f"Average Shares: {posts_metrics[0]['avg_shares']:.1f if posts_metrics else 0}\n" + \
                   f"Top Performing Posts: {len(top_posts)} posts found"
            
        except Exception as e:
            logger.error(f"Error getting Facebook metrics: {e}")
            return f"Error retrieving Facebook metrics: {str(e)}"


class InstagramMetricsTool(BaseTool):
    """Tool to get Instagram metrics and analytics."""

    name: str = "get_instagram_metrics"
    description: str = """
    Get Instagram metrics including posts, engagement, stories, and audience data.
    Use this when users ask about Instagram performance, posts, likes, comments, or stories.
    """
    args_schema: Type[BaseModel] = SocialMetricsInput
    
    def __init__(self, db_manager: MultiDatabaseManager):
        super().__init__()
        self.db_manager = db_manager
    
    async def _arun(self, organization_id: str, platform: str = "instagram", days: int = 30) -> str:
        """Get Instagram metrics for the organization."""
        try:
            # Similar structure to Facebook but for Instagram
            account_query = """
                SELECT platform, username, login_status, social_media_user_id,
                       created_at, updated_at
                FROM social_media_accounts 
                WHERE organisation_id = :org_id AND platform = 'instagram'
            """
            
            posts_query = """
                SELECT COUNT(*) as total_posts,
                       AVG(CASE WHEN likes_count IS NOT NULL THEN likes_count ELSE 0 END) as avg_likes,
                       AVG(CASE WHEN comments_count IS NOT NULL THEN comments_count ELSE 0 END) as avg_comments
                FROM posts p
                JOIN social_media_accounts sma ON p.social_media_account_id = sma.id
                WHERE sma.organisation_id = :org_id AND sma.platform = 'instagram'
                AND p.created_at >= NOW() - INTERVAL ':days days'
            """
            
            params = {"org_id": organization_id, "days": days}
            
            account_data = await self.db_manager.execute_query('socials', account_query, params)
            posts_metrics = await self.db_manager.execute_query('socials', posts_query, params)
            
            return f"Instagram Metrics for Organization {organization_id}:\n" + \
                   f"Account Status: {account_data[0]['login_status'] if account_data else 'Not connected'}\n" + \
                   f"Total Posts ({days} days): {posts_metrics[0]['total_posts'] if posts_metrics else 0}\n" + \
                   f"Average Likes: {posts_metrics[0]['avg_likes']:.1f if posts_metrics else 0}\n" + \
                   f"Average Comments: {posts_metrics[0]['avg_comments']:.1f if posts_metrics else 0}"
            
        except Exception as e:
            logger.error(f"Error getting Instagram metrics: {e}")
            return f"Error retrieving Instagram metrics: {str(e)}"


class TwitterMetricsTool(BaseTool):
    """Tool to get Twitter/X metrics and analytics."""

    name: str = "get_twitter_metrics"
    description: str = """
    Get Twitter/X metrics including tweets, engagement, retweets, and audience data.
    Use this when users ask about Twitter performance, tweets, likes, retweets, or mentions.
    """
    args_schema: Type[BaseModel] = SocialMetricsInput
    
    def __init__(self, db_manager: MultiDatabaseManager):
        super().__init__()
        self.db_manager = db_manager
    
    async def _arun(self, organization_id: str, platform: str = "twitter", days: int = 30) -> str:
        """Get Twitter metrics for the organization."""
        try:
            account_query = """
                SELECT platform, username, login_status, social_media_user_id,
                       created_at, updated_at
                FROM social_media_accounts 
                WHERE organisation_id = :org_id AND platform = 'twitter'
            """
            
            posts_query = """
                SELECT COUNT(*) as total_tweets,
                       AVG(CASE WHEN likes_count IS NOT NULL THEN likes_count ELSE 0 END) as avg_likes,
                       AVG(CASE WHEN comments_count IS NOT NULL THEN comments_count ELSE 0 END) as avg_replies,
                       AVG(CASE WHEN shares_count IS NOT NULL THEN shares_count ELSE 0 END) as avg_retweets
                FROM posts p
                JOIN social_media_accounts sma ON p.social_media_account_id = sma.id
                WHERE sma.organisation_id = :org_id AND sma.platform = 'twitter'
                AND p.created_at >= NOW() - INTERVAL ':days days'
            """
            
            params = {"org_id": organization_id, "days": days}
            
            account_data = await self.db_manager.execute_query('socials', account_query, params)
            posts_metrics = await self.db_manager.execute_query('socials', posts_query, params)
            
            return f"Twitter Metrics for Organization {organization_id}:\n" + \
                   f"Account Status: {account_data[0]['login_status'] if account_data else 'Not connected'}\n" + \
                   f"Total Tweets ({days} days): {posts_metrics[0]['total_tweets'] if posts_metrics else 0}\n" + \
                   f"Average Likes: {posts_metrics[0]['avg_likes']:.1f if posts_metrics else 0}\n" + \
                   f"Average Replies: {posts_metrics[0]['avg_replies']:.1f if posts_metrics else 0}\n" + \
                   f"Average Retweets: {posts_metrics[0]['avg_retweets']:.1f if posts_metrics else 0}"
            
        except Exception as e:
            logger.error(f"Error getting Twitter metrics: {e}")
            return f"Error retrieving Twitter metrics: {str(e)}"


class SocialOverviewTool(BaseTool):
    """Tool to get overall social media overview across all platforms."""

    name: str = "get_social_overview"
    description: str = """
    Get a comprehensive overview of all social media platforms and their performance.
    Use this when users ask for general social media performance or want to compare platforms.
    """
    args_schema: Type[BaseModel] = SocialMetricsInput
    
    def __init__(self, db_manager: MultiDatabaseManager):
        super().__init__()
        self.db_manager = db_manager
    
    async def _arun(self, organization_id: str, platform: str = "all", days: int = 30) -> str:
        """Get overview of all social media platforms."""
        try:
            overview_query = """
                SELECT sma.platform, sma.username, sma.login_status,
                       COUNT(p.id) as total_posts,
                       AVG(CASE WHEN p.likes_count IS NOT NULL THEN p.likes_count ELSE 0 END) as avg_likes,
                       AVG(CASE WHEN p.comments_count IS NOT NULL THEN p.comments_count ELSE 0 END) as avg_comments,
                       SUM(CASE WHEN p.likes_count IS NOT NULL THEN p.likes_count ELSE 0 END) as total_likes,
                       SUM(CASE WHEN p.comments_count IS NOT NULL THEN p.comments_count ELSE 0 END) as total_comments
                FROM social_media_accounts sma
                LEFT JOIN posts p ON sma.id = p.social_media_account_id 
                    AND p.created_at >= NOW() - INTERVAL ':days days'
                WHERE sma.organisation_id = :org_id
                GROUP BY sma.platform, sma.username, sma.login_status
                ORDER BY sma.platform
            """
            
            params = {"org_id": organization_id, "days": days}
            overview_data = await self.db_manager.execute_query('socials', overview_query, params)
            
            if not overview_data:
                return f"No social media accounts found for organization {organization_id}"
            
            result = f"Social Media Overview for Organization {organization_id} (Last {days} days):\n\n"
            
            for platform_data in overview_data:
                result += f"Platform: {platform_data['platform'].title()}\n"
                result += f"  Username: {platform_data['username']}\n"
                result += f"  Status: {'Connected' if platform_data['login_status'] else 'Disconnected'}\n"
                result += f"  Posts: {platform_data['total_posts']}\n"
                result += f"  Total Likes: {platform_data['total_likes']}\n"
                result += f"  Total Comments: {platform_data['total_comments']}\n"
                result += f"  Avg Likes per Post: {platform_data['avg_likes']:.1f}\n"
                result += f"  Avg Comments per Post: {platform_data['avg_comments']:.1f}\n\n"
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting social overview: {e}")
            return f"Error retrieving social media overview: {str(e)}"
