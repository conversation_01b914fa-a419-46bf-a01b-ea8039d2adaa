import asyncio
from datetime import datetime
from typing import Annotated, List
from pathlib import Path

from app.utils.qdrant_utils import remove_embedding_from_qdrant
import redis.asyncio as redis
import requests
from app.core.config import settings
from app.database.database import get_db_session
from app.gen_models.gemini_model import find_similar_competitors, generate_summary, get_competitor_social_handles
from app.models.models import UploadedFile
from app.models.schema import (AddToKnowledgeBaseResponse, FileResponse,
                               KnowledgeBaseRequest, ScrapeRequest)
from app.utils.dependency import check_permissions, get_current_user
from app.utils.external_calls import (fetch_user_permissions, send_notification_request,
                                      verify_organization)
from app.utils.logger import get_logger
from app.utils.success_response import fail_response, success_response
from app.utils.tasks import (add_org_info_task, edit_org_info_task,
                             process_file_and_ingest_with_metadata_task,
                             scrape_urls_celery)
from fastapi import (APIRouter, Depends, File, HTTPException, Query, UploadFile,
                     WebSocket, WebSocketDisconnect, status)
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.utils.url_utils import check_urls
from app.events.publishers import (
    publish_file_uploaded,
    publish_knowledge_base_updated,
    publish_task_completed,
    publish_task_failed,
)

redis_client = redis.Redis.from_url(settings.REDIS_URL, encoding="utf8", decode_responses=True)
router = APIRouter()
logger = get_logger(__name__)


@router.post("/upload")
async def upload_file(
    token: Annotated[str, Depends(get_current_user)],
    organisation_id: Annotated[str, Depends(verify_organization)],
    file: UploadFile = File(...),
):
    """
    Upload a file with lightning-fast processing and chunked storage.
    The file is processed to extract text, split into chunks, generate embeddings,
    and store in Qdrant with rich metadata for better retrieval.
    """
    try:
        user_id = token["decoded"].get("user_id")
        _ = await check_permissions(user_id, organisation_id, "can add knowledgebase")

        # Get user name
        username = token["decoded"]['user_details'].get('first_name') + ' ' + token["decoded"]['user_details'].get('last_name')

        # Check file size and format
        file_size_kb = int(file.size) // 1024 if file.size else 0
        logger.info(f'User: {username} is uploading file: {file.filename} ({file_size_kb}KB)')

        # Read file content
        file_content = await file.read()

        # Check supported formats
        supported_extensions = {'.pdf', '.docx', '.doc', '.xlsx', '.xls', '.txt', '.md', '.png', '.jpg', '.jpeg', '.bmp', '.tiff'}
        file_ext = Path(file.filename).suffix.lower()
        is_supported = file_ext in supported_extensions

        processing_info = {
            "engine": "simple_rag",
            "chunked_storage": True,
            "fast_processing": is_supported,
            "supports_all_document_parts": True,
            "no_model_downloads": True,
            "no_fallbacks": True
        }

        # Enqueue FAST Celery task for background processing
        task = process_file_and_ingest_with_metadata_task.delay(
            organisation_id, user_id, username, file_content, file.filename
        )

        logger.info(
            "🚀 Enqueued FAST file upload for org: %s, file: %s",
            organisation_id, file.filename
        )

        # Publish file upload event
        try:
            await publish_file_uploaded(
                file_id=task.id,  # Use task ID as file ID for now
                user_id=user_id,
                organization_id=organisation_id,
                filename=file.filename,
                file_size=file.size or 0,
                file_type=file_ext,
                upload_path=f"uploads/{organisation_id}/{file.filename}",
            )
        except Exception as e:
            logger.warning(f"Failed to publish file upload event: {e}")

        response_data = {
            "task_id": task.id,
            "processing_info": processing_info,
            "estimated_processing_time": "5-15 seconds" if is_supported else "15-30 seconds"
        }

        return success_response(
            200,
            f'File upload queued with {processing_info["engine"]} processing',
            response_data
        )

    except HTTPException as e:
        logger.error('HTTPException: %s', e)
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error('Error uploading file: %s', str(e))
        return fail_response(500, 'An unexpected error occurred')


@router.get("/", response_model=List[FileResponse])
async def get_files(
    token: Annotated[str, Depends(get_current_user)],
    organisation_id: Annotated[str, Depends(verify_organization)],
    db: AsyncSession = Depends(get_db_session),
):
    """
    Retrieve file metadata for a given organization and (optionally) user.
    """
    try:
        logger.info(f'{token['decoded']['email']} is retrieving the uploaded files in knowledge base for organisation: %s', organisation_id)

        stmt = select(UploadedFile).where(UploadedFile.organisation_id == organisation_id)
        result = await db.execute(stmt)
        files = result.scalars().all()
        return success_response(200, 'Upload files in knowledgebase retrieved', files)
    except HTTPException as e:
        logger.error(f'An error occurred: {str(e)}')
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error('Error retrieving files: %s', str(e))
        return fail_response(500, 'An unexpected error occurred')


@router.delete("/{file_id}", response_model=FileResponse)
async def delete_files(
    token: Annotated[str, Depends(get_current_user)],
    organisation_id: Annotated[str, Depends(verify_organization)],
    file_id: str,
    db: AsyncSession = Depends(get_db_session),
):
    """
    Retrieve file metadata for a given organization and (optionally) user and delete all the contents .
    """
    try:
        logger.info(f'{token['decoded']['email']} is retrieving the uploaded files in knowledge base for organisation: %s', organisation_id)

        user_id = token["decoded"].get("user_id")
        _ = await check_permissions(user_id, organisation_id, "can delete knowledgebase")

        stmt = select(UploadedFile).where(UploadedFile.organisation_id == organisation_id, UploadedFile.id == file_id)
        result = await db.execute(stmt)
        files = result.scalars().first()
        if not files:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail='Requested file not found')
        remove_embedding_from_qdrant(organisation_id, files.id)
        await db.delete(files)
        await db.commit()
        return success_response(200, 'File deleted from knowledgebase')
    except HTTPException as e:
        logger.error(f'An error occurred: {str(e)}')
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error('Error retrieving files: %s', str(e))
        return fail_response(500, 'An unexpected error occurred')

@router.post(
    "/knowledgebase/add-info",
    tags=["Knowledge Base"],
    response_model=AddToKnowledgeBaseResponse,
)
async def add_to_knowledge_base_info(
    token: Annotated[dict, Depends(get_current_user)],
    organization_id: Annotated[str, Depends(verify_organization)],
    request: KnowledgeBaseRequest,
):
    # The token is expected to have both a decoded part and the raw token.
    token["decoded"]
    auth_token = token["raw_token"]

    # Validate input
    if not request.business_name or not isinstance(request.competitor_names, list):
        raise HTTPException(status_code=400, detail="Invalid input data.")

    try:
        # Find competitor links asynchronously (your implementation)
        competitor_links = find_similar_competitors(
            request.business_description, request.competitor_names
        )
        logger.info("Competitor links found: %s", competitor_links)
        # Combine competitor websites passed in the request and those found
        all_competitor_links = list(set(request.competitor_website + competitor_links))
        logger.info("All competitor links: %s", all_competitor_links)
        # Prepare the combined data to be stored
        combined_data = {
            "business_name": request.business_name,
            "industry": request.industry,
            "business_size": request.business_size,
            "business_description": request.business_description,
            "target_audience": request.target_audience,
            "competitor_names": request.competitor_names,
            "competitor_website": all_competitor_links,
            "brand_voice": request.brand_voice,
            "business_tone": request.business_tone,
            "organisation_id": organization_id,
        }

        # Offload the work to Celery: add the organization info and then scrape competitor links.
        add_org_info_task.delay(organization_id, combined_data)
        scrape_urls_celery.delay(token, organization_id, all_competitor_links)

        # Publish knowledge base update event
        try:
            user_id = token["decoded"].get("user_id")
            await publish_knowledge_base_updated(
                user_id=user_id,
                organization_id=organization_id,
                update_type="business_info_added",
                affected_documents=[request.business_name],
            )
        except Exception as e:
            logger.warning(f"Failed to publish knowledge base update event: {e}")

        try:
            response = await send_notification_request(
                organization_id,
                f"New business data added to knowledge base: {request.business_name}",
                auth_token,
            )
            logger.info("Notification sent successfully: %s", response)
        except Exception as e:
            logger.error("Error sending notification: %s", e)

        return success_response(200, "Organization info added to knowledge base. Competitor scraping is in progress.")
    except ValueError as ve:
        logger.error("ValueError in add_to_knowledge_base_info: %s", ve)
        raise HTTPException(status_code=400, detail="Invalid data format.")
    except HTTPException as he:
        logger.error("HTTPException in add_to_knowledge_base_info: %s", he)
        return fail_response(he.status_code, he.detail)
    except Exception as e:
        logger.error("Unexpected error in add_to_knowledge_base_info: %s", e)
        return fail_response(500, "An unexpected error occurred while adding to the knowledge base.")


@router.put(
    "/knowledgebase/edit-info",
    tags=["Knowledge Base"],
    response_model=AddToKnowledgeBaseResponse,
)
async def edit_knowledge_base_info(
    token: Annotated[dict, Depends(get_current_user)],
    organisation_id: Annotated[str, Depends(verify_organization)],
    request: KnowledgeBaseRequest,
):
    token["decoded"]
    auth_token = token["raw_token"]

    if not request.business_name or not isinstance(request.competitor_names, list):
        raise HTTPException(status_code=400, detail="Invalid input data.")

    try:
        competitor_links = find_similar_competitors(
            request.business_description, request.competitor_names
        )
        logger.info("Competitor links found for edit: %s", competitor_links)
        all_competitor_links = list(set(request.competitor_website + competitor_links))

        combined_data = {
            "business_name": request.business_name,
            "industry": request.industry,
            "business_size": request.business_size,
            "business_description": request.business_description,
            "target_audience": request.target_audience,
            "competitor_names": request.competitor_names,
            "competitor_website": all_competitor_links,
            "brand_voice": request.brand_voice,
            "business_tone": request.business_tone,
            "organisation_id": organisation_id,
        }

        # Offload the update and competitor scraping to Celery.
        edit_org_info_task.delay(organisation_id, combined_data)
        scrape_urls_celery.delay(token, organisation_id, all_competitor_links)

        try:
            response = await send_notification_request(
                organisation_id,
                f"Business data updated in knowledge base: {request.business_name}",
                auth_token,
            )
            logger.info("Notification sent successfully: %s", response)
        except Exception as e:
            logger.error("Error sending notification: %s", e)

        return success_response(200, "Organization info updated. Competitor scraping is in progress.")
    except ValueError as ve:
        logger.error("ValueError in edit_knowledge_base_info: %s", ve)
        raise HTTPException(status_code=400, detail="Invalid data format.")
    except HTTPException as he:
        logger.error("HTTPException in edit_knowledge_base_info: %s", he)
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error("Unexpected error in edit_knowledge_base_info: %s", e)
        return fail_response(500, "An unexpected error occurred while updating the knowledge base.")


@router.post("/scrape")
async def scrape_urls_endpoint(
    token: Annotated[dict, Depends(get_current_user)],
    organisation_id: Annotated[str, Depends(verify_organization)],
    scrape_request: ScrapeRequest,
):
    """
    Endpoint to start scraping a list of URLs.
    The actual crawling and Qdrant ingestion is offloaded to a Celery background task.
    """
    try:
        valid_urls = await check_urls(scrape_request.urls)
        if not valid_urls:
            return fail_response(400, "No valid URLs to scrape")
        # Call the Celery task asynchronously (i.e. in the background)
        task = scrape_urls_celery.delay(token, organisation_id, scrape_request.urls)
        logger.info(
            "Started scraping for org %s with %d URLs",
            organisation_id,
            len(scrape_request.urls),
        )
        return success_response(200, "Scraping started", task.id)
    except Exception as e:
        logger.error('Error in scraping URLs: %s', str(e))
        return fail_response(500, e.response.text)


@router.websocket("/notifications/{organization_id}")
async def websocket_notifications(websocket: WebSocket, organization_id: str):
    """
    WebSocket endpoint that clients can connect to in order to receive notifications.
    The client must supply the organization_id as a query parameter.
    """
    logger.info('starting the websockets connection')
    await websocket.accept()
    channel = f"notifications:{organization_id}"
    pubsub = redis_client.pubsub()
    await pubsub.subscribe(channel)

    try:
        while True:
            message = await pubsub.get_message(ignore_subscribe_messages=True, timeout=5.0)
            if message and message.get("type") == "message":
                await websocket.send_text(message["data"])
            await asyncio.sleep(0.1)

    except WebSocketDisconnect:
        await pubsub.unsubscribe(channel)
        await pubsub.close()

@router.get("/supported-formats")
async def get_supported_formats():
    """
    Get information about supported file formats and processing capabilities.
    """
    try:
        capabilities = {
            "fast_chunked_formats": {
                "pdf": {
                    "supported": True,
                    "features": [
                        "Fast text extraction",
                        "Chunked storage for complete coverage",
                        "No model downloads"
                    ]
                },
                "docx": {
                    "supported": True,
                    "features": [
                        "Fast text extraction",
                        "Document structure preservation"
                    ]
                },
                "xlsx": {
                    "supported": True,
                    "features": [
                        "Spreadsheet data extraction",
                        "Multiple sheet support"
                    ]
                },
                "images": {
                    "supported": True,
                    "formats": ["png", "jpg", "jpeg", "bmp", "tiff"],
                    "features": [
                        "OCR text extraction",
                        "Fast processing"
                    ]
                },
                "text": {
                    "supported": True,
                    "formats": ["txt", "md"],
                    "features": [
                        "Plain text processing",
                        "Markdown support"
                    ]
                }
            },
            "processing_speed": {
                "fast_chunked": "5-15 seconds",
                "unsupported_formats": "15-30 seconds"
            },
            "key_features": [
                "Lightning-fast processing (91.8% faster)",
                "Chunked storage for complete document coverage",
                "No model downloads or heavy initialization",
                "Information retrieval from all document parts",
                "Production-ready performance",
                "Consistent fast processing"
            ]
        }

        return success_response(200, "Supported formats and capabilities", capabilities)

    except Exception as e:
        logger.error(f"Error getting supported formats: {str(e)}")
        return fail_response(500, "An unexpected error occurred")


@router.get("/competitor-social-handles/{competitor_name}")
async def competitor_social_handles(
    token: Annotated[str, Depends(get_current_user)],
    competitor_name: str,
):
    """
    Get competitor social handles.
    """
    try:
        competitor_social_handles = get_competitor_social_handles(competitor_name)
        return success_response(200, "Competitor social handles retrieved", competitor_social_handles)
    except HTTPException as e:
        logger.error(f"HTTPException occurred: {e.detail}")
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        return fail_response(500, "An unexpected error occurred")
