from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any

from app.models.schema import (
    SimpleContentRepurposeRequest,
    SimpleContentRepurposeResponse,
    SocialMediaPlatform
)
from app.utils.social_media_generator import generate_all_platform_content
from app.utils.dependency import get_current_user
from app.utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter()


@router.post("/repurpose", response_model=SimpleContentRepurposeResponse)
async def repurpose_content(
    request: SimpleContentRepurposeRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Repurpose content for multiple social media platforms.

    Takes original content and a list of platforms, returns repurposed content for each platform.
    This is a simple, fast API that returns results immediately.

    Note: For Instagram, an image will be generated instead of text content.
    The response will contain an image URL for Instagram and text content for other platforms.
    """
    try:
        # Validate input
        if not request.content or len(request.content.strip()) < 10:
            raise HTTPException(status_code=400, detail="Content must be at least 10 characters long")

        if not request.platforms:
            raise HTTPException(status_code=400, detail="At least one platform must be specified")

        # Validate platforms
        valid_platforms = [p.value for p in SocialMediaPlatform]
        for platform in request.platforms:
            if platform.value not in valid_platforms:
                raise HTTPException(status_code=400, detail=f"Invalid platform: {platform.value}")

        # Extract organization_id from current user
        organization_id = current_user["decoded"].get("organisation_id", "default")

        # Check if Instagram is in the platforms (for logging)
        platform_strings = [p.value for p in request.platforms]
        if "instagram" in platform_strings:
            logger.info(f"Instagram content requested - will generate image instead of text for organization: {organization_id}")

        # Generate content for all platforms
        repurposed_content = await generate_all_platform_content(request.content, platform_strings, organization_id)

        logger.info(f"Successfully repurposed content for {len(request.platforms)} platforms")

        return SimpleContentRepurposeResponse(
            repurposed_content=repurposed_content
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error repurposing content: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to repurpose content: {str(e)}")


@router.get("/platforms")
async def get_supported_platforms():
    """
    Get list of supported social media platforms.

    Returns information about all supported social media platforms including content type.
    Instagram returns images while other platforms return text content.
    """
    platforms = [
        {
            "name": platform.value,
            "display_name": platform.value.title(),
            "content_type": "image" if platform == SocialMediaPlatform.INSTAGRAM else "text",
            "max_characters": {
                SocialMediaPlatform.TWITTER: 280,
                SocialMediaPlatform.FACEBOOK: 2200,
                SocialMediaPlatform.LINKEDIN: 3000,
                SocialMediaPlatform.INSTAGRAM: 2200
            }[platform] if platform != SocialMediaPlatform.INSTAGRAM else None
        }
        for platform in SocialMediaPlatform
    ]

    return {
        "supported_platforms": platforms,
        "total_platforms": len(SocialMediaPlatform)
    }
