import uuid
from datetime import datetime

from app.database.database import Base
from sqlalchemy import Column, DateTime, Index, Integer, String, Text


# SQLAlchemy Models
class ChatThread(Base):
    __tablename__ = "chat_threads"
    id = Column(String, primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    title = Column(String, nullable=True)
    organization_id = Column(String, index=True)
    user_id = Column(String, index=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now)

    __table_args__ = (Index("idx_chatthread_org", "organization_id"),)


class ChatMessage(Base):
    __tablename__ = "chat_messages"
    id = Column(String, primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    thread_id = Column(String, index=True)
    role = Column(String, index=True)
    message = Column(Text)
    file_ids = Column(Text, nullable=True)  # Comma-separated list of file IDs
    timestamp = Column(DateTime, default=lambda: datetime.now())

    __table_args__ = (
        Index("idx_chatmessage_thread", "thread_id"),
        Index("idx_chatmessage_timestamp", "timestamp"),
    )


class UploadedFile(Base):
    __tablename__ = "uploaded_files"
    id = Column(String, primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    filename = Column(String, nullable=False)
    filesize = Column(Integer)
    filetype = Column(String)
    user_id = Column(String, index=True)
    username = Column(String, nullable=True)
    organisation_id = Column(String, index=True)
    summary = Column(Text)
    upload_date = Column(DateTime, default=lambda: datetime.now())
    timestamp = Column(Integer, default=lambda: int(datetime.now().timestamp()))


class GeminiFile(Base):
    __tablename__ = "gemini_files"
    id = Column(String, primary_key=True, index=True)  # Gemini file ID
    filename = Column(String, nullable=False)
    filetype = Column(String)
    user_id = Column(String, index=True)
    organisation_id = Column(String, index=True)
    upload_date = Column(DateTime, default=lambda: datetime.now())
    expiry_date = Column(DateTime)  # Gemini files expire after 48 hours

    __table_args__ = (
        Index("idx_geminifile_user", "user_id"),
        Index("idx_geminifile_org", "organisation_id"),
    )
