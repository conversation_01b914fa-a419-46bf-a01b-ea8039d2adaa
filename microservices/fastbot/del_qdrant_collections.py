from qdrant_client import QdrantClient

# Connect to your Qdrant instance.
qdrant_client = QdrantClient(host='localhost', port=6333)

# List all collections (optional)
collections = qdrant_client.get_collections()
print(collections)

# Delete a specific collection:
# collection_name = "your_collection_name"
# qdrant_client.delete_collection(collection_name=collection_name)

# for collection in qdrant_client.get_collections().collections:
#     qdrant_client.delete_collection(collection_name=collection.name)
#     print(f"Deleted collection: {collection.name}")
