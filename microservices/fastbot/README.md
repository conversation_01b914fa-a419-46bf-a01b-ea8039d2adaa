# FastBot Service

The FastBot Service provides file chat and knowledgebase functionality with AI integration in the EllumAI backend system.

## Features

- Chat functionality with AI models
- File upload and processing
- Web scraping for content extraction
- Metrics tracking for usage analytics
- Redis integration for caching
- Database integration for persistent storage

## API Endpoints

The service exposes the following endpoints:

- `/api/v1/chat` - Chat with AI models
- `/api/v1/file` - Upload and manage files
- `/api/v1/url` - Scrape and process web content
- `/api/v1/view` - View usage metrics
- `/health` - Health check endpoint

Full API documentation is available at `/api/v1/chat/docs` when the service is running.

## Environment Variables

Create a `.env` file in the service directory with the following variables:

```
DATABASE_URL=postgresql://username:password@host:port/database
SECRET_KEY=your_secret_key
REDIS_URL=redis://redis:6379/0
GEMINI_API_KEY=your_gemini_api_key
```

## Running the Service

### Without Docker

1. Navigate to the fastbot service directory:
   ```bash
   cd microservices/fastbot
   ```

2. Create a virtual environment:
   ```bash
   python -m venv venv
   ```

3. Activate the virtual environment:
   - On Windows: `venv\Scripts\activate`
   - On macOS/Linux: `source venv/bin/activate`

4. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

5. Start the service:
   ```bash
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8001
   ```

### With Docker

1. Build and run the Docker container:
   ```bash
   docker build -t fastbot .
   docker run -p 8001:8001 fastbot
   ```

### Using Docker Compose

From the root of the project:
```bash
docker-compose up fastbot
```
