import scrapy


class CompetitorSpider(scrapy.Spider):
    name = 'competitor_spider'

    def __init__(self, urls=None, *args, **kwargs):
        super(Competitor<PERSON>pider, self).__init__(*args, **kwargs)
        self.start_urls = urls if urls else []

    def parse(self, response):
        company_info = {
            'url': response.url,
            'title': self.extract_title(response),
            'description': self.extract_description(response),
            'products_services': self.extract_products_services(response),
            'social_media_handles': self.extract_social_media_handles(response)
        }
        yield company_info

    def extract_title(self, response):
        title = response.css('title::text').get() or response.xpath('//title/text()').get()
        return title.strip() if title else ''

    def extract_description(self, response):
        # Extract meta description
        description = response.css('meta[name="description"]::attr(content)').get()
        if not description:
            description = response.xpath('//meta[@name="description"]/@content').get()
        if not description:
            # Fallback to first paragraph text
            description = response.css('p::text').get()
        return description.strip() if description else ''

    def extract_products_services(self, response):
        # Extract common products/services patterns
        products_services = response.css('section.products, div.services').xpath('.//text()').getall()
        if not products_services:
            products_services = response.css('li').xpath('.//text()').getall()
        products_services = [ps.strip() for ps in products_services if ps.strip()]
        return products_services

    def extract_social_media_handles(self, response):
        # Extract common social media patterns
        social_media_handles = response.css(
            'a[href*="twitter.com"], a[href*="facebook.com"], a[href*="linkedin.com"], a[href*="instagram.com"]').xpath(
            '@href').getall()
        return social_media_handles