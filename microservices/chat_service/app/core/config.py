from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Class to hold application's config values."""

    SECRET_KEY: str
    ALGORITHM: str = "HS256"
    CSE_API_KEY: str
    CSE_ID: str
    OPENAI_API_KEY: str
    AUTH_SERVICE_URL: str
    SETTINGS_SERVICE_URL: str
    NOTIFICATION_SERVICE_URL: str

    # Database
    CHAT_DATABASE_URL: str
    LOCAL_CHAT_DATABASE_URL: str

    # Embeddings
    EMBEDDING_TIMEOUT: int

    # celery
    CELERY_RESULT_BACKEND: str
    CELERY_BROKER_URL: str

    # MINIO SETUP
    MINIO_ENDPOINT: str
    MINIO_ROOT_USER: str
    MINIO_ROOT_PASSWORD: str
    MINIO_SECURE: str = False
    MINIO_BUCKET: str
    ENV: str

    class Config:
        env_file = ".env"
        extra = "ignore"


settings = Settings()
