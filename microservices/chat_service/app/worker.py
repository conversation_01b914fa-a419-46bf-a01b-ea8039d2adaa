from celery import Celery
import asyncio
from app.services.chromadb_service import process_large_file
from app.celery_config import broker_url, result_backend
import logging
from io import BytesIO

logger = logging.getLogger(__name__)

celery_app = Celery(
    "worker",
    backend=result_backend,
    broker=broker_url
)

celery_app.conf.task_routes = {"app.tasks.*": {"queue": "celery"}}
celery_app.conf.update(
    worker_concurrency=4,
    broker_connection_retry_on_startup=True,
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json')


@celery_app.task
def process_file_task(file_content, file_metadata):
    async def process_and_update():
        logger.info(f"Background task started for file: {file_metadata['filename']}")
        file_content_io = BytesIO(file_content)
        # Process the file in chunks, index in ChromaDB
        try:
            logger.info(f"Processing file: {file_metadata['filename']}")
            summary = await process_large_file(file_content_io, file_metadata)
            logger.info(f"File processed successfully: {file_metadata['filename']}")
            logger.info(f"File summary: {summary}")
            await update_file_summary(summary, file_metadata['id'])
            return summary
        except Exception as e:
            logger.error(f"Error processing file {file_metadata['filename']}: {e}")
            raise e

    try:
        loop = asyncio.get_event_loop()
        if loop.is_running():
            return loop.create_task(process_and_update())
        else:
            return asyncio.run(process_and_update())
    except Exception:
        return asyncio.run(process_and_update())


async def update_file_summary(summary, file_id):
    from app.database import async_session
    from app.models import FileUpload

    logger.info('Updating summary...')
    async with async_session() as session:
        file_record = await session.get(FileUpload, file_id)
        if file_record:
            file_record.summary = summary
            # FIXME: Raises an error of another process still running.
            await session.commit()
            logger.info(f"File summary updated for ID: {file_id}")
        else:
            logger.warning(f"No file record found for ID: {file_id}")
