import logging
from twisted.internet import defer, reactor
from threading import Thread
import asyncio
from scrapy.crawler import Crawler<PERSON><PERSON><PERSON>
from scrapy.utils.project import get_project_settings
from scrapy.signalmanager import dispatcher
from scrapy import signals
from app.competitor_scraper.competitor_scraper.spiders.competitor_spider import CompetitorSpider
from app.competitor_scraper.competitor_scraper.spiders.urls_spider import ScrapeAllDataSpider

scraped_items = []


def collect_items(item, response, spider):
    scraped_items.append(item)


async def run_scraper(urls):
    try:
        logging.info("Starting the scraping process.")
        global scraped_items
        scraped_items = []

        # Configure and run Scrapy programmatically
        settings = get_project_settings()
        runner = CrawlerRunner(settings)

        # Connect the signal to the callback
        dispatcher.connect(collect_items, signal=signals.item_passed)

        @defer.inlineCallbacks
        def crawl():
            yield runner.crawl(CompetitorSpider, urls=urls)
            reactor.stop()

        def run_crawl():
            crawl()  # Start the crawling process
            if not reactor.running:
                reactor.run()  # the script will block here until the crawling is finished
            else:
                logging.warning("Reactor is already running. Skipping reactor.run().")

        # Run the crawl in a separate thread
        thread = Thread(target=run_crawl)
        thread.start()
        thread.join()

        logging.info("Scraping process completed.")
        return scraped_items
    except Exception as e:
        logging.error(f"Error running scraper: {e}", exc_info=True)
        return []

async def run_urls_scraper(urls):
    try:
        logging.info("Starting the scraping process.")
        global scraped_items
        scraped_items = []

        # Configure and run Scrapy programmatically
        settings = get_project_settings()
        runner = CrawlerRunner(settings)

        # Connect the signal to the callback
        dispatcher.connect(collect_items, signal=signals.item_passed)

        @defer.inlineCallbacks
        def crawl():
            yield runner.crawl(ScrapeAllDataSpider, urls=urls)
            reactor.stop()

        def run_crawl():
            crawl()  # Start the crawling process
            if not reactor.running:
                reactor.run()  # the script will block here until the crawling is finished
            else:
                logging.warning("Reactor is already running. Skipping reactor.run().")

        # Run the crawl in a separate thread
        thread = Thread(target=run_crawl)
        thread.start()
        thread.join()

        logging.info("Scraping process completed.")
        return scraped_items
    except Exception as e:
        logging.error(f"Error running scraper: {e}", exc_info=True)
        return []
