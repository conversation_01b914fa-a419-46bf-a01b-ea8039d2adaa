import httpx
import os
from dotenv import load_dotenv

load_dotenv()


async def interact_with_ai_model(prompt, thread_id):
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                "https://api.openai.com/v1/chat/completions",
                headers={"Authorization": f"Bearer {os.getenv('OPENAI_API_KEY')}"},
                json={
                    "model": "gpt-3.5-turbo",
                    "messages": [
                        {"role": "system",
                         "content": f"Analyze the prompt within the context of thread {thread_id}."
                         },
                        {"role": "user",
                         "content": prompt
                         }
                    ]
                }
            )
            response.raise_for_status()
            response_data = response.json()
            feedback = response_data['choices'][0]['message']['content'].strip()
        except httpx.HTTPStatusError as e:
            print(f"HTTP error occurred: {e.response.status_code}")
            raise RuntimeError(f"HTTP error occurred: {e.response.status_code}")
        except httpx.RequestError as e:
            print(f"An error occurred while requesting: {e}")
            raise RuntimeError(f"An error occurred while requesting: {e}")

    return feedback
