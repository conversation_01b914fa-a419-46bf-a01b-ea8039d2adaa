import asyncio
import os

import chromadb
from app.core.logger import get_logger
from app.models import ChatHistory, FileUpload
from app.services.openai_service import generate_summary
from app.utils.dependency import extract_text_from_file
from fastapi import HTTPException
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_openai import OpenAIEmbeddings
from sqlalchemy.ext.asyncio import AsyncSession

logger = get_logger(__name__)


# Initialize the ChromaDB Persistent Client globally with the correct path
CHROMADB_PATH = os.getenv("CHROMADB_PATH", "./chroma_persistence")

# Create ChromaDB client
client = chromadb.PersistentClient(path=CHROMADB_PATH)


def get_organisation_collection(organisation_id: str):
    """
    Get or create a ChromaDB collection specific to an organization.

    Parameters:
    - organisation_id (str): The ID of the organization.

    Returns:
    - ChromaDB Collection
    """
    collection_name = f"file_embeddings_{organisation_id}"

    if collection_name in [col.name for col in client.list_collections()]:
        return client.get_collection(collection_name)
    return client.create_collection(collection_name)


async def store_in_chromadb(business_data: dict):
    """
    Stores business-related data in the ChromaDB collection for the specified organization.

    Parameters:
    - business_data (dict): A dictionary containing all the business-related data.
    """
    try:
        organisation_id = business_data.get("organisation_id")
        if not organisation_id:
            raise ValueError("Organisation ID is required to store data.")

        # Get or create the collection for the organization
        collection = get_organisation_collection(organisation_id)

        # Combine all text fields to form the document for embedding
        business_text = (
            f"Business Name: {business_data['business_name']}\n"
            f"Industry: {business_data['industry']}\n"
            f"Business Size: {business_data['business_size']}\n"
            f"Business Description: {business_data['business_description']}\n"
            f"Target Audience: {business_data['target_audience']}\n"
            f"Competitor Names: {', '.join(business_data['competitor_names'])}\n"
            f"Competitor Websites: {', '.join(business_data['competitor_website'])}\n"
            f"Brand Voice: {business_data['brand_voice']}\n"
            f"Business Tone: {business_data['business_tone']}\n"
            f"Organisation ID: {organisation_id}\n"
        )

        # Split the business text into chunks
        text_splitter = RecursiveCharacterTextSplitter(chunk_size=500, chunk_overlap=50)
        chunks = text_splitter.split_text(business_text)

        if not chunks:
            raise ValueError("No text chunks generated for embedding.")

        # Generate embeddings for the chunks
        embeddings = OpenAIEmbeddings(model="text-embedding-3-large")
        chunk_embeddings = await asyncio.to_thread(embeddings.embed_documents, chunks)

        if not chunk_embeddings:
            raise ValueError("Embeddings could not be generated for the chunks.")

        # Add each chunk to the collection
        for i, (chunk, chunk_embedding) in enumerate(zip(chunks, chunk_embeddings)):
            doc_id = f"{business_data['business_name']}_{i}"
            collection.add(
                documents=[chunk],
                embeddings=[chunk_embedding],
                metadatas=[
                    {
                        "business_name": business_data["business_name"],
                        "chunk_index": i,
                        "competitor_names": str(business_data["competitor_names"]),
                        "business_description": business_data["business_description"],
                        "organisation_id": organisation_id,
                    }
                ],
                ids=[doc_id],
            )

        logger.info(
            f"Successfully stored business data for {business_data['business_name']} in ChromaDB."
        )

    except Exception as e:
        logger.error(f"Error storing business data: {e}")
        raise HTTPException(
            status_code=500, detail=f"An error occurred while storing data: {str(e)}"
        )


async def store_urls_data(scraped_data: dict, organisation_id: str):
    """
    Store scraped data into a ChromaDB collection.

    Args:
        scraped_data (dict): Scraped data from the website.
        organisation_id (str): ID of the organization to associate the data with.

    Raises:
        HTTPException: If an error occurs during data storage.
    """
    try:
        if not organisation_id:
            raise ValueError("Organisation ID is required to store data.")

        # Get or create the collection for the organization
        collection = get_organisation_collection(organisation_id)

        # Prepare data for embedding
        data_text = "\n".join(
            f"{key.capitalize()}: {str(value)}"
            for key, value in scraped_data.items()
            if isinstance(value, (str, list, dict))
        )

        # Ensure data_text is valid
        if not data_text.strip():
            raise ValueError("No valid text content found in scraped data.")

        # Split text into chunks
        text_splitter = RecursiveCharacterTextSplitter(chunk_size=500, chunk_overlap=50)
        chunks = text_splitter.split_text(data_text)

        # Generate embeddings for chunks
        embeddings = OpenAIEmbeddings(model="text-embedding-3-large")
        chunk_embeddings = await asyncio.to_thread(embeddings.embed_documents, chunks)

        # Add each chunk to the collection
        for i, (chunk, embedding) in enumerate(zip(chunks, chunk_embeddings)):
            doc_id = f"{scraped_data.get('url', 'unknown')}_{i}"
            collection.add(
                documents=[chunk],
                embeddings=[embedding],
                metadatas=[
                    {
                        "url": scraped_data.get("url", ""),
                        "organisation_id": organisation_id,
                        "chunk_index": i,
                    }
                ],
                ids=[doc_id],
            )

        logger.info(
            f"Successfully stored scraped data for URL: {scraped_data.get('url', 'unknown')}"
        )

    except Exception as e:
        logger.error(f"Error storing scraped data: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"An error occurred while storing scraped data: {str(e)}",
        )


async def query_chromadb(message: str, organisation_id: str):
    """
    Query ChromaDB for the closest matching document to the given message within the organization's collection.

    Parameters:
    - message (str): The query message.
    - organisation_id (str): The ID of the organization.

    Returns:
    - The matched document or None.
    """
    try:
        collection = get_organisation_collection(organisation_id)
        logger.info(f"collection: {collection}")
        embeddings = OpenAIEmbeddings(model="text-embedding-3-large")

        query_embedding = embeddings.embed_query(message)
        logger.debug(f"query_embedding: {query_embedding}")
        logger.info("Quering embedding returned, moving to getting collection results")

        result = collection.query(query_embeddings=[query_embedding], n_results=10)

        logger.debug(f"Query result: {result}")
        logger.info("Query results returned")

        if result.get("documents"):
            return result["documents"][0]
        return None

    except Exception as e:
        logger.error(f"Error querying ChromaDB for organisation {organisation_id}: {e}")
        raise HTTPException(
            status_code=500, detail="An error occurred while querying data."
        )


async def delete_file_embeddings(file_id: str, organisation_id: str):
    """
    Delete the embeddings associated with a file ID from the organization's ChromaDB collection.

    Parameters:
    - file_id (str): The ID of the file to delete.
    - organisation_id (str): The ID of the organization.
    """
    try:
        collection = get_organisation_collection(organisation_id)
        collection.delete(ids=[file_id])

        logger.info(
            f"Embeddings for file {file_id} successfully deleted from ChromaDB."
        )

    except Exception as e:
        logger.error(
            f"Error deleting embeddings for file {file_id} in organisation {organisation_id}: {e}"
        )
        raise HTTPException(
            status_code=500, detail="Failed to delete embeddings for the file."
        )


async def query_entire_chromadb_collection(organisation_id: str):
    """
    Retrieve all documents and metadata from an organization's ChromaDB collection.

    Parameters:
    - organisation_id (str): The ID of the organization.

    Returns:
    - All documents and metadata.
    """
    try:
        collection = get_organisation_collection(organisation_id)
        logger.info(f"collection: {collection}")
        return collection.get(include=["documents", "metadatas"])

    except Exception as e:
        logger.error(
            f"Error querying ChromaDB collection for organisation {organisation_id}: {e}"
        )
        raise HTTPException(
            status_code=500, detail="Failed to retrieve collection data."
        )


async def save_file_data(
    file, user_id, organisation_id, summary, username, session: AsyncSession
):
    try:
        logger.info("Processing storage of file metadata to the database")
        file_data = FileUpload(
            filename=file.filename,
            filesize=file.size,
            filetype=file.content_type,
            user_id=user_id,
            username=username,  # change to upload_by
            organisation_id=organisation_id,
            summary=summary,  # | "Auto-generated file summary"
        )
        session.add(file_data)
        await session.commit()
        logger.info(
            f"Procedure done: File {file_data.filename} metadata saved to the database"
        )
        return file_data
    except Exception as e:
        await session.rollback()
        logger.error(
            f"An unexpected error occured while trying to save file: {file_data.filename}; error: {str(e)}"
        )
        raise HTTPException(
            status_code=500,
            detail=f"An unexpected error occurred while trying to save file: {file_data.filename}",
        )


async def process_large_file(file_content, file_metadata):
    logger.info("START: Processing of file conversion...")
    try:
        # Reuse the globally initialized persistent client
        logger.info("Starting process_large_file function")
        organisation_id = file_metadata.get("organisation_id")
        collection = get_organisation_collection(organisation_id)

        # Extract text from the file content
        logger.info("Extracting text from file content")
        file_text = await extract_text_from_file(file_content, file_metadata)
        logger.info("file_text extracted")
        # Generate a summary of the extracted text
        logger.info("Generating summary of the extracted text")
        summary = await generate_summary(file_text)
        logger.info("Splitting text into chunks")
        text_splitter = RecursiveCharacterTextSplitter(chunk_size=500, chunk_overlap=50)
        logger.debug(f"The text_splitter: {text_splitter}")
        chunks = text_splitter.split_text(file_text)

        logger.info("Text splitted into chunks")
        logger.info("Initializing OpenAI embeddings")
        # Initialize OpenAI embeddings
        embeddings = OpenAIEmbeddings(model="text-embedding-3-large")

        # Embed all chunks
        logger.info("Embedding chunks")
        chunk_embeddings = embeddings.embed_documents(chunks)

        logger.debug(f"The chunk_embeddings: {chunk_embeddings}")
        logger.info("Adding chunks to the collections")
        for i, (chunk, chunk_embedding) in enumerate(zip(chunks, chunk_embeddings)):
            # logger.info(f"added: {i}")
            collection.add(
                documents=[chunk],
                embeddings=[chunk_embedding],
                metadatas=[{"file_summary": summary, "chunk_index": i}],
                ids=[f"{file_metadata['filename']}_{i}"],
            )

        logger.info(
            f"Processed and saved embeddings for file: {file_metadata['filename']}"
        )
        return summary
    except Exception as e:
        logger.error(
            f"Error processing file {file_metadata['filename']}: {e}", exc_info=True
        )
        raise e


async def save_chat_history(
    user_id: str, message: str, response: str, session: AsyncSession
):
    chat = ChatHistory(user_id=user_id, chat_content=f"Q: {message}\nA: {response}")
    session.add(chat)
    await session.commit()
