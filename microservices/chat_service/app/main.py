import logging

from app.core.config import settings
from app.database import create_db_and_tables
from app.routes import chat, file
from app.utils.minio import minio_client
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.utils import get_openapi

# Set up logging configuration
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

app = FastAPI(openapi_url="/api/v1/chat/openapi.json", docs_url="/api/v1/chat/docs")

# Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Custom OpenAPI schema to include bearer token
def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema
    openapi_schema = get_openapi(
        title="KIMEV",
        version="1.0.0",
        description="Do cool AI Stuffs",
        routes=app.routes,
    )
    openapi_schema["components"]["securitySchemes"] = {
        "Bearer": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}
    }
    for path in openapi_schema["paths"].values():
        for method in path.values():
            if "security" in method:
                method["security"].append({"Bearer": []})
            else:
                method["security"] = [{"Bearer": []}]
    app.openapi_schema = openapi_schema
    return app.openapi_schema


app.openapi = custom_openapi

app.include_router(chat.router, prefix="/api/v1/chat")
app.include_router(file.router, prefix="/api/v1/files")


@app.on_event("startup")
async def on_startup():
    # Create database tables
    logger.info("Creating database tables...")
    try:
        await create_db_and_tables()
        logger.info("Database tables created successfully.")
        logger.info("creating minIO bucket.")
        await create_buckets()

    except Exception as e:
        logger.error(f"Error creating database tables: {e}")


@app.get("/chat_status")
async def status():
    logger.info("Status endpoint accessed.")
    return {"message": "Welcome to the Chatbot API"}


async def create_buckets():
    buckets = [settings.MINIO_BUCKET]

    for bucket in buckets:
        try:
            if not minio_client.bucket_exists(bucket):
                logger.info(f"creating bucket {bucket}")
                minio_client.make_bucket(bucket)
                logger.info(f"bucket {bucket} created successfully")
        except Exception as e:
            logger.error(f"Error creating bucket {bucket}: {e}")


# for route in app.routes:
#         print(f"Path: {route.path}, Name: {route.name}, Methods: {route.methods}")

if __name__ == "__main__":
    import multiprocessing

    import uvicorn

    def start_uvicorn():
        logger.info("Starting uvicorn process")
        uvicorn.run(app, host="0.0.0.0", port=8000)

    def start_celery_worker():
        logger.info("Starting celery process")
        from app.worker import celery_app

        celery_app.worker_main(["worker", "--loglevel=info"])
        logger.info("Celery process started")

    # Start both FastAPI and Celery worker
    processes = [
        multiprocessing.Process(target=start_uvicorn),
        multiprocessing.Process(target=start_celery_worker),
    ]

    for process in processes:
        process.start()

    for process in processes:
        process.join()
