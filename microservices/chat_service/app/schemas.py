from datetime import datetime
from typing import List

from pydantic import BaseModel


# create an Enum of the platforms
class ChatThreadSummary(BaseModel):
    thread_id: str
    title: str


class UpdateThreadTitleRequest(BaseModel):
    new_title: str


class UpdateThreadTitleResponse(BaseModel):
    message: str
    thread: dict


class DeleteThreadResponse(BaseModel):
    message: str


class ChatRequest(BaseModel):
    user_message: str
    thread_id: str


class ChatResponse(BaseModel):
    response: str
    response_type: str


class ChatMessage(BaseModel):
    user_message: str
    bot_response: str
    timestamp: datetime


class NewChatMessage(BaseModel):
    user_message: str


class NewChatResponse(BaseModel):
    bot_response: str
    thread_id: str
    thread_title: str
    response_type: str


class ChatThreadResponse(BaseModel):
    thread_title: str
    thread_id: str
    messages: List[ChatMessage]


class KnowledgeBaseRequest(BaseModel):
    business_name: str
    industry: str
    business_size: str
    business_description: str
    target_audience: str
    competitor_names: list[str]
    competitor_website: list[str] = []
    brand_voice: str
    business_tone: str


class FileResponse(BaseModel):
    id: str
    filename: str
    filesize: int
    filetype: str
    user_id: str
    username: str | None
    organisation_id: str
    summary: str
    upload_date: datetime
    timestamp: int


class FileListResponse(BaseModel):
    files: List[FileResponse]


class AddToKnowledgeBaseResponse(BaseModel):
    success: bool
    message: str


class DeleteFileResponse(BaseModel):
    success: bool
    message: str


class SocialMediaContentRequest(BaseModel):
    platform: str
    additional_info: str = None


class SaveSocialMediaContentRequest(BaseModel):
    platform: str
    additional_info: str = None
    content_text: str


class ScrapeURLsRequest(BaseModel):
    urls: List[str]


# Response model
class ScrapeURLsResponse(BaseModel):
    success: bool
    message: str
