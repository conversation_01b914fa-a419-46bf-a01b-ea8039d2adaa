import asyncio
import logging
from io import Bytes<PERSON>
from typing import Annotated, List

from app.database import get_session
from app.models import FileUpload
from app.schemas import (AddToKnowledgeBaseResponse, DeleteFileResponse,
                         FileListResponse, FileResponse, KnowledgeBaseRequest,
                         ScrapeURLsRequest, ScrapeURLsResponse)
from app.services.chromadb_service import (delete_file_embeddings,
                                           save_file_data, store_in_chromadb,
                                           store_urls_data)
from app.services.openai_service import find_similar_competitors
from app.services.scrapy_service import run_scraper, run_urls_scraper
from app.utils.dependency import get_current_user
from app.utils.external_calls import (fetch_user_details,
                                      send_notification_request,
                                      verify_organization)
from app.utils.minio import get_from_minio, upload_to_minio
from app.worker import process_file_task
from fastapi import (APIRouter, BackgroundTasks, Depends, File, HTTPException,
                     UploadFile)
from fastapi.responses import StreamingResponse
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

logger = logging.getLogger(__name__)
router = APIRouter()


# TODO: Call this endpoint from onboarding....
@router.post(
    "/knowledgebase/add",
    tags=["Knowledge Base"],
    response_model=AddToKnowledgeBaseResponse,
)
async def add_to_knowledge_base(
    token: Annotated[str, Depends(get_current_user)],
    request: KnowledgeBaseRequest,
    organisation_id: Annotated[str, Depends(verify_organization)],
    background_tasks: BackgroundTasks,
):
    token["decoded"]
    auth_token = token["raw_token"]
    # Validate input
    if not request.business_name or not isinstance(request.competitor_names, list):
        raise HTTPException(status_code=400, detail="Invalid input data.")

    try:
        # Find competitor links asynchronously
        competitor_links = find_similar_competitors(
            request.business_description, request.competitor_names
        )
        logger.info(f"Competitor links found: {competitor_links}")
        all_competitor_links = list(set(request.competitor_website + competitor_links))

        # Prepare the combined data to be stored immediately
        combined_data = {
            "business_name": request.business_name,
            "industry": request.industry,
            "business_size": request.business_size,
            "business_description": request.business_description,
            "target_audience": request.target_audience,
            "competitor_names": request.competitor_names,
            "competitor_website": all_competitor_links,
            "brand_voice": request.brand_voice,
            "business_tone": request.business_tone,
            "organisation_id": organisation_id,
        }

        # Store the business info without the scraped data immediately
        await store_in_chromadb(combined_data)
        logger.info(f"Business data for {request.business_name} stored successfully.")

        # Scrape competitors' sites in the background and store the scraped data in ChromaDB
        background_tasks.add_task(
            scrape_and_store_competitor_data, all_competitor_links, combined_data
        )

        try:
            response = await send_notification_request(
                organisation_id,
                f"New business data added to knowledge base: {request.business_name}",
                auth_token,
            )
            logger.info(f"Notification sent successfully: {response}")
        except Exception as e:
            logger.error(f"Error sending notification: {str(e)}")

        return {
            "success": True,
            "message": "Data added to knowledge base. Competitor scraping is in progress.",
        }
    except ValueError as ve:
        logger.error(f"ValueError in add_to_knowledge_base: {str(ve)}")
        raise HTTPException(status_code=400, detail="Invalid data format.")
    except HTTPException as he:
        logger.error(f"HTTPException in add_to_knowledge_base: {str(he)}")
        raise he
    except Exception as e:
        logger.error(f"Unexpected error in add_to_knowledge_base: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while adding to the knowledge base.",
        )


# Background function that scrapes competitor data and stores it in ChromaDB
async def scrape_and_store_competitor_data(competitor_links, combined_data):
    logger.info("Starting scrape_and_store_competitor_data task.")
    max_retries = 3
    attempt = 0
    success = False

    while attempt < max_retries and not success:
        try:
            logger.info(
                f"Attempt {attempt + 1} of {max_retries}: Starting scraping task."
            )
            scraped_data = await run_scraper(competitor_links)
            logger.info("Scraping task completed successfully.")
            # Update the combined data with the scraped info
            combined_data["scraped_data"] = str(scraped_data)

            # Store the updated data in ChromaDB
            await store_in_chromadb(combined_data)
            logger.info("Scraped data successfully stored in ChromaDB.")
            success = True
        except Exception as e:
            attempt += 1
            logger.error(
                f"Error during scraping and storing data: {e}. Retrying {attempt}/{max_retries}..."
            )
            if attempt == max_retries:
                logger.error("Max retries reached. Scraping operation failed.")


@router.post("/upload", tags=["Knowledge Base"], response_model=DeleteFileResponse)
async def upload_file(
    token: Annotated[str, Depends(get_current_user)],
    organisation_id: Annotated[str, Depends(verify_organization)],
    session: AsyncSession = Depends(get_session),
    file: UploadFile = File(...),
):
    token_data = token["decoded"]
    auth_token = token["raw_token"]

    user_id = token_data.get("user_id")
    # Fetch user details to get the username
    user_details = await fetch_user_details(user_id)
    username = f"{user_details.get('first_name', 'Unknown')} {user_details.get('last_name', '')}"
    logger.info(f"User {username} ({user_id}) is uploading a file: {file.filename}")
    try:
        # saving file details to db initially, before converting to vector
        logger.info("Saving file metadata to database")
        response = await save_file_data(
            file=file,
            user_id=user_id,
            organisation_id=organisation_id,
            summary="",
            username=username,
            session=session,
        )  # Pass an empty string for summary initially
        file_id = response.id
        logger.info(f"File metadata saved for {file.filename}")
    except Exception as e:
        logger.error(f"Error saving file metadata: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error saving file data: {str(e)}")
    # Process the file in the background
    logger.info("Starting background task for file processing...")
    try:
        # Extract file content
        file_content = BytesIO(await file.read())
        file_metadata = {
            "filename": file.filename,
            "content_type": file.content_type,
            "size": file_content.getbuffer().nbytes,
            "id": file_id,
            "organisation_id": organisation_id,
        }
        logger.info(f"the file metadata: {file_metadata}")

        if file_content.getbuffer().nbytes == 0:
            raise HTTPException(
                status_code=400,
                detail=f"The uploaded file {file_metadata['filename']} is empty.",
            )
        process_file_task.delay(file_content.getvalue(), file_metadata)

        # upload to minIO
        logger.info("Uploading to minIO")
        upload_to_minio(file_content, file_metadata)

        logger.info("File processing task dispatched...")
    except HTTPException as e:
        raise e
    except asyncio.CancelledError:
        logger.error("File processing was cancelled.")
        await session.rollback()
        raise HTTPException(status_code=500, detail="File processing was cancelled.")
    except Exception as e:
        logger.error(f"An unexpected error occured: {str(e)}.")
        await session.rollback()
        raise HTTPException(
            status_code=500, detail=f"Unexpected error processing file: {str(e)}"
        )
    logger.info(f"Auth token before sending request: {auth_token}")
    try:
        response = await send_notification_request(
            organisation_id,
            f"New file uploaded to knowledge base: {file.filename}",
            auth_token,
        )
        logger.info(f"Notification sent successfully: {response}")
    except Exception as e:
        logger.error(f"Error sending notification: {str(e)}")

    return {"success": True, "message": "File upload successfull"}
    # TODO: Update to ensure, file is deleted, if conversion to embeddings fail
    # finally:
    #     # Clean up temporary file
    #     logger.info('END: Cleaning up...')
    #     try:
    #         # os.remove(temp_file_path)
    #         # logger.info(f"Temporary file {temp_file_path} removed.")

    #     except Exception as e:
    #         # logger.error(f"Error le {temp_file_path}: {str(e)}")
    #         raise HTTPException(
    #             status_code=500,
    #             detail=f"Error saving file to temp location: {str(e)}")


@router.get("/{file_id}", tags=["Knowledge Base"], response_model=FileResponse)
async def get_file(
    file_id: str,
    organisation_id: Annotated[str, Depends(verify_organization)],
    _: Annotated[str, Depends(get_current_user)],
    session: AsyncSession = Depends(get_session),
):
    try:
        file_data = await session.execute(
            select(FileUpload).where(
                FileUpload.id == file_id, FileUpload.organisation_id == organisation_id
            )
        )
        file_data = file_data.scalars().first()
        if not file_data:
            raise HTTPException(status_code=404, detail="File not found")
        logger.info("Retrieving from minIO...")
        file_content, filename = get_from_minio(file_data)
        logger.info(f"File retrieved: {filename}")
        # return file_data
        return StreamingResponse(
            BytesIO(file_content),
            media_type=f"{file_data.filetype}",  # need to dynamically render the media_type
            headers={"Content-Disposition": f"attachment; filename={filename}"},
        )
    except HTTPException as e:
        logger.error(f"Error retrieving file: {str(e)}")
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving file: {str(e)}")


@router.get("/", tags=["Knowledge Base"], response_model=FileListResponse)
async def list_all_files(
    organisation_id: Annotated[str, Depends(verify_organization)],
    token: Annotated[str, Depends(get_current_user)],
    session: AsyncSession = Depends(get_session),
):
    # Retrieve all files with their metadata
    logger.info("Listing all files")

    file_datas = await session.execute(
        select(FileUpload)
        .where(FileUpload.organisation_id == organisation_id)
        .order_by(FileUpload.upload_date.desc())
    )
    files = file_datas.scalars().all()

    logger.info(f"Total files found: {len(files)}")
    return {"files": files}


@router.delete("/{file_id}", tags=["Knowledge Base"], response_model=DeleteFileResponse)
async def delete_file(
    file_id: str,
    organisation_id: Annotated[str, Depends(verify_organization)],
    token: Annotated[str, Depends(get_current_user)],
    session: AsyncSession = Depends(get_session),
):
    try:
        # Retrieve the file data from the database
        logger.info(f"Deleting file with ID: {file_id}")
        file_data = await session.execute(
            select(FileUpload).where(
                FileUpload.id == file_id, FileUpload.organisation_id == organisation_id
            )
        )
        file_data = file_data.scalars().first()

        if not file_data:
            raise HTTPException(status_code=404, detail="File not found")

        # Delete the embeddings from ChromaDB
        await delete_file_embeddings(file_data.id, organisation_id)

        # Delete the file from the database
        await session.delete(file_data)
        await session.commit()

        logger.info(f"File with ID {file_id} deleted successfully")

        try:
            response = await send_notification_request(
                organisation_id,
                f"File deleted from knowledge base: {file_data.filename}",
                token["raw_token"],
            )
            logger.info(f"Notification sent successfully: {response}")
        except Exception as e:
            logger.error(f"Error sending notification: {str(e)}")
        return {
            "success": True,
            "message": f"File with ID {file_id} deleted successfully",
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deleting file: {str(e)}")


@router.post(
    "/knowledgebase/scrape", tags=["Knowledge Base"], response_model=ScrapeURLsResponse
)
async def scrape_and_add_to_knowledge_base(
    request: ScrapeURLsRequest,
    background_tasks: BackgroundTasks,
    organisation_id: str = Depends(verify_organization),
):
    urls = request.urls
    if not urls:
        raise HTTPException(status_code=400, detail="URLs list cannot be empty.")
    if not organisation_id:
        raise HTTPException(status_code=400, detail="Organisation ID is required.")

    # Add the scraping and storing task to the background
    background_tasks.add_task(scrape_and_store, urls, organisation_id)

    return {
        "success": True,
        "message": "Scraping and storing process started in the background.",
    }


async def scrape_and_store(urls: List[str], organisation_id: str):
    """
    Function to scrape data from URLs and store it in ChromaDB.

    Args:
        urls (List[str]): List of URLs to scrape.
        organisation_id (str): Organization ID for storing data.
    """
    try:
        # Run the scraper
        scraped_data = await run_urls_scraper(urls)

        # Store the scraped data in ChromaDB
        for data in scraped_data:
            await store_urls_data(data, organisation_id)
            logger.info(f"Successfully stored scraped data for URL {data['url']}    ")
        print(
            f"Successfully completed scraping and storing for organisation {organisation_id}."
        )

    except Exception as e:
        print(f"Error in scraping and storing process: {e}")
