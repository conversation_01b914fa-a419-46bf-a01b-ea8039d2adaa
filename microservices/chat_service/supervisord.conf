[supervisord]
nodaemon=true

[program:chat_service]
command=uvicorn app.main:app --host 0.0.0.0 --port 8000
autostart=true
autorestart=true
stderr_logfile=/var/log/fastapi.err.log
stdout_logfile=/var/log/fastapi.out.log
user=root

[program:celery]
command=celery -A app.worker worker --loglevel=info
autostart=true
autorestart=true
stderr_logfile=/var/log/celery.err.log
stdout_logfile=/var/log/celery.out.log
user=root


[program:flower]
command=celery -A app.worker flower --port=5555
autostart=true
autorestart=true
stderr_logfile=/var/log/flower.err.log
stdout_logfile=/var/log/flower.out.log
user=root
