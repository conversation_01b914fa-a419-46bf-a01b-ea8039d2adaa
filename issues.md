1. A separate endpoint to help in knowledge base to scrape url. then edit the upload of html file to scrape the file and not search url.
2. Rework the embeddings so that the files can be saved. this will enable download, update/edit and delete. and as such we cap the storage for such a case.

SCHEDULE CONTENT

1. Platform can be a list of the platforms to post on
2. Content is a Text of the content derived from prompt
3. post-time is a datetime object for when the content is to be posted.
4. the user that's creating the content
5. the approval
6. for approval, we can use a websockets, django channels kind of implementation to send notifications to users.
7. When a user creates content and needs an approval, frontent would display the names of users that can approve content i.e users that have the approve content or post content permissions.
8. when a certain user is selected, a notification is passed to the user about the content and then they can update the content's status to approved to allow the content to be posted at the specified date. check needs to be put in place to ensure the date of approval is not beyond the date the content is to be posted.
9. approvals  would be a list of users with the permissions.
10. Dont know how to handle when they are asked for approval yet though.

### A question though. since we have different platforms that the content can be posted on. do each platform have the same time or different time.

I mean, when a user creates a content and selects more than one platform, would each of the platform have different time for posting or just that one time set would be applied to all platforms

11. There should be a way to catch errors if the content is not posted. also we should save the content id and a little description, so we can keep track of it's analytics
12. status can either be draft, published, scheduled. we can use enum for it.

############ LIST OF PERMISSIONS ############

In scheduling content

1. can schedule content
2. can delete content
3. can create content
4. can create and post content
5. can delete socials
6. delete organisation

####################################
NOT YET IMPLEMENTED
a check for onboarding to feed the chromadb with the loggedin user organisation details



# insights

ig_reels_video_view_total_time

ig_reels_avg_watch_time

available if media_type is REELS


navigation,replies if media_type is STORY
