#!/usr/bin/env python3
"""
Test script to verify that the critical Facebook metrics fixes work correctly.
This script tests:
1. Session.merge() replacement with INSERT...ON CONFLICT
2. UnboundLocalError fixes in growth trend functions
3. Transaction management improvements
4. AsyncPG concurrency issue resolution
"""

import asyncio
import sys
import os
from datetime import datetime

# Add the microservices path to sys.path
sys.path.append(os.path.join(os.path.dirname(__file__), 'microservices', 'socials_service'))

from app.database.session import SessionLocal
from app.models import model
from app.services.facebook import save_comments_to_db, get_conversations_from_api
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload
from sqlalchemy import func
from sqlalchemy.dialects.postgresql import insert


async def test_conversation_upsert_logic():
    """Test the conversation INSERT...ON CONFLICT logic"""
    print("Testing conversation upsert logic...")
    
    async with SessionLocal() as db:
        try:
            # Test data
            test_account_id = "test_account_123"
            test_convo_id = "test_convo_123"
            
            # Clean up any existing test data
            await db.execute(
                select(model.Conversation).where(
                    model.Conversation.convo_id == test_convo_id
                ).delete()
            )
            await db.commit()
            
            # Test 1: Insert new conversation using the new upsert logic
            stmt = insert(model.Conversation).values(
                social_media_account_id=test_account_id,
                convo_id=test_convo_id,
                updated_time=datetime.now(),
                participants=[]
            ).on_conflict_do_update(
                index_elements=['convo_id'],
                set_={
                    'social_media_account_id': test_account_id,
                    'updated_time': datetime.now(),
                    'participants': []
                }
            )
            await db.execute(stmt)
            await db.commit()
            print("✓ Successfully inserted new conversation using upsert logic")
            
            # Test 2: Update existing conversation (should not cause constraint violation)
            stmt = insert(model.Conversation).values(
                social_media_account_id=test_account_id,
                convo_id=test_convo_id,
                updated_time=datetime.now(),
                participants=[{"name": "test_user"}]
            ).on_conflict_do_update(
                index_elements=['convo_id'],
                set_={
                    'social_media_account_id': test_account_id,
                    'updated_time': datetime.now(),
                    'participants': [{"name": "test_user"}]
                }
            )
            await db.execute(stmt)
            await db.commit()
            print("✓ Successfully updated existing conversation using upsert logic")
            
            # Clean up test data
            await db.execute(
                select(model.Conversation).where(
                    model.Conversation.convo_id == test_convo_id
                ).delete()
            )
            await db.commit()
            
            print("✓ Conversation upsert logic test passed!")
            return True
            
        except Exception as e:
            print(f"✗ Conversation upsert test error: {str(e)}")
            return False


async def test_growth_trend_variable_initialization():
    """Test that growth trend functions don't have UnboundLocalError"""
    print("\nTesting growth trend variable initialization...")
    
    async with SessionLocal() as db:
        try:
            # Test data
            test_org_id = "test_org_456"
            test_page_id = "test_page_456"
            test_month = "2024-02"
            
            # Clean up any existing test data
            await db.execute(
                select(model.FacebookGrowthTrend).where(
                    model.FacebookGrowthTrend.organisation_id == test_org_id
                ).delete()
            )
            await db.commit()
            
            # Test 1: Create initial record (new path)
            existing_record = await db.execute(
                select(model.FacebookGrowthTrend).where(
                    model.FacebookGrowthTrend.organisation_id == test_org_id,
                    model.FacebookGrowthTrend.trend_type == "audience",
                    model.FacebookGrowthTrend.month == test_month
                )
            )
            existing = existing_record.scalars().first()
            
            if existing:
                # Update existing record path
                existing.page_id = test_page_id
                existing.value = 200
                existing.growth_percentage = 20.0
                existing.collected_at = func.now()
                record_to_cache = existing
                print("✓ Successfully tested existing record update path")
            else:
                # Create new record path
                growth = model.FacebookGrowthTrend(
                    organisation_id=test_org_id,
                    page_id=test_page_id,
                    trend_type="audience",
                    month=test_month,
                    value=200,
                    growth_percentage=20.0
                )
                db.add(growth)
                record_to_cache = growth
                print("✓ Successfully tested new record creation path")
            
            # Test that we can access the growth_percentage without UnboundLocalError
            growth_percentage = record_to_cache.growth_percentage
            print(f"✓ Successfully accessed growth_percentage: {growth_percentage}")
            
            await db.commit()
            
            # Test 2: Now test the update path by running the same logic again
            existing_record = await db.execute(
                select(model.FacebookGrowthTrend).where(
                    model.FacebookGrowthTrend.organisation_id == test_org_id,
                    model.FacebookGrowthTrend.trend_type == "audience",
                    model.FacebookGrowthTrend.month == test_month
                )
            )
            existing = existing_record.scalars().first()
            
            if existing:
                # Update existing record path
                existing.page_id = test_page_id
                existing.value = 250
                existing.growth_percentage = 25.0
                existing.collected_at = func.now()
                record_to_cache = existing
                print("✓ Successfully tested existing record update path (second time)")
            
            # Test that we can access the growth_percentage without UnboundLocalError
            growth_percentage = record_to_cache.growth_percentage
            print(f"✓ Successfully accessed growth_percentage: {growth_percentage}")
            
            # Clean up test data
            await db.execute(
                select(model.FacebookGrowthTrend).where(
                    model.FacebookGrowthTrend.organisation_id == test_org_id
                ).delete()
            )
            await db.commit()
            
            print("✓ Growth trend variable initialization test passed!")
            return True
            
        except Exception as e:
            print(f"✗ Growth trend variable test error: {str(e)}")
            return False


async def test_transaction_management():
    """Test that transaction management works without nested commits"""
    print("\nTesting transaction management...")
    
    async with SessionLocal() as db:
        try:
            # Test that we can perform multiple operations without nested commits
            test_org_id = "test_org_789"
            
            # Clean up any existing test data
            await db.execute(
                select(model.FacebookGrowthTrend).where(
                    model.FacebookGrowthTrend.organisation_id == test_org_id
                ).delete()
            )
            
            # Perform multiple operations that would previously cause nested commits
            for i in range(3):
                growth = model.FacebookGrowthTrend(
                    organisation_id=test_org_id,
                    page_id=f"test_page_{i}",
                    trend_type="audience",
                    month=f"2024-0{i+1}",
                    value=100 + i * 10,
                    growth_percentage=10.0 + i
                )
                db.add(growth)
            
            # Single commit at the end (simulating main function behavior)
            await db.commit()
            print("✓ Successfully performed multiple operations with single commit")
            
            # Verify all records were created
            result = await db.execute(
                select(model.FacebookGrowthTrend).where(
                    model.FacebookGrowthTrend.organisation_id == test_org_id
                )
            )
            records = result.scalars().all()
            
            if len(records) == 3:
                print("✓ All records were successfully committed")
            else:
                print(f"✗ Expected 3 records, found {len(records)}")
                return False
            
            # Clean up test data
            await db.execute(
                select(model.FacebookGrowthTrend).where(
                    model.FacebookGrowthTrend.organisation_id == test_org_id
                ).delete()
            )
            await db.commit()
            
            print("✓ Transaction management test passed!")
            return True
            
        except Exception as e:
            print(f"✗ Transaction management test error: {str(e)}")
            return False


async def main():
    """Run all critical fix tests"""
    print("=" * 70)
    print("Facebook Metrics Critical Fixes Verification")
    print("=" * 70)
    
    # Test 1: Conversation upsert logic
    test1_passed = await test_conversation_upsert_logic()
    
    # Test 2: Growth trend variable initialization
    test2_passed = await test_growth_trend_variable_initialization()
    
    # Test 3: Transaction management
    test3_passed = await test_transaction_management()
    
    # Summary
    print("\n" + "=" * 70)
    print("CRITICAL FIXES TEST SUMMARY")
    print("=" * 70)
    print(f"Conversation upsert logic test: {'PASSED' if test1_passed else 'FAILED'}")
    print(f"Growth trend variable test: {'PASSED' if test2_passed else 'FAILED'}")
    print(f"Transaction management test: {'PASSED' if test3_passed else 'FAILED'}")
    
    if test1_passed and test2_passed and test3_passed:
        print("\n✓ All critical fixes tests PASSED! The system should work correctly.")
        return 0
    else:
        print("\n✗ Some critical fixes tests FAILED! There may still be issues.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
