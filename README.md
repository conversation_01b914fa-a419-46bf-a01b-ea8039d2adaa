# EllumAI Backend

EllumAI is a microservices-based backend system designed to provide AI-powered functionality through a set of specialized services. The system is built using FastAPI and follows a microservices architecture for scalability and maintainability.

## Project Overview

The EllumAI backend consists of the following microservices:

1. **Authentication Service**: Handles user authentication, registration, and JWT token management.
2. **Chat Service**: Manages chat interactions, conversations, and file handling for AI interactions.
3. **FastBot Service**: Provides file chat and knowledgebase functionality with AI integration.
4. **Settings and Payment Service**: Manages user settings, subscription plans, and payment processing.
5. **Socials Service**: Handles social media integration, scheduling, and metrics tracking.

## Architecture

The system uses:
- **FastAPI**: For building high-performance REST APIs
- **Redis**: For caching and message queuing
- **Qdrant**: Vector database for AI-related functionality
- **Docker**: For containerization and deployment
- **PostgreSQL**: For persistent data storage (implied from the code)

## Getting Started

### Prerequisites

- Docker and Docker Compose
- Python 3.12+
- Git

### Setup and Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd ellumAI_backend
   ```

2. Set up environment variables:
   - Each service has its own `.env` file in its respective directory
   - Ensure all required environment variables are set

3. Start the services using Docker Compose:
   ```bash
   docker-compose up --build
   ```

This will start all the microservices and their dependencies.

## Service Endpoints

Each service exposes its own API documentation:

- Authentication Service: http://localhost:7777/api/v1/auth/docs
- Chat Service: http://localhost:8000/api/v1/chat/docs
- FastBot Service: http://localhost:8001/api/v1/chat/docs
- Settings and Payment Service: http://localhost:8005/api/v1/settings/docs
- Socials Service: http://localhost:8006/api/v1/socials/docs

## Development

### Running Services Individually

Each service can be run individually for development purposes. Navigate to the service directory and follow the instructions in the service's README.

### Project Structure

```
ellumAI_backend/
├── docker-compose.yml
├── microservices/
│   ├── authentication_service/
│   ├── chat_service/
│   ├── fastbot/
│   ├── settings_and_payment/
│   └── socials_service/
└── logs/
```

## Contributing

1. Create a feature branch
2. Make your changes
3. Submit a pull request

## License
