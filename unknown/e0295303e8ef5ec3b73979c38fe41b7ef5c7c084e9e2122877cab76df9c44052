from pydantic import BaseModel
from typing import Dict, Optional
from datetime import datetime, time

class GeneralSettingsBase(BaseModel):
    organization_id: str
    full_name: str
    email_address: str
    phone_number: Optional[str]
    user_role: str
    brand_voice: str
    img_url: Optional[str]


class GeneralSettingsUpdate(BaseModel):
    full_name: Optional[str] = None
    phone_number: Optional[str] = None
    user_role: Optional[str] = None
    brand_voice: Optional[str] = None
    img_url: Optional[str] = None

    class Config:
        from_attributes = True

class GeneralSettingsCreate(GeneralSettingsBase):
    pass

class GeneralSettingsPublic(GeneralSettingsBase):
    id: str

    class Config:
        from_attributes = True

class ApplicationSettingsBase(BaseModel):
    organization_id: str
    default_language: str
    content_auto_save_interval: str
    in_app_notifications: bool
    email_notifications: bool

class ApplicationSettingsCreate(BaseModel):
    default_language: str
    content_auto_save_interval: str
    in_app_notifications: bool
    email_notifications: bool

class ApplicationSettingsPublic(ApplicationSettingsBase):
    id: str

    class Config:
        from_attributes = True

class SocialSettingsBase(BaseModel):
    organization_id: str
    social_media: Optional[Dict[str, str]] = {}

class SocialSettingsCreate(SocialSettingsBase):
    pass

class SocialSettingsPublic(SocialSettingsBase):
    id: str

    class Config:
        from_attributes = True

class LocalizationSettingsBase(BaseModel):
    organization_id: str
    language: str
    date_format: str
    time_format: str
    number_format: str
    country_region: str
    time_zone: str
    show_region_specific_content: bool
    block_content_based_on_locaction: bool

class LocalizationSettingsCreate(BaseModel):
    language: str
    date_format: str
    time_format: str
    number_format: str
    country_region: str
    time_zone: str
    show_region_specific_content: bool
    block_content_based_on_locaction: bool

class LocalizationSettings(LocalizationSettingsBase):
    id: str

    class Config:
        from_attributes = True


class DataStorageSettingsBase(BaseModel):
    organization_id: str
    storage_limit: Optional[float] = 1000  # in GB
    storage_used: Optional[float] = 0      # in GB
    automatic_backup: Optional[bool] = False
    backup_frequency: Optional[str] = "Weekly"  # Daily, Weekly, Monthly
    backup_location: Optional[str] = "Cloud"   # Cloud or Local Storage
    export_format: Optional[str] = "PDF"       # Export file format
    data_retention_period: Optional[str] = "1 year"  # Retention period
    deletion_warning: Optional[bool] = False

class DataStorageSettingsCreate(DataStorageSettingsBase):
    pass

class DataStorageSettingsUpdate(BaseModel):
    automatic_backup: Optional[bool] = False
    backup_frequency: Optional[str] = "Weekly"  # Daily, Weekly, Monthly
    backup_location: Optional[str] = "Cloud"   # Cloud or Local Storage
    export_format: Optional[str] = "PDF"       # Export file format
    data_retention_period: Optional[str] = "1 year"  # Retention period
    deletion_warning: Optional[bool] = False

class DataStorageSettings(DataStorageSettingsBase):
    id: str

    class Config:
        from_attributes = True

class NotificationSettingsBase(BaseModel):
    organization_id: str
    comments: Optional[bool] = True
    new_message_alerts: Optional[bool] = True
    account_activity: Optional[bool] = True
    system_updates: Optional[bool] = True
    marketing_updates: Optional[bool] = False

    in_app_notifications: Optional[bool] = True
    email_notifications: Optional[bool] = True
    push_notifications: Optional[bool] = True

    notification_frequency: Optional[str] = "Real-time"
    do_not_disturb_start: Optional[time] = None
    do_not_disturb_end: Optional[time] = None

class NotificationSettingsCreate(NotificationSettingsBase):
    pass

class NotificationSettingsUpdate(BaseModel):
    comments: Optional[bool] = True
    new_message_alerts: Optional[bool] = True
    account_activity: Optional[bool] = True
    system_updates: Optional[bool] = True
    marketing_updates: Optional[bool] = False

    in_app_notifications: Optional[bool] = True
    email_notifications: Optional[bool] = True
    push_notifications: Optional[bool] = True

    notification_frequency: Optional[str] = "Real-time"
    do_not_disturb_start: Optional[time] = None
    do_not_disturb_end: Optional[time] = None

class NotificationSettings(NotificationSettingsBase):
    id: str

    class Config:
        from_attributes = True

class SessionActivityBase(BaseModel):
    organization_id: str
    session_id: str
    username: str
    device: str
    location: str

class SessionActivityCreate(SessionActivityBase):
    pass

class SessionActivity(SessionActivityBase):
    id: str
    start_time: datetime
    end_time: Optional[datetime] = None

    class Config:
        from_attributes = True

class UsageMetricsBase(BaseModel):
    organization_id: str
    total_logins: int
    active_users_today: int
    total_sessions: int
    average_session_duration: float
    peak_usage_start: Optional[str] = None
    peak_usage_end: Optional[str] = None

class UsageMetricsCreate(UsageMetricsBase):
    pass

class UsageMetrics(UsageMetricsBase):
    id: str

    class Config:
        from_attributes = True
