from app.utils.logger import get_logger
from pydantic_settings import BaseSettings

logger = get_logger(__name__)


class Settings(BaseSettings):
    """Class to hold application's config values."""

    SECRET_KEY: str
    ALGORITHM: str
    ACCESS_TOKEN_EXPIRES_MINUTES: int
    JWT_REFRESH_TOKEN_EXPIRES: int
    SECURITY_SALT: str
    AUTH_SERVICE_URL: str
    # Database configurations
    PROD_DB_URI: str
    LOCAL_DB_URI: str
    TESTING_DB_URI: str
    # Email configurations
    MAIL_USERNAME: str
    MAIL_PASSWORD: str
    MAIL_FROM: str
    MAIL_PORT: int
    MAIL_SERVER: str
    # PAYSTACK configurations
    PAYSTACK_SECRET: str
    PAYSTACK_BASE_URL: str
    PAYSTACK_CALLBACK_API_URL: str

    # STRIPE
    STRIPE_SECRET_KEY: str
    STRIPE_WEBHOOK_SECRET: str
    # others
    ENV: str
    REDIRECT_URL: str
    PUSH_PROJECT_ID: str
    PUSH_KEY_FILE_PATH: str
    REDIS_URL: str
    REDIS_HOST: str

    GOOGLE_CLIENT_ID: str

    class Config:
        env_file = ".env"


settings = Settings()
