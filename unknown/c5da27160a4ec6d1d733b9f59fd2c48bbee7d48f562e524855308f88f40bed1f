from contextlib import asynccontextmanager
import uvicorn
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.utils import get_openapi

from app.core.config import settings
from app.database.database import create_tables
from app.routes import agents, chat
from app.utils.logger import get_logger
from app.agents.sample_agents import SAMPLE_AGENTS
from app.models.models import CustomAgent
from app.database.database import AsyncSessionLocal

logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting Deep Ellum Agent Service...")
    
    try:
        # Create database tables
        await create_tables()
        logger.info("Database tables created successfully")

        # Initialize sample agents in database
        await initialize_sample_agents()
        logger.info("Sample agents initialized")

    except Exception as e:
        logger.error(f"Error during startup: {e}")
        logger.warning("Service will start without database functionality")
    
    yield
    
    # Shutdown
    logger.info("Shutting down Deep Ellum Agent Service...")


async def initialize_sample_agents():
    """Initialize sample agents in the database."""
    try:
        async with AsyncSessionLocal() as db:
            from sqlalchemy import select

            # Check if sample agents already exist
            result = await db.execute(
                select(CustomAgent).where(CustomAgent.is_sample == True)
            )
            existing_samples = result.scalars().all()

            if existing_samples:
                logger.info("Sample agents already exist, skipping initialization")
                return

            # Create sample agents (these are global and not tied to any organization)
            for agent_type, agent_class in SAMPLE_AGENTS.items():
                agent_instance = agent_class()

                sample_agent = CustomAgent(
                    name=agent_instance.name,
                    description=agent_instance.description,
                    personality=agent_instance.personality,
                    instructions=agent_instance.instructions,
                    capabilities=[cap.value for cap in agent_instance.capabilities],
                    is_active=True,
                    is_sample=True,
                    created_by="system",
                    organization_id=None  # Sample agents are global, not organization-specific
                )

                db.add(sample_agent)

            await db.commit()
            logger.info(f"Created {len(SAMPLE_AGENTS)} global sample agents")

    except Exception as e:
        logger.error(f"Error initializing sample agents: {e}")
        # Don't raise the error to allow the service to start without database


# Create FastAPI app
app = FastAPI(
    title="Deep Ellum Custom AI Agent Service",
    description="A service for creating, managing, and chatting with custom AI agents using LangChain and LangGraph",
    version="1.0.0",
    openapi_url="/api/v1/agents/openapi.json",
    docs_url="/api/v1/agents/docs",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(agents.router, prefix="/api/v1/agents", tags=["agents"])
app.include_router(chat.router, prefix="/api/v1/chat", tags=["chat"])


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "service": settings.SERVICE_NAME,
        "version": "1.0.0"
    }


@app.get("/agent_status")
async def agent_status():
    """Service status endpoint."""
    return {
        "message": "Welcome to the Deep Ellum Custom AI Agent Service",
        "service": settings.SERVICE_NAME,
        "features": [
            "Custom AI agent creation",
            "LangChain and LangGraph integration",
            "Google Gemini AI",
            "Sample agents (Code Generator, Accessibility Advisor, Documentation Specialist)",
            "Conversation management",
            "Agent customization and testing"
        ]
    }


def custom_openapi():
    """Custom OpenAPI schema to include bearer token authentication."""
    if app.openapi_schema:
        return app.openapi_schema

    openapi_schema = get_openapi(
        title="Deep Ellum Custom AI Agent Service",
        version="1.0.0",
        description="A comprehensive service for creating and managing custom AI agents",
        routes=app.routes,
    )

    # Add Bearer token authentication scheme
    openapi_schema["components"]["securitySchemes"] = {
        "Bearer": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}
    }

    # Apply Bearer authentication to all endpoints
    for path in openapi_schema["paths"].values():
        for method in path.values():
            if "security" in method:
                method["security"].append({"Bearer": []})
            else:
                method["security"] = [{"Bearer": []}]

    # Add custom schema information
    openapi_schema["info"]["x-logo"] = {
        "url": "https://example.com/logo.png"
    }

    app.openapi_schema = openapi_schema
    return app.openapi_schema


app.openapi = custom_openapi


if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=settings.SERVICE_PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
