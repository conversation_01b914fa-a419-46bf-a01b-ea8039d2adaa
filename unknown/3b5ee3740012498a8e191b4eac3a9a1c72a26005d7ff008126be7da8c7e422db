from sqlalchemy import Column, String, Text, DateTime, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database.database import Base
import uuid


class CustomAgent(Base):
    """Model for storing custom AI agents."""
    __tablename__ = "custom_agents"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=False)
    personality = Column(Text, nullable=True)
    instructions = Column(Text, nullable=False)
    capabilities = Column(JSON, nullable=True)  # List of capabilities/tools
    is_active = Column(Boolean, default=True)
    is_sample = Column(Boolean, default=False)  # True for pre-built sample agents
    created_by = Column(String(255), nullable=False)  # User ID
    organization_id = Column(String(255), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    conversations = relationship("AgentConversation", back_populates="agent")


class AgentConversation(Base):
    """Model for storing conversations with agents."""
    __tablename__ = "agent_conversations"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    agent_id = Column(UUID(as_uuid=True), ForeignKey("custom_agents.id"), nullable=False)
    user_id = Column(String(255), nullable=False)
    title = Column(String(500), nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    agent = relationship("CustomAgent", back_populates="conversations")
    messages = relationship("ConversationMessage", back_populates="conversation")


class ConversationMessage(Base):
    """Model for storing individual messages in conversations."""
    __tablename__ = "conversation_messages"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    conversation_id = Column(UUID(as_uuid=True), ForeignKey("agent_conversations.id"), nullable=False)
    role = Column(String(50), nullable=False)  # 'user', 'assistant', 'system'
    content = Column(Text, nullable=False)
    message_metadata = Column(JSON, nullable=True)  # Additional message metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    conversation = relationship("AgentConversation", back_populates="messages")


class AgentTemplate(Base):
    """Model for storing agent templates that users can customize."""
    __tablename__ = "agent_templates"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=False)
    category = Column(String(100), nullable=False)  # e.g., 'development', 'accessibility', 'writing'
    template_instructions = Column(Text, nullable=False)
    default_capabilities = Column(JSON, nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
