from typing import Dict, Any, Optional, List
from uuid import UUID
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from app.models.models import CustomAgent
from app.models.schemas import AgentCapability
from app.agents.base_agent import LangChainAgent, LangGraphAgent
from app.agents.sample_agents import get_sample_agent, SAMPLE_AGENTS
from app.utils.logger import get_logger

logger = get_logger(__name__)


class AgentFactory:
    """Factory for creating and managing AI agents."""
    
    def __init__(self):
        self._agent_cache: Dict[str, Any] = {}
    
    async def create_agent_from_db(self, db: AsyncSession, agent_id: UUID) -> Optional[LangChainAgent]:
        """Create an agent instance from database record."""
        try:
            # Check cache first
            cache_key = str(agent_id)
            if cache_key in self._agent_cache:
                return self._agent_cache[cache_key]
            
            # Fetch agent from database
            result = await db.execute(
                select(CustomAgent).where(CustomAgent.id == agent_id)
            )
            agent_record = result.scalar_one_or_none()
            
            if not agent_record:
                logger.warning(f"Agent not found: {agent_id}")
                return None
            
            # Create agent instance
            agent = self._create_agent_instance(agent_record)
            
            # Cache the agent
            self._agent_cache[cache_key] = agent
            
            return agent
            
        except Exception as e:
            logger.error(f"Error creating agent from DB: {e}")
            return None
    
    def _create_agent_instance(self, agent_record: CustomAgent) -> LangChainAgent:
        """Create an agent instance from a database record."""
        # Convert capabilities from JSON to enum list
        capabilities = []
        if agent_record.capabilities:
            for cap in agent_record.capabilities:
                try:
                    capabilities.append(AgentCapability(cap))
                except ValueError:
                    logger.warning(f"Unknown capability: {cap}")
        
        # Determine agent type based on capabilities or use default
        if AgentCapability.CODE_GENERATION in capabilities:
            agent_class = LangGraphAgent  # Use LangGraph for complex code generation
        else:
            agent_class = LangChainAgent  # Use LangChain for simpler interactions
        
        # Create agent instance
        agent = agent_class(
            name=agent_record.name,
            description=agent_record.description,
            instructions=agent_record.instructions,
            personality=agent_record.personality,
            capabilities=capabilities
        )
        
        return agent
    
    def create_sample_agent(self, agent_type: str) -> Optional[Any]:
        """Create a sample agent by type."""
        try:
            return get_sample_agent(agent_type)
        except ValueError as e:
            logger.error(f"Error creating sample agent: {e}")
            return None
    
    async def get_agent(self, db: AsyncSession, agent_id: UUID) -> Optional[Any]:
        """Get an agent instance, creating it if necessary."""
        return await self.create_agent_from_db(db, agent_id)
    
    def clear_cache(self, agent_id: Optional[UUID] = None):
        """Clear agent cache."""
        if agent_id:
            cache_key = str(agent_id)
            self._agent_cache.pop(cache_key, None)
        else:
            self._agent_cache.clear()
    
    def get_sample_agents_info(self) -> Dict[str, Dict[str, Any]]:
        """Get information about all available sample agents."""
        from app.agents.sample_agents import list_sample_agents
        return list_sample_agents()
    
    async def create_custom_agent(
        self,
        name: str,
        description: str,
        instructions: str,
        personality: Optional[str] = None,
        capabilities: List[AgentCapability] = None
    ) -> LangChainAgent:
        """Create a custom agent instance without saving to database."""
        capabilities = capabilities or []
        
        # Determine agent type
        if AgentCapability.CODE_GENERATION in capabilities:
            agent_class = LangGraphAgent
        else:
            agent_class = LangChainAgent
        
        agent = agent_class(
            name=name,
            description=description,
            instructions=instructions,
            personality=personality,
            capabilities=capabilities
        )
        
        return agent
    
    async def test_agent(
        self,
        instructions: str,
        test_message: str,
        personality: Optional[str] = None,
        capabilities: List[AgentCapability] = None
    ) -> str:
        """Test an agent configuration with a sample message."""
        try:
            # Create temporary agent
            agent = await self.create_custom_agent(
                name="Test Agent",
                description="Temporary agent for testing",
                instructions=instructions,
                personality=personality,
                capabilities=capabilities
            )
            
            # Process test message
            response = await agent.process_message(test_message)
            return response
            
        except Exception as e:
            logger.error(f"Error testing agent: {e}")
            return f"Error testing agent: {str(e)}"


# Global factory instance
agent_factory = AgentFactory()
