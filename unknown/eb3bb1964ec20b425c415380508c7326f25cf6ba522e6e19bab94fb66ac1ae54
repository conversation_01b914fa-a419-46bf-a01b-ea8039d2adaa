# scrape_all_data_spider.py
import scrapy
from urllib.parse import urlparse


class ScrapeAllData<PERSON>pider(scrapy.Spider):
    name = 'scrape_all_data_spider'

    def __init__(self, urls=None, *args, **kwargs):
        super(<PERSON><PERSON>e<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__(*args, **kwargs)
        self.start_urls = urls if urls else []
        self.allowed_domains = [urlparse(url).netloc for url in self.start_urls]

    def parse(self, response):
        # Extract everything from the page
        page_data = {
            'url': response.url,
            'title': self.extract_title(response),
            'meta_tags': self.extract_meta_tags(response),
            'all_text': self.extract_all_text(response),
            'links': self.extract_links(response),
        }
        yield page_data

        # Follow links to scrape additional pages
        for link in response.css('a::attr(href)').getall():
            absolute_url = response.urljoin(link)
            if self.is_within_domain(absolute_url):
                yield scrapy.Request(absolute_url, callback=self.parse)

    def is_within_domain(self, url):
        domain = urlparse(url).netloc
        return domain in self.allowed_domains

    def extract_title(self, response):
        title = response.css('title::text').get() or response.xpath('//title/text()').get()
        return title.strip() if title else None

    def extract_meta_tags(self, response):
        # Extract all meta tags
        meta_tags = {}
        for meta in response.css('meta'):
            name = meta.css('::attr(name)').get() or meta.css('::attr(property)').get()
            content = meta.css('::attr(content)').get()
            if name and content:
                meta_tags[name.strip()] = content.strip()
        return meta_tags

    def extract_all_text(self, response):
        # Extract all visible text on the page
        texts = response.css('body *::text').getall()
        return ' '.join([text.strip() for text in texts if text.strip()])

    def extract_links(self, response):
        # Extract all links on the page
        links = response.css('a::attr(href)').getall()
        return [response.urljoin(link) for link in links]
