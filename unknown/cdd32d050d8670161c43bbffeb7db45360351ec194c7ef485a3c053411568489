from app.core.settings import settings
from app.utils.logger import get_logger
from sqlalchemy import create_engine
from sqlalchemy.orm import declarative_base, sessionmaker

logger = get_logger(__name__)

if settings.ENV == "TESTING":
    DATABASE_URI = settings.TESTING_DATABASE_URI
    logger.info(f"This is testing mode and this is the db uri {DATABASE_URI}")

elif settings.ENV == "DEVELOPMENT":
    DATABASE_URI = settings.LOCAL_DATABASE_URI
    logger.info(f"This is development mode and this is the db uri {DATABASE_URI}")

elif settings.ENV == "PRODUCTION":
    DATABASE_URI = settings.PROD_DATABASE_URI

engine = create_engine(
    DATABASE_URI,
    pool_pre_ping=True,
    pool_recycle=1800,
    pool_timeout=30,
    pool_size=10,
    max_overflow=20,
)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()
logger.debug(f"server is running well: {engine}")


# Dependency for getting a session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
