import asyncio
import os
from typing import List

from app.core.config import settings
from app.utils.logger import get_logger
from dotenv import load_dotenv
from huggingface_hub import InferenceClient
from openai import AsyncOpenAI

load_dotenv()

# openai client
client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)

# Create a global InferenceClient instance for the model.(Hugging Face)
client = InferenceClient(
    model="intfloat/e5-large-v2", token=os.getenv("HUGGINGFACE_TOKEN")
)

logger = get_logger(__name__)

# def get_embedding(text: str) -> list[float]:
#     """
#     Get an embedding vector for the input text using OllamaEmbeddings.
#     This function uses the Ollama model (e.g. "llama3") via LangChain's OllamaEmbeddings.

#     Parameters:
#         text (str): The text to embed.

#     Returns:
#         list[float]: The embedding vector.
#                    (On error, returns a zero vector of length 1536.)
#     """
#     try:
#         # Instantiate the embeddings object with the desired model.
#         # Replace "llama3" with your preferred model if needed.
#         embeddings = OllamaEmbeddings(model="llama3")

#         # Generate an embedding for the text.
#         # For a single text input, we use embed_query.
#         vector = embeddings.embed_query(text)
#         return vector
#     except Exception as e:
#         logger.error("Error generating embedding with OllamaEmbeddings: %s", e)
#         # Return a fallback zero vector of length 1536.
#         return [0.0] * 1536


async def get_embedding(text: str) -> List[float]:
    """
    Asynchronously get an embedding vector from Hugging Face's E5-large-v2 model.

    Parameters:
      - text: The input string for which the embedding is generated.

    Returns:
      - A list of floats representing the embedding.
      - On error, returns a zero vector with length 1536.
    """
    try:
        # Since client.feature_extraction is a blocking call, we run it in a separate thread.
        result = await asyncio.to_thread(client.feature_extraction, text)

        # The result is typically a NumPy array. Convert it to a list if possible.
        if hasattr(result, "tolist"):
            return result.tolist()
        else:
            return list(result)
    except Exception as e:
        logger.error("Error getting embedding: %s", e)
        return [0.0] * 1024  # Adjust VECTOR_DIM if necessary


async def example():
    embedding = await get_embedding("Hi, who are you?")
    print("Embedding vector shape:", len(embedding))


if __name__ == "__main__":
    import asyncio

    asyncio.run(example())
