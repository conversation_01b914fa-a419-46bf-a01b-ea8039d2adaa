# Settings and Payment Service

The Settings and Payment Service manages user settings, subscription plans, and payment processing in the EllumAI backend system.

## Features

- User settings management
- Subscription plan management
- Payment processing
- Notification handling
- Redis integration for caching

## API Endpoints

The service exposes the following endpoints:

- `/api/v1/settings` - Manage user settings
- `/api/v1/subscription` - Handle subscription plans and payments
- `/api/v1/notification` - Manage user notifications
- `/settings_status` - Check if the service is running
- `/redis-test` - Test Redis connectivity

Full API documentation is available at `/api/v1/settings/docs` when the service is running.

## Environment Variables

Create a `.env` file in the service directory with the following variables:

```
DATABASE_URL=postgresql://username:password@host:port/database
SECRET_KEY=your_secret_key
REDIS_HOST=redis
REDIS_PORT=6379
PAYMENT_API_KEY=your_payment_api_key
```

## Running the Service

### Without Docker

1. Navigate to the settings and payment service directory:
   ```bash
   cd microservices/settings_and_payment
   ```

2. Create a virtual environment:
   ```bash
   python -m venv venv
   ```

3. Activate the virtual environment:
   - On Windows: `venv\Scripts\activate`
   - On macOS/Linux: `source venv/bin/activate`

4. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

5. Initialize and run migrations:
   ```bash
   alembic revision --autogenerate -m "initial migrations"
   alembic upgrade head
   ```

6. Start the service:
   ```bash
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8005
   ```

### With Docker

1. Build and run the Docker container:
   ```bash
   docker build -t settings_and_payment .
   docker run -p 8005:8005 settings_and_payment
   ```

### Using Docker Compose

From the root of the project:
```bash
docker-compose up settings_and_payment
```

