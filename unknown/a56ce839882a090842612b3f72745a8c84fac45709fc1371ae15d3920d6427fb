import google.auth.transport.requests
import requests
from app.core.settings import settings
from app.utils.logger import get_logger
from google.oauth2 import service_account
import os

logger = get_logger(__name__)

BASE_DIR = os.path.dirname(os.path.abspath(__file__))

# TODO: Update this
json_file_path = os.path.join(
    BASE_DIR,
    "microservices/settings_and_payment/easyaza-firebase-adminsdk-23wad-3aeccfc43d.json",
)


class PushService:
    def __init__(self, firebase_project_id: str, json_file_path: str):
        self.firebase_project_id = firebase_project_id
        self.json_file_path = json_file_path

    def get_fcm_access_token(self) -> str:
        credentials = service_account.Credentials.from_service_account_file(
            self.json_file_path,
            scopes=["https://www.googleapis.com/auth/firebase.messaging"],
        )
        request = google.auth.transport.requests.Request()
        logger.info("Refreshing FCM access token")
        credentials.refresh(request)
        logger.info("FCM access token refreshed")
        return credentials.token

    def send_push(self, device_tokens: list[str], title: str, body: str):
        fcm_access_token = self.get_fcm_access_token()
        url = f"https://fcm.googleapis.com/v1/projects/{self.firebase_project_id}/messages:send"
        headers = {
            "Authorization": f"Bearer {fcm_access_token}",
            "Content-Type": "application/json",
        }

        responses = []
        logger.info(f"Sending push notification to {len(device_tokens)} devices")
        for token in device_tokens:
            payload = {
                "message": {
                    "token": token,
                    "notification": {"title": title, "body": body},
                }
            }
            try:
                response = requests.post(url, json=payload, headers=headers)
                response.raise_for_status()
                responses.append(
                    {"token": token, "status": "success", "response": response.json()}
                )
            except requests.exceptions.RequestException as e:
                logger.error(f"Push notification error for token {token}: {e}")
                responses.append({"token": token, "status": "failure", "error": str(e)})

        return responses


push_service = PushService(settings.PUSH_PROJECT_ID, settings.PUSH_KEY_FILE_PATH)
