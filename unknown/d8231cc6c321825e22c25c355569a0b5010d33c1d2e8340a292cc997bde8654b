"""
Permission definitions for the Deep Ellum Agent Service.

This module defines the permissions required for different operations
in the agent service, following the pattern used by other microservices.
"""

from enum import Enum
from typing import Dict, List


class AgentPermission(Enum):
    """Enumeration of agent-related permissions."""
    
    # Agent management permissions
    CREATE_AGENT = "create_agent"
    VIEW_AGENTS = "view_agents"
    UPDATE_AGENT = "update_agent"
    DELETE_AGENT = "delete_agent"
    
    # Chat and conversation permissions
    CHAT_WITH_AGENT = "chat_with_agent"
    VIEW_CONVERSATIONS = "view_conversations"
    DELETE_CONVERSATION = "delete_conversation"
    
    # Template permissions
    VIEW_TEMPLATES = "view_templates"
    CREATE_TEMPLATE = "create_template"
    UPDATE_TEMPLATE = "update_template"
    DELETE_TEMPLATE = "delete_template"
    
    # Testing permissions
    TEST_AGENT = "test_agent"


# Permission groups for different roles
PERMISSION_GROUPS: Dict[str, List[str]] = {
    "viewer": [
        AgentPermission.VIEW_AGENTS.value,
        AgentPermission.CHAT_WITH_AGENT.value,
        AgentPermission.VIEW_CONVERSATIONS.value,
        AgentPermission.VIEW_TEMPLATES.value,
    ],
    "editor": [
        AgentPermission.VIEW_AGENTS.value,
        AgentPermission.CREATE_AGENT.value,
        AgentPermission.UPDATE_AGENT.value,
        AgentPermission.CHAT_WITH_AGENT.value,
        AgentPermission.VIEW_CONVERSATIONS.value,
        AgentPermission.DELETE_CONVERSATION.value,
        AgentPermission.VIEW_TEMPLATES.value,
        AgentPermission.TEST_AGENT.value,
    ],
    "admin": [
        # Admins have all permissions
        AgentPermission.VIEW_AGENTS.value,
        AgentPermission.CREATE_AGENT.value,
        AgentPermission.UPDATE_AGENT.value,
        AgentPermission.DELETE_AGENT.value,
        AgentPermission.CHAT_WITH_AGENT.value,
        AgentPermission.VIEW_CONVERSATIONS.value,
        AgentPermission.DELETE_CONVERSATION.value,
        AgentPermission.VIEW_TEMPLATES.value,
        AgentPermission.CREATE_TEMPLATE.value,
        AgentPermission.UPDATE_TEMPLATE.value,
        AgentPermission.DELETE_TEMPLATE.value,
        AgentPermission.TEST_AGENT.value,
    ],
    "owner": [
        # Owners have all permissions (same as admin for this service)
        AgentPermission.VIEW_AGENTS.value,
        AgentPermission.CREATE_AGENT.value,
        AgentPermission.UPDATE_AGENT.value,
        AgentPermission.DELETE_AGENT.value,
        AgentPermission.CHAT_WITH_AGENT.value,
        AgentPermission.VIEW_CONVERSATIONS.value,
        AgentPermission.DELETE_CONVERSATION.value,
        AgentPermission.VIEW_TEMPLATES.value,
        AgentPermission.CREATE_TEMPLATE.value,
        AgentPermission.UPDATE_TEMPLATE.value,
        AgentPermission.DELETE_TEMPLATE.value,
        AgentPermission.TEST_AGENT.value,
    ]
}


def get_permissions_for_role(role: str) -> List[str]:
    """Get list of permissions for a given role."""
    return PERMISSION_GROUPS.get(role.lower(), [])


def has_permission(user_permissions: List[str], user_role: str, required_permission: str) -> bool:
    """Check if user has the required permission."""
    # Admins and owners have all permissions
    if user_role.lower() in ["admin", "owner"]:
        return True
    
    # Check if user has the specific permission
    return required_permission in user_permissions
