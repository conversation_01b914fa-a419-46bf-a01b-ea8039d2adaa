# Chat Service

The Chat Service manages chat interactions, conversations, and file handling for AI interactions in the EllumAI backend system.

## Features

- Chat conversation management
- File upload and storage using MinIO
- Integration with AI models for chat responses
- Background task processing with Celery

## API Endpoints

The service exposes the following endpoints:

- `/api/v1/chat/conversation` - Create and manage chat conversations
- `/api/v1/chat/message` - Send and receive chat messages
- `/api/v1/files/upload` - Upload files for chat context
- `/chat_status` - Check if the service is running

Full API documentation is available at `/api/v1/chat/docs` when the service is running.

## Environment Variables

Create a `.env` file in the service directory with the following variables:

```
DATABASE_URL=postgresql://username:password@host:port/database
SECRET_KEY=your_secret_key
MINIO_ENDPOINT=minio:9000
MINIO_ACCESS_KEY=your_minio_access_key
MINIO_SECRET_KEY=your_minio_secret_key
MINIO_BUCKET=chat-files
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0
```

## Running the Service

### Without Docker

1. Navigate to the chat service directory:
   ```bash
   cd microservices/chat_service
   ```

2. Create a virtual environment:
   ```bash
   python -m venv venv
   ```

3. Activate the virtual environment:
   - On Windows: `venv\Scripts\activate`
   - On macOS/Linux: `source venv/bin/activate`

4. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

5. Initialize and run migrations:
   ```bash
   alembic revision --autogenerate -m "initial migrations"
   alembic upgrade head
   ```

6. Start the service:
   ```bash
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

7. In a separate terminal, start the Celery worker:
   ```bash
   celery -A app.worker worker --loglevel=info
   ```

### With Docker

1. Build and run the Docker container:
   ```bash
   docker build -t chat_service .
   docker run -p 8000:8000 chat_service
   ```

### Using Docker Compose

From the root of the project:
```bash
docker-compose up chat_service
```

