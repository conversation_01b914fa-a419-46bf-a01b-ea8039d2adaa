import redis.asyncio as redis
from app.core.settings import settings
from fastapi import WebSocket


async def notification_subscriber(
    user_id: str, organization_id: str, websocket: WebSocket
):
    redis_client = redis.Redis(
        host=settings.REDIS_HOST, port=6379, decode_responses=True
    )
    pubsub = redis_client.pubsub()
    await pubsub.subscribe(f"user:{user_id}:notifications:{organization_id}")

    try:
        while True:
            message = await pubsub.get_message(ignore_subscribe_messages=True)
            if message:
                await websocket.send_json({"notification": message["data"]})
    finally:
        await pubsub.close()
        await redis_client.close()
