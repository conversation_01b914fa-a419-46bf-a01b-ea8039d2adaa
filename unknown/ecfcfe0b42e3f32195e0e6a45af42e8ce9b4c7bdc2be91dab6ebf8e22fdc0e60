import secrets
import string
from datetime import datetime, timedelta, timezone

import bcrypt
from app.core.settings import settings
from app.models.model import User
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, status
from itsdangerous import URLSafeTimedSerializer
from jose import ExpiredSignatureError, J<PERSON><PERSON><PERSON><PERSON>, jwt
from sqlalchemy.orm import Session

SECRET_KEY = settings.SECRET_KEY
ALGORITHM = settings.ALGORITHM
ACCESS_TOKEN_EXPIRES_MINUTES = settings.ACCESS_TOKEN_EXPIRES_MINUTES
JWT_REFRESH_TOKEN_EXPIRES = settings.JWT_REFRESH_TOKEN_EXPIRES


def verify_password(plain_password, hashed_password) -> bool:
    """
    Verify a plain-text password against a hashed password.

    Parameters:
    - plain_password: The plain-text password
    - hashed_password: The hashed password

    Returns:
    - True if passwords match, else False
    """
    return bcrypt.checkpw(
        plain_password.encode("utf-8"), hashed_password.encode("utf-8")
    )


def get_password_hash(password) -> str:
    """
    Hash a password using bcrypt.

    Parameters:
    - password: The plain-text password to hash

    Returns:
    - The hashed password
    """
    return bcrypt.hashpw(password.encode("utf-8"), bcrypt.gensalt()).decode("utf-8")


def generate_temporary_password(length=12):
    """Generate a secure temporary password."""
    alphabet = string.ascii_letters + string.digits  # + string.punctuation
    password = "".join(secrets.choice(alphabet) for _ in range(length))
    return password


# create an access token for the user
def create_access_token(user_id: str, db: Session) -> str:
    """
    Create a JWT access token.

    Parameters:
    - user_id: Payload data for the token

    Returns:
    - The generated JWT token
    """
    user = db.query(User).filter(User.id == user_id).first()
    email = user.email
    user_details = {
        "id": user_id,
        "email": user.email,
        "first_name": user.first_name,
        "last_name": user.last_name,
        "is_active": user.is_active,
        "is_verified": user.is_verified,
        "status": user.status,
        "is_onboarded": user.is_onboarded,
    }

    to_encode = {
        "user_id": user_id,
        "email": email,
        "user_details": user_details,
        # "permissions": [permission.title for permission in user.permissions],
        "type": "access",
    }
    expire = datetime.now(timezone.utc) + timedelta(
        minutes=ACCESS_TOKEN_EXPIRES_MINUTES
    )
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, ALGORITHM)
    return encoded_jwt


# create a refersh token for the user
def create_refresh_token(user_id: str):
    """Creates a refresh token"""
    to_encode = {
        "user_id": user_id,
        # "permissions": [permission.title for permission in user.permissions],
        "type": "refresh",
    }
    expire = datetime.now(timezone.utc) + timedelta(days=JWT_REFRESH_TOKEN_EXPIRES)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


def decode_token(token: str) -> str:
    """
    Decode a JWT token.

    Parameters:
    - token: The JWT token to decode

    Returns:
    - The decoded token data
    """
    try:
        # 1. decode the token sent
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])

        # 2. retrieve the user id from the decoded token
        user_id: str = payload.get("user_id")
        token_type: str = payload.get("type")
        if not user_id or token_type != "access":
            raise ValueError("Invalid token: must be an access token")
        return user_id
    # 3. handle exceptions
    except ExpiredSignatureError:
        raise ValueError("Token has expired")
    except JWTError as e:
        raise ValueError(f"Invalid token: {str(e)}")
    except Exception as e:
        raise ValueError(f"An unexpected error occurred: {str(e)}")


def create_reset_token():
    return str(secrets.choice(range(1000, 9999)))


def create_verification_token(email: str) -> str:
    """Creates a urlsafe token to verify user"""
    try:
        serializer = URLSafeTimedSerializer(SECRET_KEY)
        token = serializer.dumps(email, salt=settings.SECURITY_SALT)
        return token
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"AN error occurred: {str(e)}",
        )


def verify_verification_token(token: str, max_age: int = 3600):
    """verify the token sent back"""
    try:
        serializer = URLSafeTimedSerializer(SECRET_KEY)
        email = serializer.loads(token, salt=settings.SECURITY_SALT)
        return email
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid token"
        )
