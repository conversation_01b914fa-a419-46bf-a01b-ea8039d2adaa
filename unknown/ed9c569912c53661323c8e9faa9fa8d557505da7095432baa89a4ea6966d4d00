from pydantic import BaseModel, <PERSON>, ConfigDict
from typing import Optional, List, Dict, Any
from datetime import datetime
from uuid import UUID
from enum import Enum


class MessageRole(str, Enum):
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"


class AgentCapability(str, Enum):
    CODE_GENERATION = "code_generation"
    CODE_REVIEW = "code_review"
    ACCESSIBILITY_AUDIT = "accessibility_audit"
    DOCUMENTATION = "documentation"
    TESTING = "testing"
    DEBUGGING = "debugging"
    WEB_SEARCH = "web_search"
    FILE_ANALYSIS = "file_analysis"


# Base schemas
class CustomAgentBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    description: str = Field(..., min_length=1)
    personality: Optional[str] = None
    instructions: str = Field(..., min_length=1)
    capabilities: Optional[List[AgentCapability]] = []
    is_active: bool = True


class CustomAgentCreate(CustomAgentBase):
    pass


class CustomAgentUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, min_length=1)
    personality: Optional[str] = None
    instructions: Optional[str] = Field(None, min_length=1)
    capabilities: Optional[List[AgentCapability]] = None
    is_active: Optional[bool] = None


class CustomAgentResponse(CustomAgentBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    is_sample: bool
    created_by: str
    organization_id: Optional[str]
    created_at: datetime
    updated_at: Optional[datetime]


# Conversation schemas
class ConversationMessageBase(BaseModel):
    role: MessageRole
    content: str
    message_metadata: Optional[Dict[str, Any]] = None


class ConversationMessageCreate(ConversationMessageBase):
    pass


class ConversationMessageResponse(ConversationMessageBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    conversation_id: UUID
    created_at: datetime


class AgentConversationBase(BaseModel):
    title: Optional[str] = None
    is_active: bool = True


class AgentConversationCreate(AgentConversationBase):
    agent_id: UUID


class AgentConversationListItem(AgentConversationBase):
    """Schema for conversation list items (without messages)."""
    model_config = ConfigDict(from_attributes=True)

    id: UUID
    agent_id: UUID
    user_id: str
    created_at: datetime
    updated_at: Optional[datetime]


class AgentConversationResponse(AgentConversationBase):
    model_config = ConfigDict(from_attributes=True)

    id: UUID
    agent_id: UUID
    user_id: str
    created_at: datetime
    updated_at: Optional[datetime]
    messages: List[ConversationMessageResponse] = []


# Chat schemas
class ChatRequest(BaseModel):
    message: str = Field(..., min_length=1)
    conversation_id: Optional[UUID] = None
    agent_id: UUID


class ChatResponse(BaseModel):
    message: str
    conversation_id: UUID
    agent_name: str
    response_metadata: Optional[Dict[str, Any]] = None


# Agent Template schemas
class AgentTemplateBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    description: str = Field(..., min_length=1)
    category: str = Field(..., min_length=1, max_length=100)
    template_instructions: str = Field(..., min_length=1)
    default_capabilities: Optional[List[AgentCapability]] = []


class AgentTemplateResponse(AgentTemplateBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime]


# List responses
class AgentListResponse(BaseModel):
    agents: List[CustomAgentResponse]
    total: int
    page: int
    size: int


class ConversationListResponse(BaseModel):
    conversations: List[AgentConversationListItem]
    total: int
    page: int
    size: int
