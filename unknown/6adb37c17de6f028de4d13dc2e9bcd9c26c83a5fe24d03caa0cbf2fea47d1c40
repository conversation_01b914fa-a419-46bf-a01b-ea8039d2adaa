from sqlalchemy import Column, Foreign<PERSON><PERSON>, String, Integer, DateTime, Text, func
from sqlalchemy.orm import declarative_base, relationship
import uuid
import time
from datetime import datetime


Base = declarative_base()


class FileUpload(Base):
    __tablename__ = "file_uploads"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    filename = Column(String)
    filesize = Column(Integer)
    filetype = Column(String)
    user_id = Column(String)
    organisation_id = Column(String)
    username = Column(String)  # change to upload_by
    summary = Column(Text)

    # Make sure the upload_date is UTC timezone-aware
    upload_date = Column(DateTime, default=func.now(), nullable=False, index=True)

    # Add a Unix timestamp field (seconds since epoch)
    timestamp = Column(Integer, default=lambda: int(time.time()), nullable=False)


class ChatThread(Base):
    __tablename__ = "chat_threads"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    title = Column(String)
    user_id = Column(String)
    organisation_id = Column(String)
    chat_histories = relationship("ChatHistory", back_populates="chat_thread")
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())


class ChatHistory(Base):
    __tablename__ = "chat_history"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    thread_id = Column(String, ForeignKey("chat_threads.id"))
    user_message = Column(Text)
    bot_response = Column(Text)
    chat_thread = relationship("ChatThread", back_populates="chat_histories")
    organisation_id = Column(String)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())


class SavedContent(Base):
    __tablename__ = "saved_content"

    id = Column(Integer, primary_key=True, index=True)
    organisation_id = Column(String, index=True, nullable=False)
    user_id = Column(String, nullable=False)
    platform = Column(String, nullable=False)
    content_text = Column(Text, nullable=False)
    additional_info = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.now, nullable=False)
    updated_at = Column(
        DateTime, default=datetime.now, onupdate=datetime.now, nullable=False
    )
