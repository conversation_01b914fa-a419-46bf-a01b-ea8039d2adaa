#!/bin/bash
# Script to update social media metrics for testing purposes

# Change to the socials_service directory
cd "$(dirname "$0")"

# Check if Python is available
if ! command -v python &> /dev/null; then
    echo "Python is not installed or not in PATH"
    exit 1
fi

# Check if virtual environment exists and activate it if it does
if [ -d "venv" ]; then
    echo "Activating virtual environment..."
    source venv/bin/activate
fi

# Run the update script
echo "Running social media metrics update..."
python update_all_socials.py "$@"

# Deactivate virtual environment if it was activated
if [ -n "$VIRTUAL_ENV" ]; then
    deactivate
fi

echo "Update process completed"
