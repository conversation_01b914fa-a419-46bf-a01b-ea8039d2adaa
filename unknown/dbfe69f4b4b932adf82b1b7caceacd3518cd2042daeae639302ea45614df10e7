import csv
import mimetypes
import os
import tempfile
from functools import lru_cache
from typing import Annotated

import docx
import httpx
from app.utils.external_calls import fetch_user_permissions
import moviepy.editor as mp
import pandas as pd
import PyPDF2
import pytesseract
import speech_recognition as sr
import structlog
from app.models import Chat<PERSON><PERSON><PERSON>
from bs4 import BeautifulSoup
from dotenv import load_dotenv
from fastapi import Depends, HTTPException
from fastapi.security import OAuth2PasswordBearer
from jose import JWTError, jwt
from PIL import Image, ImageFile
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

structlog.configure(processors=[structlog.processors.JSONRenderer()])
logger = structlog.get_logger()
AUTH_SERVICE_URL = os.getenv(
    "AUTH_SERVICE_URL"
)


oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{AUTH_SERVICE_URL}/login")

load_dotenv()
ALGORITHM = os.getenv("ALGORITHM")
SECRET = os.getenv("SECRET_KEY")

ImageFile.LOAD_TRUNCATED_IMAGES = True

class TokenData(BaseModel):
    username: str | None = None


def get_current_user(token: Annotated[str, Depends(oauth2_scheme)]):
    credentials_exception = HTTPException(
        status_code=401,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET, algorithms=[ALGORITHM])
        print(f"payload: {payload}")
        user_id: str = payload.get("user_id")
        if user_id is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    return {"decoded": payload, "raw_token": token}


async def check_permissions(user_id, organisation_id, required_permission):
    try:
        # fetch the user permissions
        user_permissions_dict = await fetch_user_permissions(user_id, organisation_id)
        user_permissions = user_permissions_dict.get("permissions", [])
        user_role = user_permissions_dict.get("role", "")
        if user_role != "admin" and required_permission not in user_permissions:
            raise HTTPException(
                status_code=403,
                detail=f"User does not have permission to access this endpoint: {required_permission}"
            )
        return user_role
    except Exception as e:
        logger.error(f"Error fetching user permissions: {e}")
        raise HTTPException(status_code=500, detail="Error fetching user permissions")


async def extract_text_from_file(file, file_metadata) -> str:
    """
    Extracts text from a file based on its MIME type.
    Supports PDF, DOCX, TXT, CSV, images, web links, and videos.

    """
    logger.info(f"START: Extracting text from file: {file_metadata['filename']}")
    file_type, _ = mimetypes.guess_type(file_metadata["filename"])

    logger.info(
        f"""File name: {file_metadata['filename']},
        Detected file type: {file_type},
        Content type: {file_metadata['content_type']}"""
    )

    if not file_type:
        # Try to use the file content type as a fallback
        file_type = file_metadata["content_type"]
    file.seek(0)
    if file_type == "application/pdf":
        return await extract_text_from_pdf(file)
    elif (
        file_type
        == "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    ):
        return await extract_text_from_docx(file)
    elif file_type == "text/plain":
        return await extract_text_from_txt(file)
    elif file_type == "text/csv":
        return await extract_text_from_csv(file)
    elif (
        file_type == "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    ):
        return await extract_text_from_excel(file)
    elif file_type and file_type.startswith("image/"):
        return await extract_text_from_image(file)
    elif file_type.startswith("video/"):
        return await extract_text_from_video(file)
    elif file_type == "text/html":
        # TODO: this implementation is not correct for a link.
        # it's correct for an html page. so scraping should be used here
        return await extract_text_from_link(file)
    else:
        raise ValueError(f"Unsupported file type: {file_type}")


async def extract_text_from_pdf(file) -> str:
    """
    Extract text from a PDF file.
    """
    text = ""
    try:
        file.seek(0)
        reader = PyPDF2.PdfReader(file)
        for page_num in range(len(reader.pages)):
            page = reader.pages[page_num]
            extracted_text = page.extract_text() or ""
            text += extracted_text + "\n"
    except Exception as e:
        logger.error(f"Error reading PDF file: {e}")
        text = "Error extracting text from PDF"
    return text


async def extract_text_from_docx(file) -> str:
    """
    Extract text from a DOCX file.
    """
    text = ""
    try:
        file.seek(0)
        doc = docx.Document(file)
        for paragraph in doc.paragraphs:
            text += paragraph.text + "\n"
    except Exception as e:
        logger.error(f"Error reading DOCX file: {e}")
        text = "Error extracting text from DOCX"
    return text


async def extract_text_from_txt(file) -> str:
    """
    Extract text from a plain text file.
    """
    text = ""
    try:
        # Reset the file pointer to the beginning
        file.seek(0)

        # Read and decode the text file content
        text = file.read().decode("utf-8")

    except UnicodeDecodeError:
        logger.error("Error decoding TXT file. Ensure the file is in UTF-8 format.")
        text = "Error: Unable to decode TXT file. Ensure the file is in UTF-8 format."

    except Exception as e:
        logger.error(f"Error reading TXT file: {e}")
        text = "Error extracting text from TXT"

    return text


async def extract_text_from_csv(file) -> str:
    """
    Extract text from a CSV file.
    """
    text = ""
    try:
        file.seek(0)
        # Decode the file content
        decoded_file = file.read().decode("utf-8").splitlines()

        # Use csv.reader to parse the content
        reader = csv.reader(decoded_file)

        for row in reader:
            text += ", ".join(row) + "\n"
    except UnicodeDecodeError:
        logger.error("Error decoding CSV file. Ensure the file is in UTF-8 format.")
        text = "Error: Unable to decode CSV file. Ensure the file is in UTF-8 format."

    except Exception as e:
        logger.error(f"Error reading CSV file: {e}")
        text = "Error extracting text from CSV"

    return text


async def extract_text_from_image(file) -> str:
    """
    Extract text from an image file using pytesseract.
    """
    try:
        # Load the image
        file.seek(0)
        image = Image.open(file)

        # Perform OCR using pytesseract
        logger.info("Starting to extract text from image")

        text = pytesseract.image_to_string(image)
        return text

    except Image.UnidentifiedImageError:
        logger.error("Unrecognized or corrupted image file.")
        raise HTTPException(
            status_code=400, detail="Unrecognized or corrupted image file."
        )
    except Exception as e:
        logger.error(f"Error reading image file: {e}")
        raise HTTPException(status_code=500, detail="Error extracting text from image.")


async def extract_text_from_link(url_bytes) -> str:
    """
    Extract text from a web page using a URL (link).
    """
    text = ""
    try:
        # Decode the BytesIO content to get the URL
        url = url_bytes.getvalue().decode("utf-8")

        async with httpx.AsyncClient() as client:
            response = await client.get(url)
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, "html.parser")
            text = soup.get_text(separator=" ", strip=True)
        else:
            logger.error(
                f"Failed to retrieve the webpage. Status code: {response.status_code}"
            )
            text = "Failed to retrieve the webpage."
    except httpx.HTTPStatusError as http_err:
        logger.error(f"HTTP error occurred: {http_err}")
        text = "Error extracting text from URL"
    except Exception as e:
        logger.error(f"Error fetching webpage: {e}")
        text = "Error extracting text from URL"
    return text


async def extract_text_from_excel(file) -> str:
    """
    Extract text from an Excel file (.xlsx or .xls).
    """
    text = ""
    try:
        file.seek(0)
        df = pd.read_excel(file)

        text = df.to_string(index=False)

    except Exception as e:
        logger.error(f"Error reading Excel file: {e}")
        text = "Error extracting text from Excel file"

    return text


async def extract_text_from_video(file) -> str:
    """
    Extract text (speech) from a video file by converting
      it to audio and then performing speech-to-text.
    """
    text = ""
    try:
        # Create a temporary file for the video
        with tempfile.NamedTemporaryFile(
            delete=False, suffix=".mp4"
        ) as temp_video_file:
            # Write the BytesIO content to the temporary file
            temp_video_file.write(file.getvalue())
            temp_video_path = temp_video_file.name

        # Extract audio from the video
        video_clip = mp.VideoFileClip(temp_video_path)
        audio_path = "temp_audio.wav"
        video_clip.audio.write_audiofile(audio_path)
        video_clip.close()

        # Initialize recognizer and extract text
        recognizer = sr.Recognizer()
        with sr.AudioFile(audio_path) as source:
            audio_data = recognizer.record(source)
            text = recognizer.recognize_google(audio_data)

        # Clean up temporary audio file
        os.remove(audio_path)
        os.remove(temp_video_path)

    except FileNotFoundError as fnf_error:
        logger.error(f"File not found: {fnf_error}")
        text = "Error: Required file not found."
    except Exception as e:
        logger.error(f"Error extracting text from video: {e}")
        text = "Error extracting text from video."

    logger.info(text)
    return text


async def save_chat_history(
    thread_id: str,
    user_message: str,
    bot_response: str,
    organisation_id: str,
    session: AsyncSession,
):
    chat_history = ChatHistory(
        thread_id=thread_id,
        user_message=user_message,
        bot_response=bot_response,
        organisation_id=organisation_id,
    )
    session.add(chat_history)
    await session.commit()


# TODO: What happens if the chat history is very long?
# that would take time, so pagination can be introduced here
@lru_cache(maxsize=128)
async def get_chat_history(thread_id: str, organisation_id: str, session: AsyncSession):
    # searching for only chat histories that are for the thread and in the organisation.
    result = await session.execute(
        select(ChatHistory)
        .where(
            ChatHistory.thread_id == thread_id,
            ChatHistory.organisation_id == organisation_id,
        )
        .order_by(ChatHistory.created_at.asc())
    )
    chat_history_records = result.scalars().all()
    return chat_history_records
