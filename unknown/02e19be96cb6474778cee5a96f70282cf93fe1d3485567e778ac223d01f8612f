from app.core.config import settings
from facebook_business.adobjects.adaccount import AdAccount
from facebook_business.api import FacebookAdsApi

my_app_id = settings.FACEBOOK_CLIENT_ID
my_app_secret = settings.FACEBOOK_CLIENT_SECRET
my_access_token = "EAAH4ojWRVDsBO1T3DfZAjdf6Gn1JM68MHI8ATJuCDUdEeZBSWcwoTvnksHSkrFZBfQmXrUm4VWE9cWeciTzwGgalnCsYpU8Yc62L7cQDw8BoCxO6yFrryTkayZCk7dmEobMNQ0XV4mqhbMWwwnDVNrNup7u5u0wB7armRpSAmi0UlAaNBfZBopogGJiqcG3vpNwZDZD"
FacebookAdsApi.init(my_app_id, my_app_secret, my_access_token)
my_account = AdAccount(f"act_{settings.FACEBOOK_AD_ID}")
campaigns = my_account.get_campaigns()
print(campaigns)
