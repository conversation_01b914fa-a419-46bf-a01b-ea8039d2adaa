from app.core.config import settings
from celery import Celery

# Retrieve broker and backend URLs from environment variables.
broker_url = settings.CELERY_BROKER_URL
result_backend = settings.CELERY_RESULT_BACKEND

# Create the Celery application.
# The "include" option ensures that Celery automatically imports the specified modules,
# registering any tasks found in those modules.
celery_app = Celery(
    "fastbot", broker=broker_url, backend=result_backend, include=["app.utils.tasks"]
)


# Optional: Update Celery configuration.
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],  # Only accept JSON content.
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    broker_connection_retry_on_startup=True,
)
if __name__ == "__main__":
    # For debugging purposes, print out configuration information.
    print("Broker URL:", broker_url)
    print("Result Backend:", result_backend)
