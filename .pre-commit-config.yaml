repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
    -   id: trailing-whitespace
    -   id: end-of-file-fixer
    -   id: check-yaml
    -   id: check-added-large-files

-   repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.0.274
    hooks:
    -   id: ruff
        name: ruff (with import sorting)
        description: "Run Ruff with --fix to sort imports and apply all enabled fixes"
        exclude: ^alembic/
        stages: [pre-commit]
        args: [--ignore, "E501", --fix]
