name: CI/CD for Multi-Service Application

on:
  push:
    branches:
      - auth
  pull_request:
    branches:
      - main
      - auth

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        service:
          - authentication_service
          - settings_and_payment
          - socials_service
          - fastbot
    steps:
      # Step 1: Checkout the repository
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      # Step 2: Log in to Docker Hub
      - name: Log in to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_PASSWORD }}

      # Step 3: Build and push Docker images
      - name: Build and push Docker image for ${{ matrix.service }}
        uses: docker/build-push-action@v5
        with:
          context: ./microservices/${{ matrix.service }}
          push: true
          tags: ${{ secrets.DOCKERHUB_USERNAME }}/${{ matrix.service }}:latest
          cache-from: type=gha
          cache-to: type=gha,mode=max

  deploy-and-test:
    needs: build-and-push
    runs-on: ubuntu-latest

    steps:
      # Step 1: Checkout the repository
      - name: Checkout code
        uses: actions/checkout@v4

      # Step 2: Create .env files for services
      - name: Create .env files
        run: |
          echo "${{ secrets.FASTBOT_SERVICE_ENV }}" > ./microservices/fastbot/.env
          echo "${{ secrets.AUTH_SERVICE_ENV }}" > ./microservices/authentication_service/.env
          echo "${{ secrets.SETTINGS_AND_PAYMENT_ENV }}" > ./microservices/settings_and_payment/.env
          echo "${{ secrets.SOCIALS_SERVICE_ENV }}" > ./microservices/socials_service/.env

      # Step 3: Pull and run services using Docker Compose
      - name: Local Deployment Test
        run: |
          docker compose pull
          docker compose up -d

          # Wait for services to start
          sleep 30

          # Health check with timeout
          timeout 60s bash -c '
            while ! curl -f http://localhost:7777/auth_status &&
                   ! curl -f http://localhost:8001/health &&
                   ! curl -f http://localhost:8005/settings_status &&
                   ! curl -f http://localhost:8006/socials_status; do
              sleep 5;
            done
          ' || (docker compose logs && exit 1)

      # Step 6: Deploy to EC2 instance
      # - name: Deploy to EC2
      #   uses: fifsky/ssh-action@master
      #   with:
      #     command: |
      #       # Pull latest Docker images
      #       docker login -u $ {{ secrets.DOCKERHUB_USERNAME }} -p $ {{ secrets.DOCKERHUB_PASSWORD }}

      #       # Pull and update services
      #       docker compose pull
      #       docker compose down
      #       docker compose up -d

      #       # Cleanup old images
      #       docker image prune -f

      #       # Optional: Verify services are running
      #       retries=5
      #       for i in $(seq 1 $retries); do
      #         curl -f http://localhost:8000/chat_status && curl -f http://localhost:7777/auth_status && curl -f http://localhost:8005/settings_status && curl -f http://localhost:8006/socials_status && break || sleep 10
      #       done || exit 1
      #     host: $ {{ secrets.EC2_PUBLIC_IP }}
      #     user: ec2-user
      #     key: $ {{ secrets.EC2_PRIVATE_KEY }}
      - name: Stop Docker Compose
        if: always()
        run: docker compose down -v
